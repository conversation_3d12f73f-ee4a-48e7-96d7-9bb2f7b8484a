2025-08-01 17:40:45.078 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-01 17:40:45.338 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 32064 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-01 17:40:45.339 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "dev"
2025-08-01 17:40:46.673 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 17:40:46.675 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-01 17:40:46.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 109 ms. Found 2 LDAP repository interfaces.
2025-08-01 17:40:46.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 17:40:46.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-01 17:40:46.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-01 17:40:46.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-01 17:40:46.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 MongoDB repository interfaces.
2025-08-01 17:40:46.854 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 17:40:46.856 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 17:40:46.871 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-01 17:40:46.872 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-01 17:40:46.872 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-08-01 17:40:47.515 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 17:40:47.530 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 17:40:47.556 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 17:40:47.982 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 80 (http)
2025-08-01 17:40:48.006 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-80"]
2025-08-01 17:40:48.006 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:40:48.006 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-01 17:40:48.139 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:40:48.139 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2723 ms
2025-08-01 17:40:48.740 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-01 17:40:48.741 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-01 17:40:48.742 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-01 17:40:50.275 [main] INFO  org.springframework.ldap.core.support.AbstractContextSource - Property 'userDn' not set - anonymous context will be used for read-write operations
2025-08-01 17:40:50.583 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[localhost:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-01 17:40:51.377 [cluster-rtt-ClusterId{value='688c8ba2e281c4247388f003', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:871}] to localhost:27017
2025-08-01 17:40:51.377 [cluster-ClusterId{value='688c8ba2e281c4247388f003', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:872}] to localhost:27017
2025-08-01 17:40:51.378 [cluster-ClusterId{value='688c8ba2e281c4247388f003', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=478201458}
2025-08-01 17:40:53.308 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='file:/opt/sso_server/web/static/'] with []
2025-08-01 17:40:53.384 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-01 17:40:53.404 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-01 17:40:53.430 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-01 17:40:55.577 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-01 17:40:55.598 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-01 17:42:25.516 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-01 17:42:25.767 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 32415 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-01 17:42:25.768 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "loc"
2025-08-01 17:42:27.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 17:42:27.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-01 17:42:27.242 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 112 ms. Found 2 LDAP repository interfaces.
2025-08-01 17:42:27.267 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 17:42:27.268 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-01 17:42:27.277 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-01 17:42:27.277 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-01 17:42:27.277 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 MongoDB repository interfaces.
2025-08-01 17:42:27.291 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 17:42:27.293 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 17:42:27.308 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-01 17:42:27.308 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-01 17:42:27.308 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-08-01 17:42:27.954 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 17:42:27.969 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 17:42:27.996 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 17:42:28.429 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-08-01 17:42:28.454 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-08-01 17:42:28.454 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:42:28.455 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-01 17:42:28.597 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:42:28.597 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2756 ms
2025-08-01 17:42:29.211 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-01 17:42:29.212 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-01 17:42:29.212 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-01 17:42:30.986 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[127.0.0.1:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-01 17:42:31.801 [cluster-rtt-ClusterId{value='688c8c068daf2356189c2abd', description='null'}-127.0.0.1:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:874}] to 127.0.0.1:27017
2025-08-01 17:42:31.809 [cluster-ClusterId{value='688c8c068daf2356189c2abd', description='null'}-127.0.0.1:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:873}] to 127.0.0.1:27017
2025-08-01 17:42:31.810 [cluster-ClusterId{value='688c8c068daf2356189c2abd', description='null'}-127.0.0.1:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=127.0.0.1:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=494458000}
2025-08-01 17:42:33.633 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-01 17:42:33.704 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-01 17:42:33.722 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-01 17:42:33.746 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-01 17:42:36.117 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-01 17:42:36.179 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-01 17:42:36.181 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-01 17:42:36.183 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-01 17:42:36.417 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-01 17:42:36.432 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-01 17:42:36.436 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-01 17:42:36.436 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-01 17:42:36.436 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-01 17:42:36.436 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-01 17:42:36.436 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-01 17:42:36.436 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-01 17:42:36.437 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-01 17:42:36.442 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-01 17:42:36.442 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-01 17:42:36.538 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-08-01 17:42:36.571 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-08-01 17:42:37.199 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-08-01 17:42:37.224 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 12.425 seconds (JVM running for 18.598)
2025-08-01 17:42:37.241 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-01 17:42:39.695 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-08-01 17:42:40.244 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-01 17:42:51.348 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-08-01 17:42:53.617 [http-nio-8090-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 17:42:53.617 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 17:42:53.619 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-01 17:42:55.277 [http-nio-8090-exec-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:875}] to 127.0.0.1:27017
2025-08-01 17:43:02.355 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:876}] to 127.0.0.1:27017
2025-08-01 17:43:02.375 [http-nio-8090-exec-2] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:877}] to 127.0.0.1:27017
2025-08-01 17:43:02.395 [http-nio-8090-exec-4] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:879}] to 127.0.0.1:27017
2025-08-01 17:43:02.437 [http-nio-8090-exec-5] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:878}] to 127.0.0.1:27017
2025-08-01 17:43:04.636 [http-nio-8090-exec-5] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:880}] to 127.0.0.1:27017
2025-08-01 17:44:02.169 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-01 17:44:06.213 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-01 17:44:07.907 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
