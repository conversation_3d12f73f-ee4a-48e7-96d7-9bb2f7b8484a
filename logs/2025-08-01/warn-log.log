2025-08-01 17:40:49.612 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.dto.StatisticsDto".
2025-08-01 17:40:49.616 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:40:49.725 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.Resource",So @TableField annotation will not work!
2025-08-01 17:40:49.745 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResourceLink".
2025-08-01 17:40:49.745 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:40:49.757 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResource".
2025-08-01 17:40:49.760 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:40:49.892 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.SytPermissionAccount",So @TableField annotation will not work!
2025-08-01 17:40:49.947 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.SytSysOrganizationUser".
2025-08-01 17:40:49.949 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:40:51.546 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-01 17:40:53.307 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='file:/opt/sso_server/web/static/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-01 17:40:54.041 [main] WARN  org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: file:/opt/sso_server/web/templates/ (please add some templates or check your Thymeleaf configuration)
2025-08-01 17:40:54.054 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-01 17:40:55.413 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'enableRedisKeyspaceNotificationsInitializer' defined in class path resource [org/springframework/boot/autoconfigure/session/RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration.class]: Invocation of init method failed; nested exception is org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 127.0.0.1:6379
2025-08-01 17:42:30.100 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:42:30.216 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:42:30.229 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:42:30.376 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:42:31.882 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-01 17:42:33.633 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/static']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-01 17:42:34.344 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-01 17:42:37.240 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01 17:42:37.254 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
