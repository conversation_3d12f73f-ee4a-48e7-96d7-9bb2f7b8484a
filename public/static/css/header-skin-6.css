@charset "utf-8";
.wrapAll{
    background: url(../img/red.png) !important;
    background-size: 100% 100% !important; 
    /* background: linear-gradient(128.353617800267deg, rgba(39, 89, 147, 1) 9%, rgba(59, 151, 212, 1) 51%, rgba(53, 95, 153, 1) 87%); */
}
.center,.wrap{
  background: none !important;
}
.setUp{
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1) !important;
}
.newWrap,.mineServiceWrap,.systemWrap,.serviceWrap,.setUp,.selectWrap,.functionWrap,.tab,.functionLeft div,.search{
    background: rgba(0,0,0,0.2) !important;
}
.newWrap,.systemWrap,.serviceWrap,.banner,.mineServiceWrap,.centerBox,.threeLeftTop,.threeLeftBottom{
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1) !important;
}
.noneServiceWrap{
  box-shadow: none !important;
}
.tabClick{
    background: rgba(0,0,0,0.4) !important;
}
.tabClick p{
    background: none !important;
}
.search{
    border-color: rgba(0,0,0,0) !important;
}
/* .searchButton{
    background: rgba(0,0,0,0.4) !important;
} */
.noneServiceWrap{
    background: none !important;
}
.serviceTopTitle,.small-serviceTopTitle{
    color: #f8f8f8 !important;
}
.tabChange div{
    color: #f8f8f8 !important;
}
.newUl li a,.mineData,.mineDataTitle,.functionRightP,.serviceMore,.service,.month,.last .iconfont, .next .iconfont,.numberWrap,.setUp p,.numberWrap p,.selectWrap .title,.sortWrap p,.sort,.functionLeft p,.functionLeft div,.tab,.function,.footer,.footer1,.searchButton,.search,.messageIcon{
    color: rgba(255,255,255,0.8) !important;
}
.serviceMore .icon-font,.mineDesktopDataTitle,.newLiTime,.small-serviceMore .icon-font{
    color: rgba(255,255,255,0.5) !important;
}
.searchWrap .searchButton{
  display: none;
}
.sort:hover,.tab div:hover{
    background: rgba(0,0,0,0.2) !important;
}
.setUp:hover,.functionLeft div:hover{
    background: rgba(0,0,0,0.4) !important;
}

.tabChange div:hover{
    font-weight: bold;
    color: #fff;
    border-bottom: 2px solid #1890FF;
}
.tabChangeClick{
    font-weight: bold;
    color: #fff !important;
    border-bottom: 2px solid #1890FF;
}

.search::-webkit-input-placeholder{
    color: rgba(255,255,255,0.8) !important;
}
.search::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color: rgba(255,255,255,0.8) !important;
}
.search:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color: rgba(255,255,255,0.8) !important;
}
.search:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color: rgba(255,255,255,0.8) !important;
}
.wrap>.centerBox{
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2) !important;
}
.centerBox{
  background: #fff !important;
}
.ant-tabs-bar{
  color: rgba(255,255,255,0.7);
  border-bottom: 1px solid rgba(255,255,255,0) !important;
}
.setUp .iconfont,.setUp p{
    color: #FFF !important;
    opacity: 0.6;
}
/* .bl .newContent .system{
    background: rgba(0,0,0,0.2) !important;
    color: rgba(255,255,255,0.6) !important;
} */
.mineDesktopData:hover{
    background: rgba(0,0,0,0.2) !important;
}
.serviceTop{
    border-bottom: 1px solid rgba(255,255,255,0) !important;
}
.mineDesktopDataWrap{
    border-right: 1px solid rgba(255,255,255,0.2) !important;
}
.mineDesktopDataWrap:nth-of-type(3n+3){
    border: none !important;
}
/* .mineDesktopDataNumber{
    color: #fff !important;
} */
/* .tl .system{
    background: rgba(0,0,0,0.2) !important;
    color: rgba(255,255,255,0.6) !important;
}
.tl .system:hover,.bl .newContent .system:hover{
    background: rgba(0,0,0,0.6) !important;
} */
.tab1Click,.searchButton{
    background: rgba(0,0,0,0.4) !important;
}
.serviceNone{
    text-align: center;
    color: rgba(255,255,255,0.3) !important;;
}
.serviceNone img{
    opacity: 0.3 !important;
    width: 80px;
    margin-top: 50px;
    margin-bottom: 5px;
}


.desktop-center .ant-tabs-nav{
    background: rgba(0,0,0,0.2) !important;;
    border-radius: 3px !important;;
    height: 30px !important;;
    line-height: 30px !important;;
    margin-top: 15px !important;;
    overflow: hidden !important;
}
.desktop-center .ant-tabs-nav .ant-tabs-tab{
    padding: 0 20px !important;;
    margin: 0 !important;
    font-size: 14px !important;
}
.desktop-center .ant-tabs-nav .ant-tabs-tab-active{
    color: rgba(255,255,255,0.7) !important;
    background: rgba(0,0,0,0.3);
}
.desktop-center .ant-tabs-nav .ant-tabs-ink-bar{
    display: none !important;
}
.desktop-center .ant-tabs-nav .ant-tabs-tab:hover{
    color: rgba(255,255,255,0.7) !important;
    background: rgba(0,0,0,0.1) !important;
}
.desktop-center .serviceTopTitle{
    top: 14px !important;
}
.desktop-center .serviceMore{
    top: 16px !important;
}
.desktop-center .searchButton:hover{
    background: rgba(0,0,0,0.5) !important;
}
.desktop-center .small-serviceTopTitle{
    margin-top: 9px; !important;
}
.desktop-center .small-serviceMore{
    margin-top: 14px !important;
}




.topbar,.leftMenuButton,.rightMenuButton{
    background: #C02B22 !important;
}
.leftMenuButton,.rightMenuButton{
    border-color: #C02B22 !important;;
}
.searchWrap input{
    color: #f8f8f8 !important;
}



.desktopInfo{
    background: rgba(0,0,0,0.3) !important;
}
.threeLeftTop,.threeLeftBottom{
    background: rgba(0,0,0,0.2) !important;
}
.desktopThreeFunction span:nth-of-type(1),.threeLeftBottom,.threeLeftBottom p,.threeLeftBottom p span{
    color: rgba(255,255,255,0.7) !important;    
}
.desktopThreeFunction span:nth-of-type(1){
    color: #f8f8f8 !important; 
}
.threeLeftBottom p,.threeLeftBottom p span,.threeLeftBottom div{
    border-color: rgba(255,255,255,0.4) !important;
}

.threeLeftBottom p,.desktopThreeFunction,.threeLeftBottom div{
    border-color: rgba(255,255,255,0.1) !important;
}

.desktopThreeFunction .el-col-8>span>span{
    color: rgba(255,255,255,0.5) !important;
}

.serviceTop{
    height: 49px !important;
}

.el-drawer__wrapper .tabClick{
    background:rgba(255,255,255,0.2) !important;
}

.newUl li:hover,.desktopThreeFunction:hover,.threeLeftBottom div:hover{
    background: rgba(0,0,0,0.15) !important;
}
.threeLeftBottom p span {
    color: rgba(255, 255, 255, 0.6) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    background: rgba(255,255,255,0.1) !important;
}


.analyseHover{
    /* background: #C02B22 !important;
    border: 1px solid #C02B22 !important; */
    background: #F9C62D !important;
    border: 1px solid #F9C62D !important;
}
.analyseHover .analyseName{
    color: #fff !important;
}
.analyseHover .analyseDetial{
    color: #e8e8e8 !important;
}