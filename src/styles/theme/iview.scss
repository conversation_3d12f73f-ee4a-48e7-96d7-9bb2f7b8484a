.theme-iview {
  .avue-logo {
    background: url(../styles/image/img.png);
    background-size: cover;
    box-shadow: none;
    text-align: center;
    // .avue-logo_title{
    //   padding: 5px 8px 8px 8px;
    //   border-top-left-radius: 5px;
    //   border-top-right-radius: 5px;
    //   border-bottom-left-radius: 3px;
    //   border-bottom-right-radius: 3px;
    //   font-size: 20px;
    //   color:#fff;
    //   font-weight: 500;
    //   display: inline;
    //   background-color: #409EFF;
    // }
  }
  // .avue-tags{
  //   padding:  3px 5px 5px 0;
  //   background: #f0f0f0;
  //   box-shadow: inset 0 0 3px 2px hsla(0,0%,39.2%,.1);
  //   .is-active{
  //     &:before{
  //       background: #409EFF !important;
  //     }
  //   }
  //   .el-tabs__item{
  //     padding: 0 15px !important;
  //     position: relative;
  //     height: 32px !important;
  //     line-height:32px !important;
  //     border: 1px solid #e8eaec!important;
  //     color: #515a6e!important;
  //     background: #fff!important;
  //     border-radius: 3px;
  //     &:before{
  //       content:'';
  //       display: inline-block;
  //       width: 12px;
  //       height: 12px;
  //       margin-right:10px;
  //       border-radius: 50%;
  //       background: #e8eaec;
  //     }
  //   }
  // }

  .avue-contail {
    // background-image: url("/img/bg/star-squashed.jpg");
    // background-image: url("/static/img/grey.png");
    // background-size: 100% 100%;
  }
  .el-card,
  .error-page {
    background: #fff;
  }

  .avue-sidebar {
    background: #2E4150;
    .el-menu-item {
      // &.is-active {
      //     background-color:  #000c17;
      //     &:before {
      //       display: none;
      //     }
      //     i,span{
      //       color:#409EFF;
      //   }
      // }
    }
    .el-submenu {
      .el-menu-item {
        //   &.is-active {
        //     background-color:  #409EFF;
        //     &:before {
        //       display: none;
        //     }
        //     i,span{
        //       color:#fff;
        //   }
        // }
      }
    }
  }
}
.basic-container {
  height: 100%;
}

.basic-container .el-card {
  overflow: auto;
  height: 100%;
}

.basic-container :deep(.el-card) {
  overflow: auto;
  height: 100%;
}