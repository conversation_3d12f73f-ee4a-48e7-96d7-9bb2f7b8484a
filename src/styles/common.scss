// 全局变量
@import './variables.scss';
// ele样式覆盖
@import './element-ui.scss';
// 顶部右侧显示
@import './top.scss';
// 导航标签
@import './tags.scss';
// 工具类函数
@import './mixin.scss';
// 侧面导航栏
@import './sidebar.scss';
// 动画
@import './animate/vue-transition.scss';
//主题
@import './theme/index.scss';
//适配
@import './media.scss';
//通用配置
@import './normalize.scss';

a{
  text-decoration: none;
  color:#333;
}
*{
  outline: none;
}
//滚动条样式
@include scrollBar;
.el-table--small td{
    padding: 2px 0;
    font-size: 14px;
}
.el-button--mini, .el-button--small{
    font-size: 14px;
}
.el-pagination{
    background: #fff;
}
.avue-tags .el-tabs--card > .el-tabs__header .el-tabs__item{
    height: 38px !important;
    line-height: 35px !important;
}
.avue-tags .el-tabs--card > .el-tabs__header .el-tabs__item.is-active{
    background-color: #f2f2f2 !important;
    height: 38px !important;
    line-height: 35px !important;
    color: #66b1ff !important;
    border-bottom: none;
    border-radius: 3px 3px 0 0;
}
.avue-top{
    height: 50px;
    line-height: 50px;
}
.top-bar__left, .top-bar__right, .top-bar__item{
    height: 50px;
    line-height: 50px;
}
.top-bar__left i, .top-bar__right i,.top-search{
    line-height: 50px;
}
.top-search .el-input__inner{
    border: 1px solid #eee;
    height: 30px;
    border-radius: 3px !important;
    line-height: 30px;
}
.top-bar__item{
    width: 100%;
    margin: 0 5px;
}
// .top-search{
//     display: inline-block !important;
//     width: 250px !important;
// }
// .avue-header{
//     padding-left: 200px;
// }
.avue-left{
    width: 200px;
    -webkit-box-shadow: 4px 0px 6px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 4px 0px 6px 0px rgba(0, 0, 0, 0.1);
    z-index: 1028;
}
.avue-logo{
    width: 200px;
    // height: 150px;
    line-height: 50px;
	padding: 20px;
}
.avue-logo_title {
    display: block;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
	color: #fff;
	line-height: 40px;
}
.menu-wrapper .el-submenu .el-submenu__title:hover{
	background: #2880d9 !important;
}
.menu-wrapper .el-submenu .el-menu-item:hover{
	background: #2880d9 !important;
}
.menu-wrapper .el-menu{
	background: #293846 !important;
}
.menu-wrapper li .el-menu-item{
	height: 40px;
	line-height: 40px;
}
.menu-wrapper li .el-menu-item i{
	display: none;
}
.menu-wrapper li .el-menu-item span{
	font-size: 13px;
}
.menu-wrapper li .is-active{
	background: #409EFF !important;
}



.avue-sidebar .el-menu-item i, .avue-sidebar .el-menu-item span, .avue-sidebar .el-submenu__title i, .avue-sidebar .el-submenu__title span {
    color: #fff;
}
.el-menu-item, .el-submenu__title{
	height: 48px;
	line-height: 48px;
}
// .avue-main{
//     left: 200px;
//     width: calc(100% - 200px);
// }
.avue-crud__pagination{
    margin-bottom: 0;
}
.basic-container{
    padding: 10px !important;
    padding-top: 0 !important;
}
.el-form-item__label{
    text-align: right;
}
.avue-main{
    z-index: 900;
}
.el-tabs__nav-scroll{
    padding-left: 0;
}
// .el-scrollbar>.el-scrollbar__wrap{
//     margin-right: -10px !important;
// }

.el-dialog__body{
    max-height: 70vh;
    overflow-y: auto;
}
.el-dialog__headerbtn{
    top: 14px;
}


.el-textarea .el-textarea__inner{
    height: 70px !important;
    min-height: 70px !important;
}

.avue-upload__avatar,.avue-upload__icon{
    width: 80px;
    height: 80px;
    line-height: 80px !important;
}



.el-pagination{
    font-weight: normal;
}

.el-table__body .el-button{
    font-size: 12px;
}
.avue-dialog .el-dialog__header{
    padding: 10px 24px;
}
.pageTitle{
    margin: 0;
    font-size: 16px;
    margin-bottom: 10px;
}
.avue-crud{
    width: 100%;
}

.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{
    margin-bottom: 10px;
}