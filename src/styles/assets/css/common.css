@charset "utf-8";
.wrap {
  width: 100%;
  height: auto !important;
  overflow-y: inherit !important;
  padding: 20px;
  padding-top: 20px;
  padding-left: 0;
  padding-right: 0;
  background: #f0f2f5;
  font-size: 14px;
  position: relative;
}
.center {
  float: left;
  width: 1150px;
  position: absolute;
  left: 50%;
  margin-left: -575px;
}
.centerAuto {
  float: left;
  width: calc(100% - 200px);
  margin-left: 15px;
}
.banner {
  width: 100%;
  height: 240px;
  background: #fff;
  border-radius: 3px;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.bannerDiv {
  width: 100%;
  height: 240px;
  border-radius: 3px;
}
.carousel-item {
  border-radius: 3px;
}
.serviceWrap {
  width: 100%;
  height: 240px;
  background: #fff;
  margin-top: 20px;
  border-radius: 3px;
  position: relative;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.serviceTop {
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  border-bottom: 1px solid #ddd;
  position: relative;
}
.tabChange {
  position: absolute;
  width: 350px;
  left: 50%;
  top: 0;
  margin-left: -175px;
}
.tabChange div {
  float: left;
  height: 40px;
  padding: 0 2px;
  margin-left: 30px;
  cursor: pointer;
  font-size: 16px;
}
.tabChangeClick {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
}
.serviceMore {
  position: absolute;
  right: 0px;
  top: 12px;
  margin-right: 20px;
  color: #999;
  font-size: 14px;
  cursor: pointer;
}
.serviceContent {
  width: 100%;
  height: 200px;
  padding: 10px 16px;
}
.service {
  float: left;
  width: 157px;
  height: 65px;
  line-height: 65px;
  padding-right: 2px;
  padding-top: 10px;
  padding-left: 5px;
  line-height: 0px;
  margin-bottom: 10px;
  margin-right: 10px;
  color: #666;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  text-align: left;
}
.service img {
  width: 45px;
  height: 45px;
  margin-right: 10px;
  vertical-align: middle;
  border-radius: 50%;
}
.serviceHover {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 3px;
  width: 100%;
  height: 100%;
  background: #e2eef4;
}
.service:hover .serviceHover {
  display: block;
}
.hoverTitle {
  width: 27px;
  height: 27px;
  line-height: 27px;
  font-size: 12px;
  font-weight: bold;
  color: #666;
  padding: 0px;
  border-bottom: 1px solid #ccc;
  background: url(../img/xx.png) no-repeat center;
  background-size: 15px;
  float: right;
  margin-top: -27px;
  z-index: 100000;
}
.hoverTitleClick {
  background: url(../img/xxclick.png) no-repeat center;
  background-size: 15px;
}
.hoverTitle1 {
  width: 130px;
  height: 27px;
  line-height: 27px;
  font-size: 12px;
  font-weight: bold;
  color: #666;
  padding: 0 10px;
  border-bottom: 1px solid #ccc;
}
.hoverWrap {
  width: 100%;
  height: 40px;
  padding: 0 5px;
}
.hoverWrap img {
  width: 30px;
  height: 30px;
  margin-top: 5px;
  float: left;
  border-radius: 15px;
}
.hoverWrap p {
  float: left;
  color: #666;
  font-size: 12px;
  height: 15px;
  line-height: 15px;
  margin-top: 3px;
  margin-left: 10px;
}
.commonSystemWrap {
  width: 100%;
  height: 200px;
  background: #fff;
  border-radius: 3px;
  margin-top: 20px;
  margin-bottom: 60px;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.commonSystemContent {
  padding: 0 20px;
}
.commonSystem {
  width: 200px;
  height: 40px;
  float: left;
  margin-right: 10px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  border-radius: 3px;
  margin-top: 10px;
  background: #4f97d4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
.footer {
  text-align: center;
  font-size: 12px;
  color: #999;
  /* margin-top: 20px;
    margin-bottom: 20px; */
}
.wrap >>> .ant-tabs-bar {
  margin: 0;
}
.wrap >>> .ant-tabs-tab {
  font-size: 16px;
}
.wrap >>> .ant-tabs-nav .ant-tabs-tab {
  padding: 6px 2px;
}
