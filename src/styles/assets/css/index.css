@charset "utf-8";
.wrap{
    width: 100%;
    height: auto !important;
    overflow-y: inherit !important;
    padding: 20px;
    padding-top: 20px;
    padding-left: 0;
    padding-right: 0;
    background:#F0F2F5;
    font-size: 14px;
    position: relative;
    margin-top: -20px;
}
.center{
    float: left;
    width: 1150px;
    position: absolute;
    left: 50%;
    margin-left: -575px;
}
.centerAuto{
    float: left;
    width: calc(100% - 200px);
    margin-left: 15px;
}

.banner{
    width: 100%;
    height: 240px;
    background: #fff;
    border-radius: 3px;
}
.bannerDiv{
  width: 100%;
  height: 240px;
  border-radius: 3px;
}
.carousel-item{
    border-radius: 3px;
}
.bannerDetial{
    width: 100%;
    height: 100%;
}
.serviceWrap{
    width: 100%;
    height: 240px;
    border-radius: 3px;
    margin-bottom: 20px;
    background: #fff;
    position: relative;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.noneServiceWrap{
    background: none !important;
    box-shadow: none !important;
}
.serviceTop{
    width: 100%;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    /* border-bottom: 1px solid #eee; */
    position: relative;
}
.serviceTopTitle{
    position: absolute;
    left: 0;
    top: 7px;
    font-size: 16px;
    line-height: 25px;
    margin-left: 20px;
    font-weight: bold;
}
.tabChange{
    position: absolute;
    width: 350px;
    left: 50%;
    top: 0;
    margin-left: -175px;
}
.tabChange div{
    float: left;
    height: 40px;
    padding: 0 5px;
    font-size: 14px;
    margin-left: 30px;
    cursor: pointer;
    color: #666;
}
.tabChange div:hover{
    font-weight: bold;
    color: #424242;
    border-bottom: 2px solid #1890FF;
}
.tabChangeClick{
    font-weight: bold;
    color: #424242 !important;
    border-bottom: 2px solid #1890FF;
}
.serviceMore{
    position: absolute;
    right: 0px;
    top: 12px;
    margin-right: 20px;
    color: #999;
    font-size: 14px;
    cursor: pointer;
}
.serviceContent{
    width: 100%;
    height: 190px;
    padding: 10px 16px;
    display: none;
}
.newWrap{
    width: calc(50% - 10px);
    height: 100%;
    float: left;
    background: #fff;
    border-radius: 3px;
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.noneServiceWrap .newWrap:nth-of-type(1){
    margin-right: 20px;
}
.newContent{
    width: 100%;
    height: 190px;
    padding: 10px 20px;
    display: none;
}
.newUl{
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.messageIconWrap{
    width: 100%;
    height: 55px;
    margin-bottom: 5px;
}
.messageIcon{
    width: 33%;
    float: left;
    font-size: 14px;
    color: #999;
    line-height: 20px;
}
.messageIcon p{
    font-size: 25px;
    line-height: 28px;
    padding-left: 60px;
}
.messageIcon div{
    padding-left: 60px;
}
.messageIcon img{
    float: left;
    width: 48px;
    height: 48px;
}
.messageWrap{
    height: 120px;
}
.newUl li{
    width: 100%;
    height: 29px;
    line-height: 29px;
    text-align: left;
}
.newUl li a{
    color: #757575;
}
.newLiTitle{
    float: left;
    width: calc(100% - 100px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.newLiTime{
    float: right;
    width: 100px;
    text-align: right;
    font-size: 12px;
    color: #999;
}
.daiban{
    color: #FBD9AD;
    margin-right: 5px;
}
.shenqing{
    color: #97C21C;
    margin-right: 5px;
}
.xiaoxi{
    color: #55AED4;
    margin-right: 5px;
}
.mineData{
    float: left;
    width: 30%;
    margin-left: 2.5%;
    margin-top: 40px;
}
.mineData img{
    float: left;
    width: 60px;
    height: 60px;
}
.mineData .mineDataNumber{
    font-size: 24px;
    padding-left: 80px;
    line-height: 35px;
}
.mineData .mineDataTitle{
    font-size: 14px;
    color: #666;
    padding-left: 70px;
    line-height: 20px;
}
/* .serviceContent{
    width: 100%;
    height: 160px;
    padding: 10px;
    display: none;
}
.service{
    float: left;
    width: 157px;
    height: 65px;
    line-height: 65px;
    padding-right: 2px;
    padding-top: 10px;
    padding-left: 5px;
    line-height: 0px;
    margin-bottom: 10px;
    margin-right: 10px;
    color: #666;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    position: relative;
    text-align: left;
}
.service img{
    width: 45px;
    height: 45px;
    margin-right: 5px;
    vertical-align: middle;
    border-radius: 50%;
}
.serviceHover{
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 3px;
    width: 100%;
    height: 100%;
    background: #E2EEF4;
    -moz-box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
    -webkit-box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
    box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}
.hoverTitle {
  width: 27px;
  height: 27px;
  line-height: 27px;
  font-size: 12px;
  font-weight: bold;
  color: #666;
  padding: 0px;
  border-bottom: 1px solid #ccc;
  background: url(../img/xx.png) no-repeat center;
  background-size: 15px;
  float: right;
  margin-top: -27px;
  z-index: 100000;
}
.hoverTitleClick {
  background: url(../img/xxclick.png) no-repeat center;
  background-size: 15px;
}
.hoverTitle1{
    width: 130px;
    height: 27px;
    line-height: 27px;
    font-size: 12px;
    font-weight: bold;
    color: #666;
    padding: 0 10px;
    border-bottom: 1px solid #ccc;
}
.hoverWrap{
    width: 100%;
    height: 40px;
    padding: 0 5px;
}
.hoverWrap img{
    width: 30px;
    height: 30px;
    margin-top: 5px;
    float: left;
    border-radius: 15px;
}
.hoverWrap p{
    float: left;
    color: #666;
    font-size: 12px;
    height: 15px;
    line-height: 15px;
    margin-top: 3px;
    margin-left: 10px;
} */
.dateWrap{
    width: 150px;
    height: 200px;
    float: left;
}
.chart{
    width: calc(100% - 150px);
    height: 100%;
    float: right;
}
.monthWrap{
    width: 100%;
    height: 40px;
    font-weight: bold;
    text-align: center;
}
.month{
    display: inline-block;
    width: 100px;
    color: #666;
    font-weight: bold;
}
.last .iconfont,.next .iconfont{
    font-weight: bold;
    cursor: pointer;
    color: #666;
}
.dataWrap{
    width: 100%;
    height: 140px;
}
.numberWrap{
    width: 50%;
    height: 70px;
    float: left;
    font-size: 14px;
    color: #999;
    text-align: left;
    line-height: 30px;
}
.numberWrap p{
    color: #666;
    font-size: 20px;
    line-height: 30px;
}
.wrap >>> .ant-tabs-bar{
  margin: 0;
}
.wrap >>> .ant-tabs-tab{
  font-size: 14px;
}
.wrap >>> .ant-tabs-nav .ant-tabs-tab{
  padding: 6px 16px;
  height: 40px;
}
