@charset "utf-8";
.wrap {
  width: 100%;
  /* height: 100vh;
  overflow: hidden; */
  position: relative;
}
.top {
  width: 100%;
  height: 15vh;
  padding-top: 20px;
  padding-left: 20px;
  text-align: left;
}
.forgetCenter {
  width: 100%;
  height: 70vh;
  position: relative;
  background: url(../../image/bj.png);
  background-size: contain;
  background-repeat: repeat-x;
  padding-top: 0.1px;
}

.dialogLeft {
  text-align: center;
}
.dialogLeft i {
  font-size: 100px;
  color: #1890ff;
  margin-top: 70px;
}
.dialogLeft p {
  color: #1890ff;
  font-size: 30px;
}
.wrap >>> .el-dialog {
  height: 420px;
}

.dialogRight {
  text-align: left;
}
.contentWrap {
  position: relative;
  height: 330px;
}
.findContent {
  width: 370px;
  margin: 0 auto;
}
.buttonWrap {
  position: absolute;
  width: 370px;
  bottom: 0;
  right: 35px;
}
.buttonWrap .nextButton {
  width: 100px;
  float: right;
  margin-left: 20px;
}
.el-step__title.is-process {
  font-weight: normal;
  color: #666;
}
.wrap >>> .el-step__head.is-success,
.wrap >>> .el-step__title.is-success {
  color: #1890ff;
  border-color: #1890ff;
}
.findContentSuccess {
  font-size: 30px;
  text-align: center;
  color: #1890ff;
}
.contentWrap .el-icon-success {
  font-size: 100px;
  margin-top: 30px;
}
.el-steps {
  background: none;
}
.loginToptitle {
  font-size: 24px;
  color: #00a5ec;
  font-weight: bold;
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}
