.wrapAll {
  width: 100%;
  height: calc(100vh - 60px);
  overflow-y: auto;
  padding: 20px;
  padding-top: 30px;
  padding-left: 0;
  padding-right: 0;
  background: #f0f2f5;
  font-size: 14px;
  position: fixed;
  left: 0;
  top: 60px;
}

/* .center {
  float: left;
  width: 1150px;
  position: absolute;
  left: 50%;
  margin-left: -575px;
  padding-bottom: 40px;
  z-index: 2;
} */

/* .centerAuto {
  float: left;
  width: calc(100% - 200px);
  margin-left: 15px;
}
 */
.indexCenter{
    float: left;
    width: calc(100% - 200px) !important;
    margin-left: 105px !important;
    position: static !important;
}

.topbar {
  width: 100%;
  height: 60px;
  background: #305395;
  /* background: -moz-linear-gradient(91.0159762920382deg, rgba(38, 71, 136, 1) 36%, rgba(57, 160, 205, 1) 110%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #264788), color-stop(70%, #3794C4));
    background: -webkit-linear-gradient(91.0159762920382deg, rgba(38, 71, 136, 1) 36%, rgba(57, 160, 205, 1) 110%);;
    background: -o-linear-gradient(91.0159762920382deg, rgba(38, 71, 136, 1) 36%, rgba(57, 160, 205, 1) 110%);
    background: -ms-linear-gradient(91.0159762920382deg, rgba(38, 71, 136, 1) 36%, rgba(57, 160, 205, 1) 110%);
    background: linear-gradient(91.0159762920382deg, rgba(38, 71, 136, 1) 36%, rgba(57, 160, 205, 1) 110%); */
  position: fixed;
  left: 0;
  top: 0;
  z-index: 66;
  margin-bottom: 30px;
  -webkit-box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.imgWrap {
  float: left;
  margin-left: 10px;
  height: 45px;
  margin-top: 5px;
  overflow: hidden;
}

.imgWrap img {
  height: 45px;
}

.schoolName {
  float: left;
  line-height: 55px;
  color: rgba(255,255,255,0.9);
  font-size: 16px;
  font-weight: bold;
}

.logo {
  height: 45px;
}

.toolbar {
  /* position: absolute;
    left: 50%;
    top: 0;
    margin-left: -362px; */
  height: 60px;
  float: left;
  margin-left: 50px;
  line-height: 50px;
  font-size: 14px;
  color: #fff;
}

.toolbar li {
  float: left;
  height: 100%;
  padding: 0px;
  margin-right: 30px;
  cursor: pointer;
  color: rgba(255,255,255,0.7);
  font-size: 14px;
  border-top: 4px solid transparent;
  transition: all 0.3s ease-in-out;
}

.toolbar li:hover {
  border-top: 4px solid #f9c62d !important;
  color: #f9c62d;
}

.toolbarClick {
  border-top: 4px solid #f9c62d !important;
  color: #f9c62d;
}

.searchWrap {
  float: right;
  height: 100%;
  position: relative;
  margin-right: -10px;
}

.searchWrap input {
  width: 230px;
  height: 30px;
  margin-top: 15px;
  border: none;
  padding-left: 10px;
  padding-right: 30px;
  color: #ccc;
  border-radius: 2px;
  background: rgba(255,255,255,0.2);
}

.searchWrap .searchTopButton {
  position: absolute;
  right: 5px;
  top: 19px;
  width: 20px;
  height: 20px;
  background: url(../image/search.png) no-repeat;
  background-size: 20px;
  z-index: 10000;
  opacity: 0.7;
}

.form-control::-webkit-input-placeholder {
  color: rgba(255,255,255,0.5);
}

.form-control::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: rgba(255,255,255,0.5);
}

.form-control:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: rgba(255,255,255,0.5);
}

.form-control:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: rgba(255,255,255,0.5);
}

.stateBar {
  max-width: 240px;
  height: 60px;
  line-height: 60px;
  float: right;
  margin-right: 10px;
  padding-top: 0.1px;
}

.stateBar .name .headImg {
  float: left;
  width: 20px;
  height: 20px;
  margin-top: 20px;
  margin-right: 10px;
  border-radius: 50%;
}

.stateBar .name {
  float: left;
  height: 100%;
  font-size: 14px;
  color: rgba(255,255,255,0.7);
  max-width: 125px;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 5px;
  padding-right: 10px;
  /* position: relative; */
}

.stateBar .name img {
  width: 15px;
  height: 15px;
  margin-top: 22px;
  margin-left: 5px;
  vertical-align: sub;
}

.minePopup {
  width: 135px;
  margin: 0 auto;
  background: #f8f8f8;
  color: #999;
  border-radius: 3px;
  line-height: 33px;
  cursor: pointer;
  /* display: none; */
  position: absolute;
  right: 10px;
  top: 60px;
  z-index: 1000000;
  -webkit-box-shadow: 0px 2px 5px rgba(30, 48, 76, 0.32156862745098);
  -moz-box-shadow: 0px 2px 5px rgba(30, 48, 76, 0.32156862745098);
  box-shadow: 0px 2px 5px rgba(30, 48, 76, 0.32156862745098);
}

.minePopup i {
  font-size: 16px;
  color: #999;
  margin-right: 10px;
}

.minePopup p {
  padding: 0 10px;
}

.minePopup p:nth-last-of-type(1) {
  border-top: 1px solid #eee;
}

.minePopup p:hover {
  background: #f0f0f0;
}

/* .name:hover .minePopup {
  display: block;
} */

.changeSkin,
.signOut {
  float: left;
  width: 20px;
  height: 60px;
  margin-left: 30px;
  cursor: pointer;
}

.changeSkin img,
.signOut img {
  width: 20px;
  vertical-align: middle;
}

.changeSkin{
    width: 40px;
    padding: 0 10px;
    position: relative;
}

/* .changeSkin:hover .skinBox {
  display: block;
} */

.header-skin {
  float: right;
  overflow: hidden;
}

.skinTitle {
  font-size: 14px;
  padding: 0 15px;
  line-height: 25px;
  margin-top: 5px;
}

.header-skin li {
  display: inline;
  float: left;
  width: 14px;
  height: 14px;
  margin: 7px 5px;
  border: 1px solid #ddd;
  cursor: pointer;
  border-radius: 50%;
}

.skinChange {
  color: #fff;
  float: right;
  line-height: 50px;
  cursor: pointer;
}

.skinBox {
  /* display: none; */
  width: 160px;
  float: right;
  overflow: hidden;
  position: absolute;
  right:0px;
  top: 60px;
  z-index: 1000000;
  background: #f8f8f8;
  border-radius: 3px;
  -webkit-box-shadow: 0px 2px 5px rgba(30, 48, 76, 0.32156862745098);
  -moz-box-shadow: 0px 2px 5px rgba(30, 48, 76, 0.32156862745098);
  box-shadow: 0px 2px 5px rgba(30, 48, 76, 0.32156862745098);
}

.header-skin {
  padding: 10px;
  padding-top: 0;
}

.header-skin li.header-skin-selected {
  display: block;
  float: left;
  width: 60px;
  height: 50px;
  border: none;
  margin: 7px 5px;
  border-radius: 3px;
  box-sizing: border-box;
  color: #333;
}

.header-skin-selected > div:after {
  content: "✔";
  display: block;
  width: 60px;
  height: 35px;
  background: rgba(0, 0, 0, 0.1);
  line-height: 35px;
  border-radius: 3px;
  text-align: center;
  color: #fff;
}

.header-skin li {
  display: block;
  float: left;
  width: 60px;
  height: 50px;
  border: none;
  border-radius: 3px;
  box-sizing: border-box;
  color: #333;
}

.header-skin li > div {
  width: 100%;
  height: 35px;
  border-radius: 3px;
}

.header-skin li > p {
  width: 100%;
  height: 15px;
  margin-top: 5px;
  line-height: 15px;
  font-size: 12px;
  text-align: center;
  color: #666;
}

.header-skin li.header-skin-4 > div {
  background: #eee;
}

.header-skin li.header-skin-1 > div {
  background: #005bac;
}

.header-skin li.header-skin-2 > div {
  background: #B28039;
}

.header-skin li.header-skin-3 > div {
  background: #525F72;
}

.header-skin li.header-skin-5 > div {
  background: #6F2D88;
}

.header-skin li.header-skin-6 > div {
  background: #C02B22;
}

.header-skin li.header-skin-7 > div {
  background: #26675B;
}

.left {
  width: 65px;
  float: left;
  margin-left: 20px;
  position: fixed;
  left: 0;
  top: 90px;
}

.right {
  width: 65px;
  float: right;
  margin-right: 20px;
  position: fixed;
  right: 0;
  top: 90px;
}

.setUp {
  width: 65px;
  height: 65px;
  text-align: center;
  border-radius: 3px;
  margin-bottom: 8px;
  padding-top: 2px;
  background: #fff;
  cursor: pointer;
  box-shadow: 2px 2px 4px rgba(230, 230, 230, 1);
}

.setUp .iconfont {
  font-size: 28px;
  color: #999;
}

.setUp p {
  font-size: 12px;
  color: #999;
  margin-top: -5px;
}

.setUp:hover {
  background: #305395;
  color: #fff;
}

.setUp:hover p {
  color: #fff;
}

.setUp:hover .iconfont {
  color: #fff;
}

.tabClick {
  background: #305395;
  color: #fff !important;
}

.tabClick p {
  color: #fff;
}

.tabClick .iconfont {
  color: #fff;
}

.footer {
  width: 100%;
  line-height: 30px;
  font-size: 12px;
  color: #999;
  width: 100%;
  text-align: center;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 1;
}
.footer p {
  margin: 10px;
}

.footer1 {
  width: 100%;
  line-height: 30px;
  font-size: 12px;
  color: #999;
  width: 100%;
  text-align: center;
}
.footer1 p {
  margin: 10px;
}

.ant-tabs-nav-scroll {
  text-align: center;
}
.ant-tabs-bar {
  margin: 0;
}


.headerBackground{
    background: rgba(0,0,0,0.15);
    transition: all 0.2s ease-in-out;
}


.rightMenuButton{
    position: fixed;
    right: 10px;
    top: 140px;
    display: none;
    z-index: 2000000;
}
.leftMenuButton{
    position: fixed;
    right: 10px;
    top: 90px;
    display: none;
    z-index: 2000000;
}
.el-drawer__header{
    margin-bottom: 10px;
}
.leftMenu .el-drawer,.rightMenu .el-drawer{
    background: rgba(0,0,0,0.8);
}

.leftMenu .el-drawer .ant-menu{
    background: none;
}

.rightMenu .setUp{
    margin-left: 10px;
}

.caidanImg{
    float: right;
    width: 20px;
    height: 100%;
    display: none;
}

@media (max-width: 850px){
    .indexCenter{
        width: 100% !important;
        margin-left: 0 !important;
    }
    .searchWrap,.toolbar{
        display: none;
    }
    .toolbar{
        margin-left: 20px;
    }
    .rightMenuButton,.leftMenuButton{
        display: block;
    }
    .left,.right{
        display: none;
    }
    .stateBar .name .nameSpan{
        display: none;
    }
    .caidanImg{
        display: block;
    }
    .el-drawer__wrapper .setUp{
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1) !important;
        background: rgba(0,0,0,0.2) !important;
    }
    .el-drawer__wrapper .tabClick{
        background:rgba(255,255,255,0.2) !important;
    }
    .setUp .iconfont,.setUp p{
        color: #FFF !important;
        opacity: 0.6;
    }
}



