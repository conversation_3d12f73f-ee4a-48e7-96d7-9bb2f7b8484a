@charset "utf-8";
.wrap {
  width: 100%;
  height: auto !important;
  overflow-y: inherit !important;
  padding: 20px;
  padding-top: 20px;
  padding-left: 0;
  padding-right: 0;
  background: #f0f2f5;
  font-size: 14px;
  /* position: relative; */
  margin-top: -20px;
}
/* .center {
  float: left;
  width: 1150px;
  position: absolute;
  left: 50%;
  margin-left: -575px;
} */
.centerAuto {
  float: left;
  width: calc(100% - 200px);
  margin-left: 15px;
}
.serviceCenter{
    width: 100%;
    float: none;
    position: relative !important;
    left: 0% !important;
    margin-left: 0 !important;
    z-index: 2;
    /* padding-bottom: 40px; */
}
.selectWrap,
.functionWrap {
  width: 100%;
  background: #fff;
  border-radius: 3px;
  padding-bottom: 10px;
}
.functionWrap {
  margin-top: 15px;
  padding: 0 30px 30px;
}
.selectWrap .title {
  font-size: 20px;
  color: #424242;
  font-weight: bold;
  line-height: 60px;
  padding: 0 30px;
  text-align: left;
}
.sortWrap {
  width: 100%;
  padding: 0 30px;
  margin-bottom: 10px;
}
.sortWrap p {
  float: left;
  width: 90px;
  height: 30px;
  line-height: 30px;
  color: #666;
  text-align: left;
}
.sortContent {
  float: right;
  width: calc(100% - 90px);
}
.sort {
  float: left;
  color: #666;
  padding: 5px 10px;
  border-radius: 3px;
  margin-right: 10px;
  cursor: pointer;
}
.sort:hover {
  background: #1890FF;
  color: #fff;
}
.sortClick {
  background: #fff;
}
.search {
  width: 250px;
  height: 30px;
  border: none;
  border-radius: 3px 0 0 3px;
  border: 1px solid #ccc;
  border-right: none;
  background: #fff;
  padding-left: 10px;
  float: left;
  outline: none;
}
.searchButton {
  float: left;
  width: 60px;
  height: 30px;
  border: none;
  border-radius: 0 3px 3px 0;
  color: #fff;
  background: #1890FF;
  cursor: pointer;
  outline: none;
}
.searchButton img {
  width: 18px;
  height: 18px;
}
.functionLeft {
  float: left;
  width: 90px;
}
.functionLeft p {
  width: 100%;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  height: 55px;
  line-height: 67px;
  text-align: left;
  margin-bottom: 22px;
}
.functionLeft div {
  width: 50px;
  height: 30px;
  line-height: 30px;
  border-radius: 3px;
  text-align: center;
  margin-bottom: 10px;
  background: #e2eef4;
  color: #666;
  cursor: pointer;
  font-size: 12px;
}
.functionLeft div:hover {
  background: #1890ff;
  color: #fff;
}
.functionRight {
  float: left;
  width: 100%;
  padding-top: 6px;
  width: calc(100% - 90px);
}
.tab {
  float: left;
  height: 30px;
  margin-top: 15px;
  margin-left: 4px;
  line-height: 30px;
  background: #fff;
  border-radius: 3px;
  color: #666;
}
.tab div {
  float: left;
  padding: 0 20px;
  border-radius: 3px;
  cursor: pointer;
}
.tab div:hover {
  background: #1890FF;
  color: #fff;
}
.tab1Click,
.tab1Click p {
  background: #1890FF;
  color: #fff;
}
.functionContent {
  margin-top: 50px;
  padding: 10px 0px;
  /* display: flex !important;
  justify-content: space-between;
  flex-wrap: wrap; */
}
.function {
  float: left;
  width: 157px;
  height: 75px;
  line-height: 75px;
  padding-right: 2px;
  padding-top: 0.1px;
  padding-left: 5px;
  line-height: 0px;
  margin-right: 25px;
  color: #777;
  cursor: pointer;
  position: relative;
  text-align: left;
}
.functionName{
    display: inline-block;
    width: 90px;
    height: 45px;
    line-height: 45px;
    vertical-align: middle;
    margin-top: 15px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.function img {
  display: inline-block;
  width: 45px;
  height: 45px;
  margin-top: 15px;
  margin-right: 10px;
  /* vertical-align: middle; */
}
.functionHover {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 3px;
  width: 100%;
  height: 100%;
  background: #fff;
  border: 1px solid #eee;
  box-shadow: 2px 2px 5px rgba(30, 48, 76, 0.2);
}
.hoverTitle {
  width: 27px;
  height: 27px;
  line-height: 27px;
  font-size: 12px;
  font-weight: bold;
  color: #666;
  padding: 0px;
  border-bottom: 1px solid #eee;
  background: url(../img/xx.png) no-repeat center;
  background-size: 15px;
  float: right;
  margin-top: -27px;
  z-index: 100000;
}
.hoverTitleClick {
  background: url(../img/xxclick.png) no-repeat center;
  background-size: 15px;
}
.hoverTitle1{
    width: 128px;
    height: 27px;
    line-height: 27px;
    font-size: 12px;
    font-weight: bold;
    color: #666;
    padding: 0 10px;
    border-bottom: 1px solid #eee;
}
.hoverWrap {
  width: 100%;
  height: 45px;
  padding: 0 10px;
}
.hoverWrap img {
  width: 30px;
  height: 30px;
  margin-top: 8px;
  float: left;
  border-radius: 15px;
}
.hoverWrap p {
  float: left;
  color: #999;
  font-size: 12px;
  height: 15px;
  line-height: 15px;
  margin-top: 3px;
  margin-left: 5px;
}
.hoverWrap p:nth-of-type(1){
    margin-top: 6px;
}
/* .function:hover .functionHover {
  display: block;
} */

.search::-webkit-input-placeholder {
  color: #ccc;
}
.search::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #ccc;
}
.search:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #ccc;
}
.search:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #ccc;
}

/* 弹窗 */
.wrap >>> .el-dialog__header {
  text-align: left;
  font-weight: bold;
}
.wrap >>> .el-dialog__body {
  padding: 10px 20px;
}
.wrap >>> .el-dialog {
  width: 800px;
  height: 500px;
}
.popup {
  width: 100%;
  height: 400px;
  border-radius: 3px;
  background: #fff;
  display: block;
}
.popupTitle {
  width: 100%;
  height: 60px;
  padding: 0 20px;
  border-bottom: 1px solid #eee;
}
.popupTitleImg {
  width: 40px;
  height: 40px;
  float: left;
  margin-top: 10px;
}
.popupTitle span {
  float: left;
  line-height: 60px;
  margin-left: 10px;
  font-size: 16px;
  font-weight: bold;
}
.popupTitleClose {
  float: right;
  width: 15px;
  height: 15px;
  margin-top: 25px;
  cursor: pointer;
}
.popupCenter {
  width: 100%;
  height: 40px;
  width: 760px;
  margin: 0 auto;
  margin-top: 10px;
  border-bottom: 1px solid #ccc;
}
.popupTab {
  float: left;
  height: 40px;
  line-height: 39px;
  font-size: 14px;
  margin-left: 20px;
}
.popupTab li {
  float: left;
  padding: 0 20px;
  cursor: pointer;
  border-radius: 3px;
  background: #fff;
}
.popupTab .liClick {
  border: 1px solid #ccc;
  border-bottom: none;
}
.number11 {
  position: absolute;
  top: 70px;
  right: 0;
  font-size: 12px;
  color: #999;
  line-height: 40px;
  margin-right: 20px;
}
.number11 img {
  width: 15px;
  vertical-align: text-top;
}
.popupContent {
  margin-top: 5px;
}
.PopupBox {
  width: 100%;
  margin: 0 auto;
  height: 300px;
  padding: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  font-size: 14px;
  display: block;
}
.popupP {
  color: #666;
  line-height: 20px;
  text-align: left;
}
.popupP span {
  color: #333;
  font-weight: bold;
}
.button1Wrap {
  text-align: center;
  margin-top: 15px;
}
.button1Wrap button {
  width: 100px;
  height: 35px;
  background: #2e8ded;
  border: none;
  color: #fff;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}
.feedback {
  width: 100%;
  height: 210px;
  resize: none;
  margin-top: 10px;
}
.AIbox {
  width: 200px;
  height: 150px;
  margin: 0 auto;
  text-align: center;
  padding-top: 50px;
  color: #666;
  line-height: 30px;
}
.AIbox img {
  width: 50px;
  height: 50px;
}
.introduce {
  width: 100%;
  text-align: center;
  margin-top: 10px;
  color: #666;
}
.serviceNone{
    text-align: center;
    color: #999;
}
.serviceNone img{
    opacity: 0.3;
    width: 80px;
    margin-top: 50px;
    margin-bottom: 5px;
}



@media (min-width: 1024px){
    .function{
        margin-right: 9px;
    }
}
 
@media (min-width: 1280px) {
    .function{
        margin-right: 25px;
    }
} 
 
@media (min-width: 1366px) {
    .function{
        margin-right: 10px;
    }
}
 
@media (min-width: 1440px) {
    .function{
        margin-right: 23px;
    }
}
  
@media (min-width: 1600px) {
    .function{
      margin-right: 19px;
    }
}  
 
@media (min-width: 1680px) {
    .function{
        margin-right: 8px;
    }
}
@media (min-width: 1920px) {
    .function{
        margin-right: 15px;
    }
}


