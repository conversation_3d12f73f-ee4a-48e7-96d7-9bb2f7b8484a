<template>
    <el-form class="login-form"
             status-icon
             :rules="loginRules"
             ref="loginForm"
             :model="loginForm"
             label-width="0">
        <el-form-item prop="username">
            <el-input size="small"
                      @keyup.enter.native="handleLogin"
                      v-model="loginForm.username"
                      auto-complete="off"
                      :placeholder="$t('login.username')">
                <i slot="prefix" class="icon-yonghu"></i>
            </el-input>
        </el-form-item>
        <el-form-item prop="password">
            <el-input size="small"
                      @keyup.enter.native="handleLogin"
                      v-model="loginForm.password"
                      auto-complete="off"
                      :placeholder="$t('login.password')">
                <i class="el-icon-view el-input__icon"
                   slot="suffix"
                   @click="showPassword"></i>
                <i slot="prefix" class="icon-mima"></i>
            </el-input>
        </el-form-item>
        <el-form-item prop="code">
            <el-row :span="24">
                <el-col :span="16">
                    <el-input size="small"
                              @keyup.enter.native="handleLogin"
                              :maxlength="code.len"
                              v-model="loginForm.code"
                              auto-complete="off"
                              :placeholder="$t('login.code')">
                        <i slot="prefix" class="icon-yanzhengma"></i>
                    </el-input>
                </el-col>
                <el-col :span="8">
                    <div class="login-code">
                        <span class="login-code-img"
                              @click="refreshCode"
                              v-if="code.type == 'text'">{{ code.value }}</span>
                        <img :src="code.src"
                                class="login-code-img"
                                @click="refreshCode"
                                v-else/>
                        <!-- <i class="icon-shuaxin login-code-icon" @click="refreshCode"></i> -->
                    </div>
                </el-col>
            </el-row>
        </el-form-item>
        <el-form-item>
            <el-checkbox label="记住密码" name="type"></el-checkbox>
            <span style="color: #606266; float: right; cursor: pointer" @click="sendMsg">忘记密码</span>
        </el-form-item>
        <el-form-item>
            <el-button type="primary"
                    size="small"
                    @click.native.prevent="handleLogin"
                    class="login-submit">{{ $t("login.submit") }}</el-button>
        </el-form-item>
    </el-form>
</template>

<script>
    import {randomLenNum} from "@/util/util";
    import {mapGetters} from "vuex";
    import {setStore} from "@/util/store";
    import {toLogin} from "@/api";

    export default {
        name: "userlogin",
        data() {
            const validateCode = (rule, value, callback) => {
                if (this.code.value != value) {
                    this.loginForm.code = "";
                    this.refreshCode();
                    callback(new Error("请输入正确的验证码"));
                } else {
                    callback();
                }
            };
            return {
                loginForm: {
                    username: "10001",
                    password: "sanyth123!",
                    code: "",
                    redomStr: "",
                },
                checked: false,
                code: {
                    src: "",
                    value: "",
                    len: 4,
                    type: "text",
                },
                loginRules: {
                    username: [
                        {
                            required: true,
                            message: "请输入用户名",
                            trigger: "blur",
                        },
                    ],
                    password: [
                        {
                            required: true,
                            message: "请输入密码",
                            trigger: "blur",
                        },
                        {
                            min: 6,
                            message: "密码长度最少为6位",
                            trigger: "blur",
                        },
                    ],
                    code: [
                        {
                            required: true,
                            message: "请输入验证码",
                            trigger: "blur",
                        },
                        {
                            min: 4,
                            max: 4,
                            message: "验证码长度为4位",
                            trigger: "blur",
                        },
                        {
                            required: true,
                            trigger: "blur",
                            validator: validateCode,
                        },
                    ],
                },
                number: 1,
                // passwordType: "password"
            };
        },
        created() {
            this.refreshCode();
        },
        mounted() {
        },
        computed: {
            ...mapGetters(["tagWel"]),
        },
        props: [],
        methods: {
            refreshCode() {
                this.loginForm.redomStr = randomLenNum(this.code.len, true);
                this.code.type == "text"
                    ? (this.code.value = randomLenNum(this.code.len))
                    : (this.code.src = `${this.codeUrl}/${this.loginForm.redomStr}`);
                this.loginForm.code = this.code.value;
            },
            showPassword() {
                this.passwordType == ""
                    ? (this.passwordType = "password")
                    : (this.passwordType = "");
            },
            handleLogin() {
                this.$refs.loginForm.validate((valid) => {
                    if (valid) {
                        // setStore({ name: "tokenFlag", content: 1 });
                        // // console.log(">>>", this.loginForm);
                        // this.$store.dispatch("LoginByUsername", this.loginForm).then(() => {
                        //   this.$router.push({
                        //     // path: this.tagWel.value
                        //     path: "/",
                        //   });
                        // });
                        // console.log(this.loginForm);
                        let {username, password} = this.loginForm;
                        console.log(encrypt(username), encrypt(password));
                        toLogin({
                            username: encrypt(username),
                            password: encrypt(password),
                        }).then((res) => {
                            // console.log(res);
                            if (res.data.code === "00000") {
                                this.$router.replace({
                                    path: this.$router.$avueRouter.getPath({
                                        name: "首页",
                                        src: "/wel/index",
                                    }),
                                    query: {},
                                });
                            }
                        });
                    }
                });
            },
            sendMsg() {
                //func: 是父组件指定的传数据绑定的函数，this.msg:子组件给父组件传递的数据
                this.$emit("func", this.number);
            },
        },
    };
</script>

<style>
</style>
