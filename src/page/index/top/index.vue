<template>
    <div class="avue-top">
        <div class="top-bar__left">
            <div class="avue-breadcrumb"
                 :class="[{ 'avue-breadcrumb--active': isCollapse }]"
                 v-if="showCollapse">
                <i class="el-icon-s-fold" @click="setCollapse"></i>
            </div>
        </div>
        <div class="top-bar__title">
            <top-search></top-search>
            <p class="mobileTitle">统一身份认证</p>
        </div>
        <div class="top-bar__right">
            <div style="display: inline-block;width: 30px; height: 30px;line-height: 1px;margin-top: 10px;">
                <img class="top-bar__img"
                     style="margin-right: 0px"
                     v-if="info.avatar"
                     :src="'/file/view/' + info.avatar"
                     :onerror="defaultImg"/>
                <img class="top-bar__img"
                     style="margin-right: 0px"
                     v-else
                     src="../../../styles/assets/image/touxiangf.png"/>
            </div>
            <el-dropdown style="margin-right: 20px">
                <span class="el-dropdown-link">
                  <!-- {{userInfo.username}} -->
                  <span class="loginName">{{ username }}</span>
                  <i class="el-icon-arrow-down el-icon--right"
                     style="vertical-align: top"></i>
                </span>
                <el-dropdown-menu class="selectDrop" slot="dropdown">
                    <el-dropdown-item>
                        <div @click="checkRoleHandle">选择角色</div>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <router-link to="/mine/info/index">{{$t("navbar.userinfo")}}
                        </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <router-link to="/mine/password/index">修改密码</router-link>
                    </el-dropdown-item>
                    <el-dropdown-item @click.native="logout" divided>{{$t("navbar.logOut")}}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
        <el-dialog class="role-container"
                   title="选择角色"
                   append-to-body
                   :visible.sync="isChangeRole"
                   width="300px"
                   :close-on-click-modal="false">
            <div class="role-item"
                 v-for="item in roleList"
                 :key="item"
                 :class="{ 'is-active': roleName === item }"
                 @click="changeRoleHandle(item)">
                {{ item }}
            </div>
        </el-dialog>
    </div>
</template>
<script>
    import {getRoleList, switchRole} from "@/api/user";
    import {mapGetters, mapState} from "vuex";
    import {fullscreenToggel, listenfullscreen} from "@/util/util";
    import {setStore} from "@/util/store";
    import {filterDesktop} from "@/util/filter";
    import topLock from "./top-lock";
    import topMenu from "./top-menu";
    import topSearch from "./top-search";
    import topTheme from "./top-theme";
    import topLogs from "./top-logs";
    import topColor from "./top-color";
    import topNotice from "./top-notice";
    import topLang from "./top-lang";

    export default {
        components: {
            topLock,
            topMenu,
            topSearch,
            topTheme,
            topLogs,
            topColor,
            topNotice,
            topLang,
        },
        name: "top",
        data() {
            return {
                isChangeRole: false,
                roleList: [],
                userdata: null,
                roleName: "",
                username: "",
                info: {
                    realname: "",
                    avatar: "",
                },
                defaultImg:
                    'this.src="' +
                    require("../../../styles/assets/image/touxiangf.png") +
                    '"',
            };
        },
        filters: {},
        created() {
            this.getRoleInfo();
        },
        mounted() {
            listenfullscreen(this.setScreen);
        },
        inject: ["appReload"],
        computed: {
            ...mapState({
                showDebug: (state) => state.common.showDebug,
                showTheme: (state) => state.common.showTheme,
                showLock: (state) => state.common.showLock,
                showFullScren: (state) => state.common.showFullScren,
                showCollapse: (state) => state.common.showCollapse,
                showSearch: (state) => state.common.showSearch,
                showMenu: (state) => state.common.showMenu,
                showColor: (state) => state.common.showColor,
            }),
            ...mapGetters([
                "userInfo",
                "isFullScren",
                "tagWel",
                "tagList",
                "isCollapse",
                "tag",
                "logsLen",
                "logsFlag",
            ]),
        },
        methods: {
            getRoleInfo() {
                getRoleList().then((res) => {
                    if (res.data.code === "00000") {
                        this.userdata = res.data.info;
                        this.roleName = this.userdata.roleName;
                        this.username = this.userdata.humanname;
                        this.roleList = this.userdata.roleList;
                        setStore({name: "user-info", content: this.userdata});
                    }
                });
            },
            // 切换角色
            checkRoleHandle() {
                this.isChangeRole = true;
            },
            changeRoleHandle(item) {
                switchRole({role: item}).then((res) => {
                    if (res.data && res.data.code === "00000") {
                        this.getRoleInfo();
                        this.isChangeRole = false;
                        this.$store.commit("DEL_ALL_TAG");
                        // this.$store.commit('SET_MENUID', {})
                        // this.$store.commit('SET_MENUALL', []);
                        // this.$store.commit('SET_MENU', [])
                        // this.$store.commit('SET_ROLES', [])


                        // window.location.href = "/index";
                        this.$message.success("角色切换成功！");
                        this.$router.replace({
                            path: this.$router.$avueRouter.getPath({
                                name: "首页",
                                src: "/wel/index",
                            }),
                            query: {},
                        });
                        this.appReload();
                    }
                });
            },
            handleScreen() {
                fullscreenToggel();
            },
            setCollapse() {
                this.$store.commit("SET_COLLAPSE");
            },
            setScreen() {
                this.$store.commit("SET_FULLSCREN");
            },
            logout() {
                this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
                    confirmButtonText: this.$t("submitText"),
                    cancelButtonText: this.$t("cancelText"),
                    type: "warning",
                }).then(() => {
                    window.location.href = "/logout";
                });
            },
            toFront() {
                this.$router.push({
                    path: "/portal/index/common",
                });
            },
            toMineDesktopId() {
                filterDesktop("我的桌面").then((res) => {
                    this.$router.push("/portal/index/desktop?Did=" + res);
                });
            },
        },
    };
</script>

<style scoped>
    .loginName {
        font-size: 14px;
        color: #666;
        display: inline-block;
        width: 60px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .toFront {
        position: absolute;
        font-size: 14px;
        padding: 0 10px;
        line-height: 50px;
        left: 0;
        top: 0;
        cursor: pointer;
    }

    .toFront:hover {
        background: #eee;
    }

    .el-icon-monitor {
        margin-right: 5px;
        font-size: 16px;
    }

    .el-dropdown {
        margin-left: 10px;
    }

    .el-dropdown-link {
        cursor: pointer;
    }

    .top-bar__title {
        padding-right: 150px;
    }

    .top-search {
        float: right;
        line-height: 50px;
        margin-top: -5px;
    }

    .role-container >>> .el-dialog__body {
        padding: 10px 10px 30px;
    }

    .is-active {
        color: #00a5ec;
        font-weight: 700;
    }

    .role-item {
        height: 30px;
        line-height: 30px;
        padding: 0 10px;
        cursor: pointer;
    }

    .role-item:hover {
        background-color: #eee;
    }

    .my-dropdown >>> .el-dropdown-menu {
        color: red;
    }

    .avue-breadcrumb--active {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }

    .avue-breadcrumb i {
        font-size: 25px !important;
        font-weight: 100;
        color: #666;
    }

    .selectDrop {
        top: 40px !important;
        left: auto !important;
        right: 10px !important;
    }

    .mobileTitle {
        font-size: 16px;
        line-height: 50px;
        margin: 0;
        display: none;
    }
</style>
