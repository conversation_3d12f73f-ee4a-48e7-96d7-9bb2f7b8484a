<template>
    <div class="avue-contail" :class="{ 'avue--collapse': isCollapse }">
        <div class="avue-header">
            <!-- 顶部导航栏 -->
            <top ref="top"/>
        </div>
        <div class="avue-layout">
            <div class="avue-left">
                <!-- 左侧导航栏 -->
                <sidebar/>
            </div>
            <div class="avue-main" :class="{ 'avue-main--fullscreen': !isMenu }">
                <!-- 顶部标签卡 -->
                <tags/>
                <!-- 主体视图层 -->
                <div style="height: 100%; overflow-y: auto; overflow-x: hidden"
                     id="avue-view"
                     v-show="!isSearch">
                    <transition name="fade-transform" mode="out-in">
                        <router-view class="avue-view" v-if="isRouterAlive"/>
                    </transition>
                </div>
            </div>
        </div>
        <div class="avue-shade" @click="showCollapse"></div>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import tags from "./tags";
    import screenshot from "./screenshot";
    import search from "./search";
    import top from "./top/";
    import sidebar from "./sidebar/";
    import admin from "@/util/admin";
    import {setTheme} from "@/util/util";

    export default {
        components: {
            top,
            tags,
            search,
            sidebar,
            screenshot,
        },
        name: "index",
        provide() {
            return {
                index: this,
                reload: this.reload,
            };
        },
        data() {
            return {
                //搜索控制
                isSearch: false,
                //刷新token锁
                refreshLock: false,
                //刷新token的时间
                refreshTime: "",
                isRouterAlive: true,
            };
        },
        created() {
            //实时检测刷新token
            setTheme("theme-iview");
        },
        mounted() {
            this.init();
        },
        computed: mapGetters(["isMenu", "isLock", "isCollapse", "website", "menu"]),
        props: [],
        methods: {
            reload() {
                this.isRouterAlive = false;
                this.$nextTick(() => {
                    this.isRouterAlive = true;
                });
            },
            showCollapse() {
                this.$store.commit("SET_COLLAPSE");
            },
            // 屏幕检测
            init() {
                this.$store.commit("SET_SCREEN", admin.getScreen());
                window.onresize = () => {
                    setTimeout(() => {
                        this.$store.commit("SET_SCREEN", admin.getScreen());
                    }, 0);
                };
            },
            //打开菜单
            GetMenu(item = {}) {
                this.$store.dispatch("GetMenu", item.parentId).then((data) => {
                    if (data && data.length !== 0) {
                        console.log("GetMenu==打开菜单==============")
                        this.$router.$avueRouter.formatRoutes(data, true);
                    }
                    //当点击顶部菜单做的事件
                    if (!this.validatenull(item)) {
                        let itemActive = {},
                            childItemActive = 0;
                        //vue-router路由
                        if (item.path) {
                            itemActive = item;
                        } else {
                            if (this.menu[childItemActive].length == 0) {
                                itemActive = this.menu[childItemActive];
                            } else {
                                itemActive = this.menu[childItemActive].children[childItemActive];
                            }
                        }
                        this.$store.commit("SET_MENUID", item);
                        this.$router.push({
                            path: this.$router.$avueRouter.getPath(
                                {
                                    name: itemActive.label,
                                    src: itemActive.path,
                                },
                                itemActive.meta
                            ),
                        });
                    }
                });
            },
            // 10分钟检测一次token
            // refreshToken() {
            //   this.refreshTime = setInterval(() => {
            //     const token =
            //       getStore({
            //         name: "token",
            //         debug: true
            //       }) || {};
            //     const date = calcDate(token.datetime, new Date().getTime());
            //     if (validatenull(date)) return;
            //     if (date.seconds >= this.website.tokenTime && !this.refreshLock) {
            //       this.refreshLock = true;
            //       this.$store
            //         .dispatch("RefeshToken")
            //         .then(() => {
            //           this.refreshLock = false;
            //         })
            //         .catch(() => {
            //           this.refreshLock = false;
            //         });
            //     }
            //   }, 1000);
            // }
        },
    };
</script>

<style scoped>
    /* fade-transform */
    .fade-transform-leave-active,
    .fade-transform-enter-active {
        transition: all 0.46s;
    }

    .fade-transform-enter {
        opacity: 0;
        transform: translateX(-20px);
    }

    .fade-transform-leave-to {
        opacity: 0;
        transform: translateX(20px);
    }

    .el-scrollbar >>> .el-scrollbar__wrap {
        margin-right: -17px !important;
    }

    .el-menu-item:focus,
    .el-menu-item:hover {
        background: transparent !important;
    }

    .avue-sidebar >>> .el-menu {
        background-color: transparent;
    }

    .avue-sidebar {
        padding-top: 0px;
    }
</style>
