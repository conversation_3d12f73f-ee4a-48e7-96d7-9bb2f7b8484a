<template>
    <div id="app">
        <router-view v-if="isRouterAlive"/>
    </div>
</template>

<script>
    import {GetSysParam} from "@/api/sysParam";

    export default {
        name: "app",
        provide() {
            return {
                appReload: this.reload,
            };
        },
        data() {
            return {
                isRouterAlive: true,
            };
        },
        async created() {
          await GetSysParam({idOrNameOrType: "isgrey"}).then(res=>{
            let val = res.data.info.value;
            if(val == '是'){
              document.documentElement.classList.add('mourning');
            }else {
              document.documentElement.classList.remove('mourning');
            }
          })
        },
        methods: {
            reload() {
                this.isRouterAlive = false;
                this.$nextTick(() => {
                    this.isRouterAlive = true;
                });
            },
        },
        computed: {},
    };
</script>
<style>
    @import url("styles/assets/font/font.css");
</style>
<style lang="scss">
    #app {
        width: 100%;
        height: 100%;
        overflow: hidden;
        font-family: "Helvetica Neue", Helvetica, "Hiragino Sans GB",
        "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", "PingFang SC", Arial, sans-serif;
    }
    /* 可以根据需要添加其他元素的样式，使其在灰色显示期间得到限制或变化 */
    .mourning {
      filter: grayscale(100%) !important; /* 将内容元素转为灰度图像 */
    }
</style>
