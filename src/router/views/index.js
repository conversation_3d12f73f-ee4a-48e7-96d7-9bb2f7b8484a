import Layout from '@/page/index/'

export default [
    {
        path: '/wel',
        component: Layout,
        redirect: '/wel/index',
        children: [{
            path: 'index',
            name: '首页',
            meta: {
                i18n: 'dashboard',
                // isAuth: false
            },
            component: () =>
                import( /* webpackChunkName: "views" */ '@/views/wel/index')
        },
            // {
            //     path: 'dashboard',
            //     name: '控制台',
            //     meta: {
            //         i18n: 'dashboard',
            //         menu: false,
            //     },
            //     component: () =>
            //         import( /* webpackChunkName: "views" */ '@/views/wel/dashboard')
            // }
        ]
    },
    // {
    //     path: '/form-detail',
    //     component: Layout,
    //     children: [{
    //         path: 'index',
    //         name: '详情页',
    //         meta: {
    //             i18n: 'detail'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/util/form-detail')
    //     }]
    // },
    // {
    //     path: '/info',
    //     component: Layout,
    //     redirect: '/info/index',
    //     children: [{
    //         path: 'index',
    //         name: '个人信息',
    //         meta: {
    //             i18n: 'info'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/user/info')
    //     }, {
    //         path: 'setting',
    //         name: '个人设置',
    //         meta: {
    //             i18n: 'setting'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/user/setting')
    //     }, {
    //         path: 'password',
    //         name: '修改密码',
    //         meta: {
    //             i18n: 'password'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/user/password')
    //     }]
    // }

    {
        path: '/#',
        component: Layout,
        children: [
            {
                path: '/changePasswd',
                name: "修改密码",
                meta: {
                    isTab: true
                },
                component: () => import(/* webpackChunkName: "views" */ '@/views/user/password')
            },
        ]
    },
    {
        path: '/#',
        component: Layout,
        children: [
            {
                path: '/wxgzh/user',
                name: "用户管理",
                meta: {
                    isTab: true
                },
                component: () => import(/* webpackChunkName: "views" */ '@/views/yonghu/index')
            },
        ]
    }
]
