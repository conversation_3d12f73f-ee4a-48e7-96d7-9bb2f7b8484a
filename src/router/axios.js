/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios'
import {serialize} from '@/util/util'
import {Message} from 'element-ui'
import website from '@/config/website';
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style

axios.defaults.timeout = 10000;
// 返回其他状态吗
axios.defaults.validateStatus = function (status) {
    return status >= 200 && status <= 500; // 默认的
};
// 跨域请求，允许保存cookie
axios.defaults.withCredentials = true;
// NProgress Configuration
NProgress.configure({
    showSpinner: false
});
// HTTPrequest拦截
axios.interceptors.request.use(config => {
    NProgress.start()
    // const meta = (config.meta || {});

    //headers中配置serialize为true开启序列化
    if (config.method === 'post' && config.headers['Content-Type'] === 'application/x-www-form-urlencoded; charset=UTF-8') {
        config.data = serialize(config.data);
    }
    return config
}, error => {
    return Promise.reject(error)
});
// HTTPresponse拦截
axios.interceptors.response.use(res => {
    NProgress.done();
    const status = Number(res.status) || 200;
    // console.log(status);
    const statusWhiteList = website.statusWhiteList || [];
    const message = res.data.info || '服务异常';
    //如果在白名单里则自行catch逻辑处理
    if (statusWhiteList.includes(status)) return Promise.reject(res);
    //如果是401则跳转到登录页面
    if (res.data && res.data.code === '401') {
        if (process.env.NODE_ENV === 'production') {
            window.location.href = '/logout'
        }
    } else if (res.data && res.data.code && res.data.code !== '00000' && res.data.code!=200) {
        Message({
            message: message,
            type: 'warning'
        });
    }
    // 如果请求为非200否者默认统一处理
    if (status !== 200) {
        Message({
            message: message,
            type: 'error'
        })
        return Promise.reject(new Error(message))
    }

    return res;
}, error => {
    NProgress.done();
    return Promise.reject(new Error(error));
})

export default axios;
