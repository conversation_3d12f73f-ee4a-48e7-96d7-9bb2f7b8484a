import router from './router/router'
import store from './store'
import { validatenull } from '@/util/validate'
import NProgress from 'nprogress' // progress bar
// import {toLogin, getPassToken} from '@/util/oauth';
// import {setStore, getStore, removeStore} from '@/util/store';

import 'nprogress/nprogress.css' // progress bar style
NProgress.configure({ showSpinner: false });
// const lockPage = store.getters.website.lockPage; //锁屏页
router.beforeEach((to, from, next) => {
  let meta = to.meta || {};
  const isMenu = meta.menu === undefined ? to.query.menu : meta.menu;
  store.commit('SET_IS_MENU', isMenu === undefined);

  const value = to.query.src || to.fullPath;
  const label = to.query.name || to.name;
  meta = to.meta || router.$avueRouter.meta || {};
  const i18n = to.query.i18n;
  if (meta.isTab !== false && !validatenull(value) && !validatenull(label)) {
    store.commit('ADD_TAG', {
      label: label,
      value: value,
      params: to.params,
      query: to.query,
      meta: (() => {
        if (!i18n) {
          return meta
        }
        return {
          i18n: i18n
        }
      })(),
      group: router.$avueRouter.group || []
    });
  }
  next()
  // }
  // } else {
  //   //判断是否需要认证，没有登录访问去登录页
  //   if (meta.isAuth === false) {
  //     if(!getToken() && getStore({name: "user-info"})) {
  //       console.log("删除user-info");
  //       removeStore({name: "user-info"})
  //     }
  //     next()
  //   } else {
  //     // window.location.href = "http://182.92.64.176/user/login?service=" + to.path
  //     // next('/login')
  //     toLogin(to.fullPath)
  //   }
  // }
})

router.afterEach(() => {
  NProgress.done();
  let title = store.getters.tag.label;
  // let i18n = store.getters.tag.meta.i18n;
  // title = router.$avueRouter.generateTitle(title, i18n)
  //根据当前的标签也获取label的值动态设置浏览器标题
  router.$avueRouter.setTitle(title);
});
