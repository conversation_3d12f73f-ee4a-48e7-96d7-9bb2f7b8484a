server:
  port: 80
  servlet:
    context-path: /
spring:
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: *****************************************
    username: syt_new_portal
    password: syt_new_portal
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 60000
      pool-name: DatebookHikariCP
      max-lifetime: 60000
      connection-timeout: 60000
      connection-test-query: SELECT * FROM DUAL
      validation-timeout: 3000
      login-timeout: 5
  thymeleaf:
    prefix: file:/opt/sso_server/web/templates/
    cache: false
    mode: HTML5
    encoding: utf-8
    suffix: .html
  session:
    store-type: redis
  redis:
    host: 127.0.0.1
    port: 6379
    password: freekeer!@#$%
    database: 3
  resources:
    static-locations: file:/opt/sso_server/web/static/
  mvc:
    static-path-pattern: /**
log:
  level: info
  path: logs/
  maxHistory: 1

