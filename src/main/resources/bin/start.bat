@echo off & setlocal enabledelayedexpansion
rem set JAVA_HOME=D:\Java\jdk1.8.0_171
rem set Classpath=%JAVA_HOME%\lib\tools.jar;%JAVA_HOME%\lib\dt.jar;
rem set Path=%JAVA_HOME%\bin;

cd ../config
for /f "eol=; tokens=2,2 delims==" %%i in ('findstr /i "prop.jvm.mem-opts" conf.properties') do set JVM_MEM_OPTS=%%i

set LIB_JARS=../config
cd ../lib
for %%i in (*) do set LIB_JARS=!LIB_JARS!;../lib/%%i

echo Starting the server [sanyth-portal] ......
java %JVM_MEM_OPTS% -classpath %LIB_JARS% -Dspring.config.location=../config/application.yml com.sanyth.portal.Main
echo The server [sanyth-portal] started.
pause