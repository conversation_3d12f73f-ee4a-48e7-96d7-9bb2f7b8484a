<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.auth.server.mapper.OauthClientDetailsMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="baseResultMap" type="com.sanyth.auth.server.model.OauthClientDetails">
		<id column="id" property="id" />
		<id column="client_id" property="clientId" />
		<result column="resource_ids" property="resourceIds" />
		<result column="client_secret" property="clientSecret" />
		<result column="scope" property="scope" />
		<result column="authorized_grant_types" property="authorizedGrantTypes" />
		<result column="web_server_redirect_uri" property="webServerRedirectUri" />
		<result column="authorities" property="authorities" />
		<result column="access_token_validity" property="accessTokenValidity" />
		<result column="refresh_token_validity" property="refreshTokenValidity" />
		<result column="additional_information" property="additionalInformation" />
		<result column="autoapprove" property="autoapprove" />
		<result column="client_name" property="clientName" />
		<result column="description" property="description" />
		<result column="create_time" property="createTime" />
		<result column="auth_type" property="authType" />
	</resultMap>

</mapper>
