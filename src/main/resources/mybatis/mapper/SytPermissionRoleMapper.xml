<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.auth.server.mapper.SytPermissionRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.sanyth.auth.server.model.SytPermissionRole">
        <id column="ID" property="id"/>
        <result column="ROLENAME" property="rolename"/>
        <result column="ROLEKEY" property="rolekey"/>
        <result column="ROLEINFO" property="roleinfo"/>
        <result column="ROLETYPE" property="roletype"/>
        <result column="MASTERID" property="masterid"/>
        <result column="OPDATE" property="opdate"/>
        <result column="AUTHSTRING" property="authstring"/>
        <result column="MOBILEAUTH" property="mobileauth"/>
    </resultMap>

    <select id="getByAccount" resultMap="baseResultMap">
		SELECT T.*
      FROM SYT_PERMISSION_ROLE T
     WHERE EXISTS
     (SELECT 1
              FROM SYT_PERMISSION_ACCOUNT_ROLE
             WHERE T.ID = ROLE_ID
               AND ACCOUNT_ID = #{accountId,jdbcType=VARCHAR})
	</select>
    <select id="queryMapGroupByAccount" resultType="java.util.Map">
        select ar.ACCOUNT_ID,r.ROLENAME from SYT_PERMISSION_ACCOUNT_ROLE ar left join SYT_PERMISSION_ROLE r on ar.ROLE_ID=r.ID
        <where>
            <if test="accountId!=null and accountId!=''">
                ACCOUNT_ID = #{accountId,jdbcType=VARCHAR})
            </if>
        </where>
    </select>
</mapper>
