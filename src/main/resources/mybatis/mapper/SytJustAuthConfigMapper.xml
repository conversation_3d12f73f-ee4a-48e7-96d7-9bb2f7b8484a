<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.auth.server.mapper.SytJustAuthConfigMapper">
    <resultMap id="baseResultMap" type="com.sanyth.auth.server.model.SytJustAuthConfig">
        <id column="ID" property="id"/>
        <result column="AUTHTYPE" property="authType"/>
        <result column="CLIENTID" property="clientId"/>
        <result column="CLIENTSECRET" property="clientSecret"/>
        <result column="REDIRECTURI" property="redirectUri"/>
        <result column="ALIPAYPUBLICKEY" property="alipayPublicKey"/>
        <result column="AGENTID" property="agentId"/>
        <result column="BZ" property="bz"/>
        <result column="CREATEDATE" property="createDate"/>
        <result column="MODIFYDATE" property="modifyDate"/>
    </resultMap>

</mapper>
