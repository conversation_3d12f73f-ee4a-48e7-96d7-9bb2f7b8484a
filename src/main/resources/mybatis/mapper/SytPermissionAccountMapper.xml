<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.auth.server.mapper.SytPermissionAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.sanyth.auth.server.model.SytPermissionAccount">
        <result column="ID" property="id"/>
        <result column="HUMANCODE" property="humancode"/>
        <result column="HUMANSMSCODE" property="humansmscode"/>
        <result column="HUMANNAME" property="humanname"/>
        <result column="HUMANDESCRIPTION" property="humandescription"/>
        <result column="CREATEDATE" property="createdate"/>
        <result column="VALIDFROMDATE" property="validfromdate"/>
        <result column="VALIDTODATE" property="validtodate"/>
        <result column="VALIDFLAG" property="validflag"/>
        <result column="HUMANPASSWORD" property="humanpassword"/>
        <result column="SEX" property="sex"/>
        <result column="BIRTHDAY" property="birthday"/>
        <result column="TELOFFICE" property="teloffice"/>
        <result column="TELHOME" property="telhome"/>
        <result column="TELMOBILE1" property="telmobile1"/>
        <result column="TELMOBILE2" property="telmobile2"/>
        <result column="EMAIL" property="email"/>
        <result column="ADDRESS" property="address"/>
        <result column="POSTALCODE" property="postalcode"/>
        <result column="AGE" property="age"/>
        <result column="ORGID" property="orgid"/>
        <result column="SIGNATURE" property="signature"/>
        <result column="ENCRYPTYPE" property="encryptype"/>
        <result column="IDCODE" property="idcode"/>
        <result column="IDTYPE" property="idtype"/>
        <result column="LOGINTIME" property="logintime"/>
        <result column="LOGININFO" property="logininfo"/>
        <result column="DUTYID" property="dutyid"/>
        <result column="HUMANNUMBER" property="humannumber"/>
        <result column="DISPLAYORDER" property="displayorder"/>
        <result column="ORGANIZATIONNAMES" property="organizationnames"/>
        <result column="ACTIVEFLAG" property="activeflag"/>
        <result column="EMPLOYEETYPE" property="employeeType"/>
        <result column="CURRENTSTATE" property="currentState"/>
    </resultMap>

    <sql id="base_columns">
		t.ID,
		t.HUMANCODE,
		t.HUMANSMSCODE,
		t.HUMANNAME,
		t.HUMANDESCRIPTION,
		t.CREATEDATE,
		t.VALIDFROMDATE,
		t.VALIDTODATE,
		t.VALIDFLAG,
		t.SEX,
		t.BIRTHDAY,
		t.TELOFFICE,
		t.TELHOME,
		t.TELMOBILE1,
		t.TELMOBILE2,
		t.EMAIL,
		t.ADDRESS,
		t.POSTALCODE,
		t.AGE,
		t.ORGID,
		t.SIGNATURE,
		t.ENCRYPTYPE,
		t.IDCODE,
		t.IDTYPE,
		t.LOGINTIME,
		t.LOGININFO,
		t.DUTYID,
		t.HUMANNUMBER,
		t.DISPLAYORDER,
		t.ORGANIZATIONNAMES,
		t.ACTIVEFLAG,
        t.EMPLOYEETYPE,
        t.CURRENTSTATE
	</sql>

    <select id="queryList" resultMap="baseResultMap">
        select
        <include refid="base_columns"/>
        from SYT_PERMISSION_ACCOUNT t
        <where>
            id != 'syt_visitor'
            <if test="map.roleId!=null and map.roleId!=''">
                and exists(select 1 from SYT_PERMISSION_ACCOUNT_ROLE ar
                where ar.account_id=t.id
                and ar.role_id=#{map.roleId, jdbcType=VARCHAR})
            </if>
            <if test="map.organizationnames!=null and map.organizationnames!=''">
                and exists(select 1 from SYT_SYS_ORGANIZATION_USER ou
                where ou.user_id=t.id
                and ou.organization_id in (#{map.organizationnames}))
            </if>
            <if test="map.humancode!=null and map.humancode!=''">
                <!--and t.humancode=#{humancode,jdbcType=VARCHAR}-->
                and t.humancode like concat(concat('%', #{map.humancode}), '%')
            </if>
            <if test="map.telmobile1!=null and map.telmobile1!=''">
                <!--and t.humancode=#{humancode,jdbcType=VARCHAR}-->
                and t.telmobile1 like concat(concat('%', #{map.telmobile1}), '%')
            </if>
            <if test="map.humanname!=null and map.humanname!=''">
                and t.humanname like concat(concat('%', #{map.humanname}), '%')
            </if>
            <if test="map.sex!=null and map.sex!=''">
                and t.sex=#{map.sex,jdbcType=VARCHAR}
            </if>
            <if test="map.validflag!=null">
                and t.validflag=#{map.validflag,jdbcType=VARCHAR}
            </if>
            <if test="map.idcode!=null and map.idcode!=''">
                and t.idcode like concat(concat('%', #{map.idcode}), '%')
            </if>
            <if test="map.employeeType!=null and map.employeeType!=''">
                and t.employeeType in
                <foreach collection="map.employeeType.split(',')" item="item" index="index" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <if test="map.currentState!=null and map.currentState!=''">
                and t.currentState in
                <foreach collection="map.currentState.split(',')" item="item" index="index" open="(" separator="," close=")">
                    '${item}'
                </foreach>
            </if>
            <!--账号审计类型-->
            <include refid="zhsjlxWhere" />
        </where>
        ORDER BY DISPLAYORDER ASC
    </select>

    <!--账号审计类型对应的查询条件-->
    <sql id="zhsjlxWhere">
        <!--90天以上未登录人数-->
        <if test="map.zhsjlx=='wdlrs'">
            and t.logintime is null or t.logintime &lt; (sysdate-90)
        </if>
        <!--90天以上未修改密码人数-->
        <if test="map.zhsjlx=='wxgmmrs'">
            and t.modifypasstime is null or t.modifypasstime &lt; (sysdate-90)
        </if>
        <!--账号过期人数-->
        <if test="map.zhsjlx=='zhgqrs'">
            and t.validtodate &lt; sysdate
        </if>
        <!--账号未绑定组织人数-->
        <if test="map.zhsjlx=='wbdzzrs'">
            and not exists (select 1 from SYT_SYS_ORGANIZATION_USER u where t.id=u.user_id)
        </if>
        <!--账号未分配角色人数-->
        <if test="map.zhsjlx=='wfpjsrs'">
            and not exists (select 1 from SYT_PERMISSION_ACCOUNT_ROLE u where t.id=u.ACCOUNT_ID)
        </if>
    </sql>
    <select id="queryNumberByZhsjlx" resultType="java.util.Map">
        select count(*) as ${map.zhsjlx} from SYT_PERMISSION_ACCOUNT t
        <where>
            id != 'syt_visitor'
            <include refid="zhsjlxWhere" />
        </where>
    </select>
</mapper>
