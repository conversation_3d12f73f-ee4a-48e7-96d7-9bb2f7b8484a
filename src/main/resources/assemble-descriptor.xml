<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>bin</id>
	<formats>
		<format>tar.gz</format>
		<format>zip</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>src/main/resources/bin</directory>
			<outputDirectory>sso_server/bin</outputDirectory>
			<includes>
				<include>*</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>true</filtered>
		</fileSet>
		<fileSet>
			<directory>src/main/resources/</directory>
			<outputDirectory>sso_server/conf</outputDirectory>
			<includes>
				<include>application.yml</include>
				<include>application-${active.properties}.yml</include>
				<include>logback-spring.xml</include>
				<include>conf.properties</include>
				<include>__RSA_PAIR</include>
				<include>ip2region.db</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>true</filtered>
		</fileSet>
		<fileSet>
			<directory>src/main/resources/static</directory>
			<outputDirectory>sso_server/web/static</outputDirectory>
			<includes>
				<include>**/*.*</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>false</filtered>
		</fileSet>
		<fileSet>
			<directory>src/main/resources/templates</directory>
			<outputDirectory>sso_server/web/templates</outputDirectory>
			<includes>
				<include>**/*.*</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>false</filtered>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<outputDirectory>sso_server/lib</outputDirectory>
			<scope>runtime</scope>
		</dependencySet>
	</dependencySets>
</assembly>