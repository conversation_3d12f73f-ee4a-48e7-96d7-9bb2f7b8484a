server:
  port: 8090
  servlet:
    context-path: /
spring:
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: ***************************************
    username: syt_oauth
    password: syt_2020_oauth
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 60000
      pool-name: DatebookHikariCP
      max-lifetime: 60000
      connection-timeout: 60000
      connection-test-query: SELECT * FROM DUAL
      validation-timeout: 3000
      login-timeout: 5
  thymeleaf:
#    prefix: file:D:\JavaCode\IdeaProjects\git-sanyth-sso-server\src\main\resources\templates\
    cache: false
    mode: HTML5
    encoding: utf-8
    suffix: .html
  session:
    store-type: redis
  redis:
    host: ***************
    database: 0
    port: 6379
    password: sanyth!@#$%
  data:
    mongodb:
      uri: **************************************************************
  resources:
    static-locations: classpath:/static
  mvc:
    static-path-pattern: /**
  ldap:
    urls: ldap://***************:389
    base: dc=bistu,dc=cn
    username: cn=admin,dc=bistu,dc=cn
    password: Xxkdldap123%

log:
  level: info
  path: logs/
  maxHistory: 1
