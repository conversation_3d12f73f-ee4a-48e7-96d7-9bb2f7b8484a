<!DOCTYPE html>
<html lang="zh-cmn-Hans" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>绑定管理</title>
    <link rel="stylesheet" href="/manage/platform/wei_xin/web_auth/css/weui.min.css">
    <link rel="stylesheet" href="/manage/platform/wei_xin/web_auth/css/weui_example.css">
    <link rel="stylesheet" href="/manage/platform/wei_xin/web_auth/css/common.css">
</head>
<body>
<div class="container">
    <div class="page msg_text_primary js_show">
        <div class="weui-msg">
            <div class="weui-msg__icon-area">
                <img class="header-img" th:if="${#strings.isEmpty(username)}"
                     src="/manage/platform/wei_xin/web_auth/img/bg3.png"
                     alt=""/>
                <img class="header-img" th:unless="${#strings.isEmpty(username)}"
                     src="/manage/platform/wei_xin/web_auth/img/bg2.png"
                     alt=""/>
            </div>
            <div class="weui-msg__text-area">
                <h2 class="weui-msg__title" th:if="${#strings.isEmpty(username)}">未绑定</h2>
                <!--                    <p class="weui-msg__desc">未绑定</p>-->
                <h2 class="weui-msg__title" th:unless="${#strings.isEmpty(username)}">已绑定</h2>
                <!--                    <p class="weui-msg__desc">当前账号已绑定</p>-->
                <p class="weui-msg__desc-primary" th:unless="${#strings.isEmpty(username)}">用户名：<span
                        style="color: #2D8CF0;" th:text="${username}"></span></p>
            </div>
            <div class="weui-msg__opr-area">
                <p class="weui-btn-area" th:if="${#strings.isEmpty(username)}">
                    <a href="/platform/wx/webAuthed/bindResult"
                       role="button" class="weui-btn weui-btn_primary">登录账号并绑定</a>
                </p>
                <p class="weui-btn-area" th:unless="${#strings.isEmpty(username)}">
                    <!--                    <a href="/noAuth/platform/wx/webAuth/authenticateResult"-->
                    <!--                       role="button" class="weui-btn weui-btn_primary">进入系统</a>-->
                    <a th:if="${confirmBtnOn}" href="/noAuth/platform/wx/webAuth/auth_confirmed" role="button"
                       class="weui-btn weui-btn_primary">进入系统</a>
                    <a href="/noAuth/platform/wx/webAuth/unbindResult"
                       role="button" class="weui-btn weui-btn_default">解除绑定</a>
                </p>
            </div>
            <div class="weui-msg__tips-area" th:if="${#strings.isEmpty(username)}">
                <p class="weui-msg__tips">首次登录并绑定账号，可方便您下次免登录访问本服务，也可随时解绑。</p>
            </div>
            <div class="weui-msg__extra-area">
                <div class="weui-footer">
                    <p class="weui-footer__text" th:text="${openId}"></p>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>