<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>
    <title>认证平台-登录授权</title>
    <link rel="stylesheet" type="text/css" href="/manage/css/bootstrap.min.3.3.7.css" th:href="@{/manage/css/bootstrap.min.3.3.7.css}"/>
    <script type="text/javascript" charset="utf-8" src="/manage/js/bootstrap.min.js" th:src="@{/manage/js/bootstrap.min.js}"></script>
</head>

<body>
<nav class="navbar navbar-default container-fluid">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand" href="#">认证平台登录授权</a>
        </div>
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-5">
            <p class="navbar-text navbar-right">
<!--                <a href="#" th:href="@{/login}">登陆</a>-->
                <!--                <a href="#">授权管理</a>-->
                <!--                <a href="#">申请接入</a>-->
            </p>
        </div>
    </div>
</nav>
<div style="padding-top: 30px;width: 300px; color: #555; margin:0px auto;">
    <form id='confirmationForm' name='confirmationForm' th:action="@{/oauth/authorize}" method='post'>
        <input name='user_oauth_approval' value='true' type='hidden' />
        <input name='authorize' value='Authorize' type='hidden' />
        <p><a th:href="${app.clientIndex}" target="_blank" th:text="${app.clientName}">应用</a> 请求授权登录：</p>
        <ul class="list-group" >
            <li class="list-group-item"> <span >
                <input type="hidden"  th:name="'scope.'+${s}" value="true"  th:each="s : ${scopes}" />
                <input type="checkbox" disabled checked="checked"  th:each="s : ${scopes}" />
                <label th:text="登录"  th:each="s : ${scopes}"></label>
            </span></li>
        </ul>
        <p class="help-block">授权后表明你已同意</p>
        <button  class="btn btn-success pull-right" type="submit" id="write-email-btn">授权</button></p>
    </form>
</div>
</body>
</html>