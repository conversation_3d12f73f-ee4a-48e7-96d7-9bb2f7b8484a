<%@ page language="java" pageEncoding="UTF-8" contentType="text/html; charset=UTF-8"%>
<%@page import="cn.sanyth.xggl.xsxx.util.AESUtil"%>
<%
	String uid = (String) request.getSession().getAttribute("_currentUsername");
	if (uid != null && !"".equals(uid)) {
		System.out.println("[INFO] uid:" + uid);
		try {
			String initVector = AESUtil.encryptKey();
			String encryptKey = AESUtil.encryptKey();
			session.setAttribute(AESUtil.AES_INIT_VECTOR, initVector);
			session.setAttribute(AESUtil.AES_ENCRYPT_KEY, encryptKey);
			uid = AESUtil.encrypt(uid, initVector, encryptKey);
		} catch (Exception e) {
			System.out.println("[ERROR] uid:" + uid);
		}
	}
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta http-equiv="pragma" content="no-cache" />
		<meta http-equiv="cache-control" content="no-cache" />
		<meta http-equiv="expires" content="0" />

		<title>单点系统_登录</title>

		<script type="text/javascript" src="${basePath}core/boot.js"></script>
		<script type="text/javascript">
			var form = null;
			var uid = '<%=uid%>';
			$(function(){
				mini.parse();
				form = new mini.Form("form");
				console.log(uid)
				if (uid && uid != 'null') {
					form.loading("加载中，请稍候...");
					$.ajax({
						url: '${basePath}caswisedu/login.htm',
						data: {
							uid: '<%=uid%>'
						},
						cache: false,
						type: 'post',
						dataType: 'html',
						success: function (result) {
							form.unmask();
							if (typeof (result) == 'string') {
								result = mini.decode(result);
							}

							if (result.success) {
								//top.location = '${basePath}user/index.htm';
								var url = '<%=request.getParameter("url")%>' == 'null' ? '' : '<%=request.getParameter("url")%>';
								var tabName = '<%=request.getParameter("tabName")%>' == 'null' ? '' : '<%=request.getParameter("tabName")%>';
								top.location = "${basePath}toIndex.htm?newTab=" + url + "&tabName=" + tabName;
							} else {
								alert("用户不存在或已停用");
							}
						},
						error: function (jqXHR, textStatus, errorThrown) {
							form.unmask();
							mini.alert(jqXHR.responseText);
						}
					});
				} else {
					top.location = "${basePath}/wiseduIndex.jsp";
				}
			});
		</script>
	</head>

	<body id="form">
	</body>
</html>