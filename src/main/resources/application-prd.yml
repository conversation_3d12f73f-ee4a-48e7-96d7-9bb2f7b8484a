server:
  port: 8282
  servlet:
    context-path: /
spring:
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: *****************************************
    username: syt_new_portal
    password: syt_new_portal
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 60000
      pool-name: DatebookHikariCP
      max-lifetime: 60000
      connection-timeout: 60000
      connection-test-query: SELECT * FROM DUAL
      validation-timeout: 3000
      login-timeout: 5
  thymeleaf:
    prefix: classpath:/web/templates/
    cache: false
    mode: HTML5
    encoding: utf-8
    suffix: .html
  session:
    store-type: redis
  redis:
    host: **************
    port: 6379
    password: sanyth123456
    database: 1
  data:
    mongodb:
      uri: ******************************************
  resources:
    static-locations: classpath:/web/static/
  mvc:
    static-path-pattern: /**
  ldap:
    urls: ldap://**************:389
    base: dc=sanyth,dc=cn
    username: cn=admin,dc=sanyth,dc=cn
    password: 123456
log:
  level: info
  path: logs/
  maxHistory: 1

