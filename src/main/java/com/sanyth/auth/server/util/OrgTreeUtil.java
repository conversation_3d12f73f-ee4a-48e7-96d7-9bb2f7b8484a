package com.sanyth.auth.server.util;

import com.sanyth.auth.server.core.common.SpringTool;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.service.ISytSysOrganizationService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class OrgTreeUtil {

    public static List<SytSysOrganization> buildLazyTreeList(List<SytSysOrganization> alllist, SytSysOrganization organization) throws Exception {
        List<SytSysOrganization> parentlist = new ArrayList<>();
        if (StringUtils.isEmpty(organization.getParent())) {
            for (SytSysOrganization r : alllist) {
                if (StringUtils.isBlank(r.getParent())) {
                    SytSysOrganization vo = new SytSysOrganization();
                    BeanUtils.copyProperties(r, vo);
                    parentlist.add(vo);
                }
            }
        } else {
            parentlist = alllist;
        }
        ISytSysOrganizationService organizationService = (ISytSysOrganizationService) SpringTool.getApplicationContext().getBean("sytSysOrganizationService");
        SytSysOrganization sysOrganization;
        for (SytSysOrganization root : parentlist) {
//            List<SytSysOrganization> childList = getChildList(root.getId(), alllist);
            sysOrganization = new SytSysOrganization();
            sysOrganization.setParent(root.getId());
            List<SytSysOrganization> childList = organizationService.queryList(sysOrganization);
            if (CollectionUtils.isNotEmpty(childList)) {
                root.setHasChildren(true);
//                root.setChildren(childList);
            }
        }
        return parentlist;
    }

    public static List<SytSysOrganization> buildTreeList(List<SytSysOrganization> alllist) throws Exception {
        List<SytSysOrganization> parentlist = new ArrayList<>();
        for (SytSysOrganization r : alllist) {
            if (StringUtils.isBlank(r.getParent())) {
                SytSysOrganization vo = new SytSysOrganization();
                BeanUtils.copyProperties(r, vo);
                parentlist.add(vo);
            }
        }
        for (SytSysOrganization root : parentlist) {
            List<SytSysOrganization> childList = getChildList(root.getId(), alllist);
            if (CollectionUtils.isNotEmpty(childList)) {
                root.setChildren(childList);
            }
        }
        return parentlist;
    }

    private static List<SytSysOrganization> getChildList(String id, List<SytSysOrganization> alllist) throws Exception {
        List<SytSysOrganization> childList = new ArrayList<>();
        for (SytSysOrganization child : alllist) {
            if (StringUtils.isNotEmpty(child.getParent()) && child.getParent().equals(id)) {
                SytSysOrganization vo = new SytSysOrganization();
                BeanUtils.copyProperties(child, vo);
                childList.add(vo);
            }
        }

        for (SytSysOrganization child : childList) {
            List<SytSysOrganization> list = getChildList(child.getId(), alllist);
//            if (CollectionUtils.isNotEmpty(list))
            child.setChildren(list);
        }
        return childList;
    }
}
