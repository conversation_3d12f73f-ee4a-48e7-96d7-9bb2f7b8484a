package com.sanyth.auth.server.util;


import cn.hutool.core.util.StrUtil;
import com.sanyth.auth.server.core.common.Constants;
import com.sanyth.auth.server.model.SytPermissionAccount;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 安全工具类
 *
 */
@UtilityClass
public class SecurityUtils {
	/**
	 * 获取Authentication
	 */
	public Authentication getAuthentication() {
		return SecurityContextHolder.getContext().getAuthentication();
	}

	/**
	 * 获取用户
	 *
	 * @param authentication
	 * <p>
	 */
	public SytPermissionAccount getUser(Authentication authentication) {
		Object principal = authentication.getPrincipal();
		if (principal instanceof SytPermissionAccount) {
			return (SytPermissionAccount) principal;
		}
		return null;
	}

	/**
	 * 获取用户
	 */
	public SytPermissionAccount getUser() {
		Authentication authentication = getAuthentication();
		return authentication == null ? null : getUser(authentication);
	}

	/**
	 * 获取用户角色信息
	 *
	 * @return 角色集合
	 */
	public List<Integer> getRoles() {
		Authentication authentication = getAuthentication();
		Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

		List<Integer> roleIds = new ArrayList<>();
		authorities.stream()
				.filter(granted -> StrUtil.startWith(granted.getAuthority(), Constants.ROLE_DEF))
				.forEach(granted -> {
					String id = StrUtil.removePrefix(granted.getAuthority(), Constants.ROLE_DEF);
					roleIds.add(Integer.parseInt(id));
				});
		return roleIds;
	}

}
