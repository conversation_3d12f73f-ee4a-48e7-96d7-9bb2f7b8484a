package com.sanyth.auth.server.util;

import cn.hutool.core.collection.CollUtil;
import com.sanyth.auth.server.core.exception.BusinessException;
import com.sanyth.auth.server.model.SytPermissionAccount;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 校验工具类
 */
public class ValidationUtils {

    private static final Pattern PATTERN_URL = Pattern.compile("^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]");

    private static final Pattern PATTERN_XML_NCNAME = Pattern.compile("[a-zA-Z_][\\-_.0-9_a-zA-Z$]*");
    //身份证号校验
    private static final Pattern PATTERN_IDCARD = Pattern.compile("(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)");
    //手机校验
    private static final Pattern PATTERN_MOBILE = Pattern.compile("^1[3|4|5|7|8][0-9]\\d{8}$");
    //邮箱校验
    private static final Pattern PATTERN_EMAIL = Pattern.compile("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$");


    public static boolean isIDCard(String idCard) {
        return StringUtils.hasText(idCard)
                && PATTERN_IDCARD.matcher(idCard).matches();
    }

    public static boolean isMobile(String mobile) {
        return StringUtils.hasText(mobile)
                && PATTERN_MOBILE.matcher(mobile).matches();
    }

    public static boolean isEmail(String email) {
        return StringUtils.hasText(email)
                && PATTERN_EMAIL.matcher(email).matches();
    }

    public static boolean isURL(String url) {
        return StringUtils.hasText(url)
                && PATTERN_URL.matcher(url).matches();
    }

    public static boolean isXmlNCName(String str) {
        return StringUtils.hasText(str)
                && PATTERN_XML_NCNAME.matcher(str).matches();
    }

    public static void validate(SytPermissionAccount account) {
        if(!isIDCard(account.getIdcode())){
            throw new BusinessException("身份证号格式不正确");
        }
        if(!isMobile(account.getTelmobile1())){
            throw new BusinessException("手机号格式不正确");
        }
        if(!isEmail(account.getEmail())){
            throw new BusinessException("邮箱格式不正确");
        }
    }

    public static void validate(Validator validator, Object object, Class<?>... groups) {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (CollUtil.isNotEmpty(constraintViolations)) {
            throw new ConstraintViolationException(constraintViolations);
        }
    }

}
