package com.sanyth.auth.server.pojo;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.ArrayList;
import java.util.List;

public class AccountListener extends AnalysisEventListener<Account> {

    List<Account> list = new ArrayList<>();

    @Override
    public void invoke(Account account, AnalysisContext analysisContext) {
        account.setRowIndex(analysisContext.readRowHolder().getRowIndex());
        list.add(account);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
//        System.out.println("解析完成");
//        System.out.println(JSON.toJSONString(list));
    }
}
