package com.sanyth.auth.server.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sanyth.auth.server.model.Resource;

import java.util.List;

@JsonIgnoreProperties({"description"})
public class ResourceResult extends Resource {
    private List<ResourceResult> children;

    public List<ResourceResult> getChildren() {
        return children;
    }

    public void setChildren(List<ResourceResult> children) {
        this.children = children;
    }
}
