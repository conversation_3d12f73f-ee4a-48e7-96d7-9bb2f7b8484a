package com.sanyth.auth.server.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

public class ImportErrorInfo {
    /**
     * 错误行号
     */
    @ExcelProperty(value = "行号", index = 0)
    private Integer rowIndex;
    /**
     * 错误值
     */
    @ExcelProperty(value = "错误值", index = 1)
    private String value;
    /**
     * 错误描述
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "错误描述", index = 2)
    private String info;

    public ImportErrorInfo(Integer rowIndex, String value, String info) {
        this.rowIndex = rowIndex;
        this.value = value;
        this.info = info;
    }

    public Integer getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
