package com.sanyth.auth.server.pojo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

@Data
public class Account {

    @ExcelIgnore
    private Integer rowIndex;
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "学工/工号" ,index = 0)
    private String humancode;
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "真实姓名" ,index = 1)
    private String humanname;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "性别(female:女,male:男)" ,index = 2)
    private String sex;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "组织机构" ,index = 3)
    private String organizationnames;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "手机号" ,index = 4)
    private String telmobile1;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "人员类别" ,index = 5)
    private String employeeType;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "证件号码" ,index = 6)
    private String idcode;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "开始时间" ,index = 7)
    private Date validfromdate;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "结束时间" ,index = 8)
    private Date validtodate;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "人员状态(0.在校1.离校)" ,index = 9)
    private Double validflag;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "角色" ,index = 10)
    private String role;

}
