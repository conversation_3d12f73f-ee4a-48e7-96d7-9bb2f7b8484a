package com.sanyth.auth.server.web;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytJustAuthUser;
import com.sanyth.auth.server.service.ISytPermissionAccountService;
import com.sanyth.auth.server.service.SytJustAuthUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;


/**
 * 用第三方平台登录用户信息
 */
@Controller
@RequestMapping("/sytJustAuthUser")
public class SytJustAuthUserController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytJustAuthUserController.class);
    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;
    @Resource
    private SytJustAuthUserService sytJustAuthUserService;


    /**
     * @param baseQuery
     * @return
     */
    @PostMapping("/queryPage")
    @ResponseBody
    public Resp list(@RequestBody BaseQuery<SytJustAuthUser> baseQuery, HttpServletRequest request) {
        Page<SytJustAuthUser> page = sytJustAuthUserService.queryPage(baseQuery);
        return Resp.success(page);
    }
    // 查询当前用户的数据
    @PostMapping("/getCurrentData")
    @ResponseBody
    public Resp getCurrentData(@RequestBody SytJustAuthUser baseQuery, HttpServletRequest request) {
        baseQuery.setHumancode(currentUser(request).getHumancode());
        List<SytJustAuthUser> list = sytJustAuthUserService.queryList(baseQuery);
        return Resp.success(list);
    }

    @PostMapping("/get")
    @ResponseBody
    public Resp get(String id) {
        SytJustAuthUser account = sytJustAuthUserService.getById(id);
        return Resp.success(account);
    }

//    @PostMapping("/edit")
//    @ResponseBody
//    public Resp edit(@RequestBody JSONObject params) throws Exception {
//        sytJustAuthUserService.edit(params);
//        return Resp.success();
//    }

    @PostMapping("/delete")
    @ResponseBody
    public Resp delete(@RequestBody JSONObject param) throws Exception {
        if(StringUtils.isBlank(param.getString("id"))){
            return Resp.error(ErrorInfo.MSG_0003);
        }
        sytJustAuthUserService.delete(param.getString("id"));
        return Resp.success();
    }

    @RequestMapping("/downImportTemplate")
    public void downImportTemplate(HttpServletResponse response) {
        try {
            String fileName = URLEncoder.encode("导入模板", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), SytJustAuthUser.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("sheet1")
                    .doWrite(new ArrayList());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
