package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.SytPermissionRole;
import com.sanyth.auth.server.service.ISytPermissionRoleService;
import com.sanyth.auth.server.service.RoleResourceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * ${table.comment}前端控制器
 * Created by JIANGPING on 2020-05-15.
 */
@RestController
@RequestMapping("/role")
public class SytPermissionRoleController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytPermissionRoleController.class);
    @javax.annotation.Resource
    private ISytPermissionRoleService iSytPermissionRoleService;
    @javax.annotation.Resource
    private RoleResourceService roleResourceService;

    /**
     * 分页查询SytPermissionRole信息
     *
     * @param query
     * @return
     */
    @PostMapping("/queryPage")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询角色信息",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#query.queryParam}}"
    )
    public Resp queryPage(BaseQuery<SytPermissionRole> query) {
        Page<SytPermissionRole> sytPermissionRolePage = iSytPermissionRoleService.queryPage(query);
        return Resp.success(sytPermissionRolePage);
    }

    @PostMapping("/list")
    public Resp list() {
        List<SytPermissionRole> list = iSytPermissionRoleService.list(new QueryWrapper<>());
        List<Map<String, Object>> newlist = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(r -> {
                Map<String, Object> m = new HashMap<>();
                m.put("id", r.getId());
                m.put("rolename", r.getRolename());
                newlist.add(m);
            });
        }
        return Resp.success(newlist);
    }

    @PostMapping("/listManage")
    public Resp listManage() {
        List<SytPermissionRole> rolePermissonResults = iSytPermissionRoleService.listManage();
        return Resp.success(rolePermissonResults);
    }

    /**
     * 编辑SytPermissionRole信息
     *
     * @param permissonParam
     * @return
     */
    @PostMapping("/edit")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "编辑角色信息",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#permissonParam}}"
    )
    public Resp edit(@RequestBody SytPermissionRole permissonParam) {
        iSytPermissionRoleService.edit(permissonParam);
        roleResourceService.updatePermission(); // 更新缓存
        return Resp.success();
    }

    /**
     * 删除SytPermissionRole信息
     *
     * @param param
     * @return
     */
    @PostMapping("/delete")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "删除角色信息",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp delete(@RequestBody Map<String, String> param) {
        String id = param.get("id");
        if (StringUtils.isBlank(id)) {
            return Resp.error(ErrorInfo.MSG_0003);
        }
        iSytPermissionRoleService.removeByIds(Arrays.asList(id));
        roleResourceService.updatePermission(); // 更新缓存
        return Resp.success();
    }

}
