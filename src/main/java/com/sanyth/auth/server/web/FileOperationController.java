package com.sanyth.auth.server.web;

import com.sanyth.auth.server.core.common.FileInfo;
import com.sanyth.auth.server.core.common.FileOperationUtil;
import com.sanyth.auth.server.core.common.Resp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by JIANGPING on 2020/5/26.
 */
@Controller
@RequestMapping("/file")
public class FileOperationController {

    @Autowired
    FileOperationUtil fileOperationUtil;

    @RequestMapping("/upload")
    @ResponseBody
    public Resp upload(@RequestParam("file") MultipartFile...file) throws Exception {
        FileInfo info = fileOperationUtil.save(file);
        return Resp.success(info);
    }

    @RequestMapping("/view/{id}")
    public void view(@PathVariable("id") String id,
                     HttpServletRequest request,
                     HttpServletResponse response) throws Exception {
        fileOperationUtil.download(id, true, request, response);
    }

    @RequestMapping("/download/{id}")
    public void download(@PathVariable("id") String id,
                         HttpServletRequest request,
                         HttpServletResponse response) throws Exception {
        fileOperationUtil.download(id, false, request, response);
    }

    @RequestMapping("/remove/{id}")
    @ResponseBody
    public Resp delete(@PathVariable("id") String id) throws Exception {
        fileOperationUtil.remove(id);
        return Resp.success();
    }
}
