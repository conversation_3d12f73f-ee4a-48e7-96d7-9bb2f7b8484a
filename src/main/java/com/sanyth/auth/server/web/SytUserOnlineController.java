package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.LoginLogs;
import com.sanyth.auth.server.pojo.SytUserOnline;
import com.sanyth.auth.server.service.LoginLogsService;
import com.sanyth.auth.server.util.PageResult;
import com.sanyth.auth.server.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.session.data.redis.RedisIndexedSessionRepository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线用户监控
 * 
 */
@RestController
@RequestMapping("/monitor/online")
public class SytUserOnlineController extends BaseController {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private LoginLogsService loginLogsService;

    /**
     * 用于超管的在线监控,如果要个人监控，需要判断sessionAttr:SPRING_SECURITY_CONTEXT值中包含个人的才显示，或者改造为别的认证方式，例如jwt，自定义token等
     * @param query
     * @return {@link Resp}
     */
    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<SytUserOnline> query) {
        RedisIndexedSessionRepository sessionRepository = SpringTool.getBean(RedisIndexedSessionRepository.class);
//        if (sessionRepository == null) {
//            return Resp.error();
//        }
//        Set<Object> keys = sessionRepository.getSessionRedisOperations().keys(Constants.USER_ONLINE_REDIS_KEY + Constants.SYMBOL_STAR);
//        List<Map<String,Object>> list =  new ArrayList<>();
//        Object[] arr = {"lastAccessedTime","maxInactiveInterval","creationTime","sessionAttr:SPRING_SECURITY_CONTEXT"};
//        keys.stream().forEach(item->{
//            if(((String)item).indexOf("expires")==-1){//排除过期信息
//                List<Object> values  = sessionRepository.getSessionRedisOperations().opsForHash().multiGet(item, Arrays.asList(arr));
//                Map<String, Object> re = new HashMap<>();
////                re.put("lastAccessedTime",values.get(0));
////                re.put("maxInactiveInterval",values.get(1));
////                re.put("creationTime",values.get(2));
//                SytUserOnline userOnline = parseUserInfo(values.get(3));
//                userOnline.setTokenId(parseUserToken((String)item));
//                list.add(re);
//            }
//        });
        SytUserOnline param = query.getQueryParam();
        List<SytUserOnline> userOnlineDTOList = new ArrayList<>();
        PageResult<String> keys = redisUtil.findKeysForPage(Constants.USER_ONLINE_REDIS_KEY + Constants.SYMBOL_STAR, 1, Integer.MAX_VALUE);
        List<String> rows = keys.getRows();
        Long total = keys.getTotal();
        Object[] arr = {"lastAccessedTime","maxInactiveInterval","creationTime","sessionAttr:SPRING_SECURITY_CONTEXT"};
        rows.stream().forEach(item->{
            if(item.indexOf("expires")==-1){//排除过期信息
                List<Object> values  = sessionRepository.getSessionRedisOperations().opsForHash().multiGet(item, Arrays.asList(arr));
//                System.out.println("maxInactiveInterval:" + values.get(1));
                Object val = values.get(3);
                if(val != null && val instanceof SecurityContextImpl){
                    SytUserOnline userOnline = parseUserInfo(val);
                    userOnline.setTokenId(parseUserToken(item));
                    if (StringUtils.isEmpty(param.getUserName()) || Objects.equals(userOnline.getUserName(), param.getUserName())) {
                        userOnlineDTOList.add(userOnline);
                    }
                }
            }
        });
        Collections.reverse(userOnlineDTOList);
        // 创建分页帮助对象
        ManualPaginationHelper<SytUserOnline> paginationHelper = new ManualPaginationHelper<>();
        List<SytUserOnline> paginatedList = paginationHelper.paginate(userOnlineDTOList, query.getPage(), query.getPageSize());
        List<String> tokenIds = paginatedList.stream().map(SytUserOnline::getTokenId).collect(Collectors.toList());
        LambdaQueryWrapper<LoginLogs> lambdaQueryWrapper = Wrappers.lambdaQuery();
//        logsQueryWrapper.in("token_id", tokenIds);
        try {
            MybatisParameterUtils.cutInParameter(lambdaQueryWrapper,LoginLogs::getTokenId, tokenIds);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<LoginLogs> loginLogsList = loginLogsService.list(lambdaQueryWrapper);

        for (SytUserOnline sytUserOnline : paginatedList) {
            for (LoginLogs loginLogs : loginLogsList) {
                if (Objects.equals(sytUserOnline.getTokenId(), loginLogs.getTokenId())) {
                    sytUserOnline.setIpaddr(loginLogs.getIp());
                    sytUserOnline.setBrowser(loginLogs.getBrowser());
                    sytUserOnline.setOs(loginLogs.getOperatingSystem());
                    sytUserOnline.setLoginTime(loginLogs.getLoginTime().getTime());
                }
            }
        }
        Page<SytUserOnline> page = new Page<>();
        page.setRecords(paginatedList);
        page.setSize(query.getPageSize());
        page.setTotal(userOnlineDTOList.size());
        page.setPages(query.getPage());
        page.setCurrent(query.getPage());
        return Resp.success(page);
    }

    /**
     * 强退用户
     */
    @PostMapping("/kickOut")
    public Resp forceLogout(@RequestBody JSONObject param){
        String token = param.getString("token");
        redisUtil.delete(Constants.USER_ONLINE_REDIS_KEY + token);
        // redis session
        /*RedisIndexedSessionRepository sessionRepository = SpringTool.getBean(RedisIndexedSessionRepository.class);
        if (sessionRepository == null) {
            return Resp.error();
        }

        sessionRepository.deleteById(token);
        sessionRepository.getSessionRedisOperations().delete("spring:session:sessions:" + token);*/
        return Resp.success();
    }

    /**
     * 根据Redis中存储对象的Key解析对应的SessionId
     * @param key
     * @return
     */
    private String parseUserToken(String key){
        if(StringUtils.isNotEmpty(key)){
            String[] arr = key.split(":");
            if(arr != null && arr.length == 4){
                return arr[3];
            }
        }
        return null;
    }
    /**
     * 解析Redis中存储的用户信息和登录信息
     * @param val
     * @return
     */
    private SytUserOnline parseUserInfo(Object val){
//        Map<String, Object> userInfo = new HashMap<>();
        SytUserOnline userOnline = new SytUserOnline();
        JSONObject json = (JSONObject) JSONObject.toJSON(val);
        if(json != null && json.containsKey("authentication")){
            JSONObject authInfo = json.getJSONObject("authentication");
            if(authInfo != null){
                /*if(authInfo.containsKey("name")){//用户登录名称
                    userInfo.put("username",authInfo.getString("name"));
                }*/
                String sessionId = "";
                if(authInfo.containsKey("details")){//用户登录时的地址
                    JSONObject details = authInfo.getJSONObject("details");
                    if(details != null && details.containsKey("sessionId")){
//                            userInfo.put("hostAddress",details.getString("sessionId"));
                        sessionId = details.getString("sessionId");
                    }
                }
                userOnline.setTokenId(sessionId);
                if(authInfo.containsKey("principal")){
                    JSONObject principal = authInfo.getJSONObject("principal");
                    String humancode = principal.getString("humancode");
                    userOnline.setUserName(humancode);
                    userOnline.setRealName(principal.getString("humanname"));
                    userOnline.setDeptName(principal.getString("organizationnames"));
                   /* QueryWrapper<LoginLogs> logsQueryWrapper = new QueryWrapper<>();
                    logsQueryWrapper.eq("token_id", sessionId);
                    List<LoginLogs> list = loginLogsService.list(logsQueryWrapper);
                    if (list.size()>0) {
                        LoginLogs loginLogs = list.get(0);
                        userOnline.setIpaddr(loginLogs.getIp());
                        userOnline.setBrowser(loginLogs.getBrowser());
                        userOnline.setOs(loginLogs.getOperatingSystem());
                        userOnline.setLoginTime(loginLogs.getLoginTime().getTime());
                    }*/
                }
            }
        }

        return userOnline;
    }
}
