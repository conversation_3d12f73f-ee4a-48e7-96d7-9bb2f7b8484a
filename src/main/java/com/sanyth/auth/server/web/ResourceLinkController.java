package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.ResourceLink;
import com.sanyth.auth.server.service.ResourceLinkService;
import com.sanyth.auth.server.service.RoleResourceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/resourceLink")
public class ResourceLinkController extends BaseController {
    private Logger log = LoggerFactory.getLogger(ResourceLinkController.class);

    @Resource
    ResourceLinkService resourceLinkService;
    @javax.annotation.Resource
    private RoleResourceService roleResourceService;

    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<ResourceLink> query) {
        try {
            Page page = resourceLinkService.queryPage(query);
            return new Resp<Page<ResourceLink>>(page);
        } catch (Exception e) {
            log.error("查询异常", e);
            return Resp.error();
        }
    }

    @PostMapping("/treeList")
    public Resp treeList() throws Exception {
        QueryWrapper wrapper = new QueryWrapper<ResourceLink>();
        List<ResourceLink> list = resourceLinkService.list(wrapper);
        List<ResourceLink> resourceResults = buildTreeList(list);
        return Resp.success(resourceResults);
    }

    private List<ResourceLink> buildTreeList(List<ResourceLink> alllist) throws Exception {
        List<ResourceLink> parentList = new ArrayList<>();
        for (ResourceLink r : alllist) {
            if (StringUtils.isBlank(r.getParentId())) {
                parentList.add(r);
            }
        }

        for (ResourceLink root : parentList) {
            List<ResourceLink> childList = getChildList(root.getId(), alllist);
            root.setChildren(childList);
        }
        return parentList;
    }

    private static List<ResourceLink> getChildList(String id, List<ResourceLink> alllist) throws Exception {
        List<ResourceLink> childList = new ArrayList<>();
        for (ResourceLink child : alllist) {
            if (StringUtils.isNotEmpty(child.getParentId()) && child.getParentId().equals(id)) {
                childList.add(child);
            }
        }

        for (ResourceLink child : childList) {
            List<ResourceLink> list = getChildList(child.getId(), alllist);
            child.setChildren(list);
        }
        return childList;
    }

    @PostMapping("/getParentList")
    public Resp<List<ResourceLink>> getParentList() {
        List<ResourceLink> parentList = resourceLinkService.getParentList();
        return Resp.success(parentList);
    }

    @PostMapping("/edit")
    public Resp edit(@RequestBody ResourceLink resourceLink) {
        resourceLinkService.saveOrUpdate(resourceLink);
        roleResourceService.updatePermission(); // 更新缓存
        return Resp.success();
    }

    @PostMapping("/delete")
    public Resp delete(@RequestBody Map<String, String> param) {
        String id = param.get("id");
        if (StringUtils.isBlank(id)) {
            return Resp.error(ErrorInfo.MSG_0003);
        }
        resourceLinkService.removeById(id);
        roleResourceService.updatePermission(); // 更新缓存
        return Resp.success();
    }
}
