package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytCodeMzb;
import com.sanyth.auth.server.service.ISytCodeMzbService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * ${table.comment}前端控制器
 * Created by JIANGPING on 2020-05-15.
 */
@RestController
@RequestMapping("/sytCodeMzb")
public class SytCodeMzbController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytCodeMzbController.class);
    @Autowired
    private ISytCodeMzbService iSytCodeMzbService;

    /**
     * 分页查询SytCodeMzb信息
     *
     * @param query
     * @return
     */
    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<SytCodeMzb> query) {
        try {
            Page<SytCodeMzb> page = new Page<>(query.getPage(), query.getPageSize());
            QueryWrapper<SytCodeMzb> wrapper = new QueryWrapper<>();
            // Query condition...

            page = iSytCodeMzbService.page(page, wrapper);
            return Resp.success(page);
        } catch (Exception e) {
            log.error("查询SytCodeMzb信息出现异常", e);
            return Resp.error();
        }
    }

    @PostMapping("/list")
    public Resp List() {
        try {
            List<SytCodeMzb> list = iSytCodeMzbService.list(new QueryWrapper<>());
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 编辑SytCodeMzb信息
     *
     * @param sytCodeMzb
     * @return
     */
    @PostMapping("/edit")
    public Resp edit(@RequestBody SytCodeMzb sytCodeMzb) {
        try {
            iSytCodeMzbService.saveOrUpdate(sytCodeMzb);
            return Resp.success();
        } catch (Exception e) {
            log.error("编辑SytCodeMzb信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 删除SytCodeMzb信息
     *
     * @param param
     * @return
     */
    @PostMapping("/delete")
    public Resp delete(@RequestBody Map<String, String> param) {
        try {
            String id = param.get("id");
            if (StringUtils.isBlank(id)) {
                return Resp.error(ErrorInfo.MSG_0003);
            }
            iSytCodeMzbService.removeByIds(Arrays.asList(id));
            return Resp.success();
        } catch (Exception e) {
            log.error("编辑SytCodeMzb信息出现异常", e);
            return Resp.error();
        }
    }
}
