package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.service.ISytSysOrganizationService;
import com.sanyth.auth.server.util.OrgTreeUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 组织机构表前端控制器
 * Created by JIANGPING on 2020-05-14.
 */
@RestController
@RequestMapping("/sytSysOrganization")
public class SytSysOrganizationController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytSysOrganizationController.class);
    @Resource
    private ISytSysOrganizationService iSytSysOrganizationService;

    /**分页查询
     * @param query
     * @return
     */
    @PostMapping("/queryPage")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询组织机构",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#query.queryParam}}"
    )
    public Resp list(@RequestBody BaseQuery<SytSysOrganization> query) {
        return Resp.success(iSytSysOrganizationService.queryPage(query));
    }

    /**获取所有父节点
     * @return
     */
    @PostMapping("/queryParentList")
    public Resp queryParentList() {
        return Resp.success(iSytSysOrganizationService.queryParentList());
    }

    @PostMapping("/queryNodeList")
    public Resp queryNodeList(@RequestBody BaseQuery<SytSysOrganization> query) {
        SytSysOrganization param = query.getQueryParam();
        return Resp.success(iSytSysOrganizationService.queryList(param));
    }

    /**组织机构树  懒加载
     * @return
     * @throws Exception
     */
    @PostMapping("/lzayTreeList")
    public Resp lzayTreeList(@RequestBody(required=false) SytSysOrganization organization) throws Exception {
        if (organization == null) {
            organization = new SytSysOrganization();
        }
        List<SytSysOrganization> sytSysOrganizations = iSytSysOrganizationService.queryList(organization);
        return Resp.success(OrgTreeUtil.buildLazyTreeList(sytSysOrganizations,organization));
    }

    @PostMapping("/lzayTreeJsonArray")
    public List<SytSysOrganization> lzayTreeJsonArray(@RequestBody(required=false) SytSysOrganization organization) throws Exception {
        if (organization == null) {
            organization = new SytSysOrganization();
        }
        List<SytSysOrganization> sytSysOrganizations = iSytSysOrganizationService.queryList(organization);
        return OrgTreeUtil.buildLazyTreeList(sytSysOrganizations,organization);
    }

    /**
     * 组织机构树 只查有效机构
     * @param organization
     * @return
     * @throws Exception
     */
    @PostMapping("/treeList")
    public Resp treelist(@RequestBody(required=false) SytSysOrganization organization) throws Exception {
        if (organization == null) {
            organization = new SytSysOrganization();
        }
        organization.setValid("1");
        List<SytSysOrganization> sytSysOrganizations = iSytSysOrganizationService.queryList(organization);
        return Resp.success(OrgTreeUtil.buildTreeList(sytSysOrganizations));
    }

    @PostMapping("/treeListJsonArray")
    public List<SytSysOrganization> treeListJsonArray(@RequestBody(required=false) SytSysOrganization organization) throws Exception {
        if (organization == null) {
            organization = new SytSysOrganization();
        }
        organization.setValid("1");
        List<SytSysOrganization> sytSysOrganizations = iSytSysOrganizationService.queryList(organization);
        return OrgTreeUtil.buildTreeList(sytSysOrganizations);
    }

    /**
     * 编辑
     *
     * @param organization
     * @return
     */
    @PostMapping("/edit")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "编辑组织机构",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#organization}}"
    )
    public Resp edit(@RequestBody SytSysOrganization organization) {
        organization.setValid("1");
        SytSysOrganization sytSysOrganization = new SytSysOrganization();
        BeanUtils.copyProperties(organization, sytSysOrganization);
        sytSysOrganization.setOrgname(organization.getLabel());
        iSytSysOrganizationService.saveOrUpdate(sytSysOrganization);
        return Resp.success();
    }

    /**
     * 删除
     *
     * @param param
     * @return
     */
    @PostMapping("/delete")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "删除组织机构",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp delete(@RequestBody JSONObject param) {
        String id = param.getString("id");
        if (StringUtils.isBlank(id)) {
            return Resp.error(ErrorInfo.MSG_0003);
        }
        iSytSysOrganizationService.removeByIds(Arrays.asList(id));
        return Resp.success();
    }
}
