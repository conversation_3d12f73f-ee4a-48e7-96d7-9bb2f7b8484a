package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.Resource;
import com.sanyth.auth.server.pojo.ResourceResult;
import com.sanyth.auth.server.service.ResourceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by JIANGPING on 2020/4/13.
 */
@RestController
@RequestMapping("/resource")
public class ResourceController extends BaseController {

    @javax.annotation.Resource
    private ResourceService resourceService;

    private Logger log = LoggerFactory.getLogger(ResourceController.class);


    /**
     * @param query
     * @return
     */
    @PostMapping("/queryPage")
    public Resp list(@RequestBody BaseQuery<Resource> query) {
        Page<Resource> page = new Page<>(query.getPage(), query.getPageSize());
        QueryWrapper<Resource> wrapper = new QueryWrapper<>();
        // Query condition...

        page = resourceService.page(page, wrapper);
        return Resp.success(page);
    }

    @PostMapping("/get")
    public Resp get(String id) {
        return Resp.success(resourceService.getById(id));
    }

    @PostMapping("/allChildren")
    public Resp allChildren(String id, BaseQuery query) {
        Page<Resource> allChildren = resourceService.getAllChildren(id, query);
        return Resp.success(allChildren);
    }


    @PostMapping("/treeListByUser")
    public Resp treeListByUser(HttpServletRequest request) throws Exception {
        CurrentUser currentUser = currentUser(request);
        List<Resource> list = resourceService.getListByRole(currentUser.getRoleId());
        return Resp.success(buildTreeList(list));
    }

    @PostMapping("/treeList")
    public Resp treeList() throws Exception {
        Resource resource = new Resource();
        resource.setIsBtn(Constants.HAS_NO);
        List<Resource> list = resourceService.queryList(resource);
        List<ResourceResult> resourceResults = buildTreeList(list);
        return Resp.success(resourceResults);
    }

    private List<ResourceResult> buildTreeList(List<Resource> alllist) throws Exception {
        List<ResourceResult> menulist = new ArrayList<>();
        for (Resource r : alllist) {
            if (StringUtils.isBlank(r.getParentId())) {
                ResourceResult vo = new ResourceResult();
                BeanUtils.copyProperties(r, vo);
                menulist.add(vo);
            }
        }

        for (ResourceResult root : menulist) {
            List<ResourceResult> childList = getChildList(root.getId(), alllist);
            root.setChildren(childList);
        }
        return menulist;
    }

    private static List<ResourceResult> getChildList(String id, List<Resource> alllist) throws Exception {
        List<ResourceResult> childList = new ArrayList<>();
        for (Resource child : alllist) {
            if (StringUtils.isNotEmpty(child.getParentId()) && child.getParentId().equals(id)) {
                ResourceResult vo = new ResourceResult();
                BeanUtils.copyProperties(child, vo);
                childList.add(vo);
            }
        }

        for (ResourceResult child : childList) {
            List<ResourceResult> list = getChildList(child.getId(), alllist);
            child.setChildren(list);
        }
        return childList;
    }


    @PostMapping("/edit")
    public Resp edit(@RequestBody Resource resource) {
        resourceService.saveOrUpdate(resource);
        return Resp.success();
    }

    @PostMapping("/delete")
    public Resp delete(@RequestBody Map<String, String> param) {
        String id = param.get("id");
        resourceService.delete(id);
        return Resp.success();
    }

}
