package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.SytSysSafety;
import com.sanyth.auth.server.service.SytSysSafetyService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 安全设置，密码策略
 */
@RestController
@RequestMapping(value = "/SytSysSafety")
public class SytSysSafetyController extends BaseController {

    private Logger log = LoggerFactory.getLogger(SytSysSafetyController.class);

    @Autowired
    SytSysSafetyService sytSysSafetyService;

    @PostMapping("/queryPage")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询安全策略",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#query.queryParam}}"
    )
    public Resp queryPage(@RequestBody BaseQuery<SytSysSafety> query) {
        Page<SytSysSafety> sytSysParamPage = sytSysSafetyService.queryPage(query);
        return Resp.success(sytSysParamPage);
    }

    @PostMapping("/edit")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "编辑安全策略",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp edit(@RequestBody JSONObject param) {
        sytSysSafetyService.edit(param);
        return Resp.success();
    }

    @PostMapping("/delete")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "删除安全策略",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp delete(@RequestBody JSONObject param) {
        String id = param.getString("id");
        if (StringUtils.isBlank(id)) {
            return Resp.error(ErrorInfo.MSG_00005);
        }
        sytSysSafetyService.delete(id);
        return Resp.success();
    }

    @PostMapping("/list")
    public List<SytSysSafety> list(SytSysSafety dict) {
        dict = dict == null ? new SytSysSafety() : dict;
        return sytSysSafetyService.queryByVo(dict);
    }

    /**
     * 根据code查询单条数据
     * @param param
     * @return com.sanyth.portal.core.common.Resp
     */
    @PostMapping("/getByCode")
    public Resp getByCode(@RequestBody JSONObject param) {
        String code = param.getString("code");
        if (StringUtils.isBlank(code)) {
            return Resp.error(ErrorInfo.MSG_00005);
        }
        return Resp.success(sytSysSafetyService.getByCode(code));
    }
    /**
     * 根据多个CODE查询，返回指定数据
     * @param param
     */
    @PostMapping("/getListByCodes")
    public Resp getListByCodes(@RequestBody JSONObject param) {
        String codes = param.getString("code");
        if (StringUtils.isBlank(codes)) {
            return Resp.error(ErrorInfo.MSG_00005);
        }
        return Resp.success(sytSysSafetyService.getAll(codes));
    }

}
