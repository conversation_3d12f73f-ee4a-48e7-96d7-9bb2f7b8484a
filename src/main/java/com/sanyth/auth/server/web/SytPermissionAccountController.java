package com.sanyth.auth.server.web;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytPermissionRole;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.pojo.Account;
import com.sanyth.auth.server.pojo.AccountListener;
import com.sanyth.auth.server.pojo.ImportErrorInfo;
import com.sanyth.auth.server.service.ISytPermissionAccountService;
import com.sanyth.auth.server.service.ISytPermissionRoleService;
import com.sanyth.auth.server.service.ISytSysOrganizationService;
import com.sanyth.auth.server.service.SytSysParamService;
import com.sanyth.auth.server.util.ValidationUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 用户信息表前端控制器
 * Created by JIANGPING on 2020-05-14.
 */
@Controller
@RequestMapping("/sytPermissionAccount")
public class SytPermissionAccountController extends BaseController {

    static final String CHANGE_PHONE_INFO = "CHANGE_PHONE_INFO";
    private Logger log = LoggerFactory.getLogger(SytPermissionAccountController.class);
    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;
    @Resource
    private ISytPermissionRoleService iSytPermissionRoleService;
    @Resource
    private ISytSysOrganizationService iSytSysOrganizationService;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    VerifyCodeUtils verifyCodeUtils;
    @Autowired
    SytSysParamService paramService;

    @RequestMapping("/redis")
    @ResponseBody
    public Resp list(String type) {
        String key = "test";
        if ("a".equals(type)) {
            List<String> list = Arrays.asList("123");
            Map<String, List<String>> ss = new HashMap<>();
            ss.put(key, list);
            redisTemplate.opsForHash().putAll(key, ss);
        } else if ("b".equals(type)) {
            List<String> list = Arrays.asList("123", "456");
            Map<String, List<String>> ss = new HashMap<>();
            ss.put(key, list);
            redisTemplate.opsForHash().putAll(key, ss);
        }
        return Resp.success(redisTemplate.opsForHash().entries(key));
    }

    /**
     * @param baseQuery
     * @return
     */
    @PostMapping("/queryPage")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询用户管理",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#baseQuery.queryParam}}"
    )
    public Resp list(@RequestBody BaseQuery<Map<String, Object>> baseQuery) {
        Page<SytPermissionAccount> page = iSytPermissionAccountService.queryPage(baseQuery);
        return Resp.success(page);
    }

    @PostMapping("/get")
    @ResponseBody
    public Resp get(String id) {
        SytPermissionAccount account = iSytPermissionAccountService.getById(id);
        return Resp.success(account);
    }

    @PostMapping("/edit")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "编辑用户",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#params}}"
    )
    public Resp edit(@RequestBody JSONObject params) throws Exception {
        iSytPermissionAccountService.edit(params);
        return Resp.success();
    }

    @PostMapping("/delete")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "删除用户",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#params}}"
    )
    public Resp delete(@RequestBody JSONObject param) throws Exception {
        iSytPermissionAccountService.delete(param.getString("id"));
        return Resp.success();
    }

    @RequestMapping("/downImportTemplate")
    public void downImportTemplate(HttpServletResponse response) {
        try {
            String fileName = URLEncoder.encode("导入模板", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), Account.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("sheet1")
                    .doWrite(new ArrayList());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Deprecated
    @RequestMapping("/generateExportData")
    @ResponseBody
    public Resp generateExportData(@RequestBody BaseQuery<Map<String, Object>> baseQuery, HttpSession session) {
        try {
            baseQuery.setPage(1);
            baseQuery.setPageSize(Integer.MAX_VALUE);
            Page<SytPermissionAccount> page = iSytPermissionAccountService.queryPage(baseQuery);
            session.setAttribute("exportData", page.getRecords());
            return Resp.success();
        } catch (Exception e) {
            log.error("生成失败",e);
        }
        return Resp.error();
    }

    @RequestMapping("/export")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "导出用户",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public void export(HttpServletResponse response, HttpSession session,@RequestParam Map<String, Object> param) {
        try {
            BaseQuery<Map<String, Object>> baseQuery = new BaseQuery<>();
            baseQuery.setPage(1);
            baseQuery.setPageSize(Integer.MAX_VALUE);
            baseQuery.setQueryParam(param);
            List<SytPermissionAccount> list = iSytPermissionAccountService.queryListAndRole(baseQuery);
//            List<SytPermissionAccount> list = (List<SytPermissionAccount>) session.getAttribute("exportData");
            List<Account> accounts = new ArrayList<>();
            list.forEach(permissionAccount ->{
                Account account = new Account();
                BeanUtils.copyProperties(permissionAccount, account);
                StringBuilder sb = new StringBuilder();
                permissionAccount.getRole().stream().forEach(obj->{
                    sb.append(((JSONObject) obj).getString("label")).append(",");
                });
                account.setRole(sb.toString());
                accounts.add(account);
            });
            String fileName = URLEncoder.encode("用户信息", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), Account.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("sheet1")
                    .doWrite(accounts);
        } catch (IOException e) {
            log.error("导出失败", e);
        }
    }

    @RequestMapping("/import")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "导入用户",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#file}}"
    )
    public void importAccount(@RequestParam("file") MultipartFile file, HttpServletResponse response) {
        try {
            List<Account> accountList = EasyExcel.read(file.getInputStream(), Account.class, new AccountListener()).sheet().doReadSync();
//            List<Account> accountList = EasyExcel.read("C:\\Users\\<USER>\\Desktop\\test.xlsx", Account.class, new AccountListener()).sheet().doReadSync();
            List<ImportErrorInfo> errorInfo = new ArrayList<>();
            if (!CollectionUtils.isEmpty(accountList)) {
                List<SytPermissionRole> sytPermissionRoles = iSytPermissionRoleService.list(new QueryWrapper<>());
                Map<String, String> roleMap = new HashMap<>();
                for (SytPermissionRole role : sytPermissionRoles) {
                    roleMap.put(role.getRolename(), role.getId());
                }

                List<SytSysOrganization> sytSysOrganizations = iSytSysOrganizationService.list(new QueryWrapper<>());
                Map<String, String> orgMap = new HashMap<>();
                for (SytSysOrganization org : sytSysOrganizations) {
                    orgMap.put(org.getOrgname(), org.getId());
                }

                Set<String> accountSets = new HashSet<>();
                List<SytPermissionAccount> accounts = iSytPermissionAccountService.list(new QueryWrapper<>());
                for (SytPermissionAccount account : accounts) {
                    accountSets.add(account.getHumancode());
                }

                for (Account account : accountList) {
                    String humancode = account.getHumancode();
                    String humanname = account.getHumanname();
                    String roles = account.getRole();
                    String organization = account.getOrganizationnames();
                    boolean error = false;
                    if (StringUtils.isEmpty(humancode)) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), humancode, "不能为空"));
                        error = true;
                    } else {
                        if (accountSets.contains(humancode)) {
                            errorInfo.add(new ImportErrorInfo(account.getRowIndex(), humancode, "当前登录账号已存在"));
                            error = true;
                        }
                    }

                    if (StringUtils.isEmpty(humanname)) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), humanname, "不能为空"));
                        error = true;
                    }

                    Set<String> roleIds = new HashSet<>();
                    if (StringUtils.isEmpty(roles)) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), roles, "不能为空"));
                        error = true;
                    } else {
                        String[] strings = roles.trim().split(",");
                        for (String string : strings) {
                            if (roleMap.get(string.trim()) != null) {
                                roleIds.add(roleMap.get(string.trim()));
                            }
                        }

                        if (CollectionUtils.isEmpty(roleIds)) {
                            errorInfo.add(new ImportErrorInfo(account.getRowIndex(), roles, "角色名称不匹配"));
                            error = true;
                        }
                    }

                    Set<String> orgIds = new HashSet<>();
                    if (StringUtils.isEmpty(organization)) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), organization, "不能为空"));
                        error = true;
                    } else {
                        String[] orgs = organization.trim().split(",");
                        for (String string : orgs) {
                            if (orgMap.get(string.trim()) != null) {
                                orgIds.add(orgMap.get(string.trim()));
                            }
                        }

                        if (CollectionUtils.isEmpty(orgIds)) {
                            errorInfo.add(new ImportErrorInfo(account.getRowIndex(), organization, "组织机构不存在"));
                            error = true;
                        }
                    }

                    if (StringUtils.isEmpty(account.getTelmobile1())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), account.getTelmobile1(), "不能为空"));
                        error = true;
                    }
                    if (!ValidationUtils.isMobile(account.getTelmobile1())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), account.getTelmobile1(), "不正确"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(account.getEmployeeType())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), account.getEmployeeType(), "不能为空"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(account.getIdcode())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), account.getIdcode(), "不能为空"));
                        error = true;
                    }
                    if (!ValidationUtils.isMobile(account.getIdcode())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), account.getIdcode(), "不正确"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(account.getValidfromdate())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), String.valueOf(account.getValidfromdate()), "不能为空"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(account.getValidtodate())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), String.valueOf(account.getValidtodate()), "不能为空"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(account.getValidflag())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), String.valueOf(account.getValidflag()), "不能为空"));
                        error = true;
                    }
                    /*if (StringUtils.isEmpty(account.getHumanpassword())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), account.getHumanpassword(), "不能为空"));
                        error = true;
                    } else if (!ToolsUtil.checkPasswordStrength("", account.getHumanpassword())) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), account.getHumanpassword(), "密码强度不符"));
                        error = true;
                    }*/

                    if (!error) {
                        JSONObject params = new JSONObject();
                        SytPermissionAccount permissionAccount = new SytPermissionAccount();
                        BeanUtils.copyProperties(account, permissionAccount);
                        params.put("account", permissionAccount);
                        params.put("role", roleIds);
                        params.put("organization", orgIds);
                        iSytPermissionAccountService.edit(params);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(errorInfo)) {
                // 回写导入错误信息...
                String fileName = URLEncoder.encode("错误信息", "UTF-8");
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ImportErrorInfo.class)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet("sheet1")
                        .doWrite(errorInfo);
                return;
            }
            Resp.render(response, ErrorInfo.CODE_00000, ErrorInfo.CODE_MSG_00000);
        } catch (Exception e) {
            e.printStackTrace();
            Resp.render(response, ErrorInfo.CODE_00001, ErrorInfo.CODE_MSG_00001);
        }
    }


    // 更新个人信息,注：此接口不更新手机号
    @PostMapping("/updateInfo")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "更新用户信息",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#account}}"
    )
    public Resp updateInfo(@RequestBody SytPermissionAccount account) throws Exception {
        return iSytPermissionAccountService.updateInfo(account);
    }

    /**修改密码
     * @param param
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/changePasswd")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "修改密码",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp changePasswd(@RequestBody Map<String, String> param, HttpServletRequest request) throws Exception {
        CurrentUser currentUser = currentUser(request);
        String oldpassword = param.get("oldpassword");
        String newpassword = param.get("newpassword");
        String newpasswords = param.get("newpasswords");
        return iSytPermissionAccountService.changePasswd(oldpassword, newpassword, newpasswords, currentUser);
    }

    @PostMapping("/restPasswd")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "重置密码",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp restPasswd(@RequestBody JSONObject param) {
        return iSytPermissionAccountService.resetPasswordByAdmin(param);
    }

    @PostMapping("/getAccountAuditData")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "获取用户审计日志",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp getAccountAuditData(@RequestBody JSONObject param) {
        return iSytPermissionAccountService.getAccountAuditData(param);
    }

    /**
     * 发送更改电话验证代码
     *
     * @param param   参数
     * @param request 请求
     * @return {@link Resp}
     * @throws Exception 异常
     */
    @ResponseBody
    @RequestMapping("/sendChangePhoneVerifyCode")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "获取修改电话验证码",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp sendChangePhoneVerifyCode(@RequestBody Map<String, String> param, HttpServletRequest request) throws Exception {
        // 判断验证码发送是否在指定时间
        /*String msg = verifyCodeUtils.checkVerifyCodeSendTime();
        if(StringUtils.isNotBlank(msg)){
            return Resp.error(msg);
        }*/

        int codeValidTime = 5; // 验证码有效期，默认5分钟
        String type = param.get("verifyType"); // 验证方式；区分是手机，还是邮箱
        if(org.apache.commons.lang3.StringUtils.isBlank(type)){
            return Resp.error("参数verifyType不能为空");
        }
        if(!"email".equals(type) && !"phone".equals(type)){
            return Resp.error("参数verifyType值不正确");
        }
        String phoneOrEmail = param.get("phoneOrEmail");
        if(org.apache.commons.lang3.StringUtils.isBlank(phoneOrEmail)){
            return Resp.error("参数phoneOrEmail不能为空");
        }

        SytPermissionAccount account = iSytPermissionAccountService.getByHumancode(currentUser(request).getHumancode());
        Object verified = redisTemplate.opsForHash().get("CHANGE_PHONE_INFO_VERIFIED_KEY" + account.getHumancode(), "verified");
        if (!"true".equals(verified)) {
            if ("email".equals(type)) {
                if (!Objects.equals(account.getEmail(), phoneOrEmail)) {
                    return Resp.error("邮箱与用户邮箱不匹配");
                }
            } else if ("phone".equals(type)) {
                if (!Objects.equals(account.getTelmobile1(), phoneOrEmail)) {
                    return Resp.error("手机号与用户手机号不匹配");
                }
            }
        }


        String key = CHANGE_PHONE_INFO+"_"+phoneOrEmail;

        // 查询是否有已发送的验证码；
        Object str = redisTemplate.opsForHash().get(key, "code");
        if(str != null && org.apache.commons.lang3.StringUtils.isNotBlank(String.valueOf(str))){
            return Resp.success("验证码未过有效期");
        }
        String verifyCode = verifyCodeUtils.createRandom(true, 6);
        Resp resp = Resp.error();
        // 验证码发送次数,CodeSendCount_年月日_手机号或者邮箱  <EMAIL> CodeSendCount_20220303_17799887766
        String keyCount = "CodeSendCount_" + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMdd") + "_";
        if("email".equals(type)){
            keyCount += phoneOrEmail;

            // 判断是否超出次数
            if(verifyCodeUtils.checkVerifyCodeSendCount(keyCount, "codeCount")){
                return Resp.error("发送失败：已超过当天验证码发送限制次数，请明天再来进行相关操作");
            }
            String subject = "修改手机号"; // 邮件主题，必填
            String content = "您正在进行修改手机号，验证码："+verifyCode+"，切勿将验证码泄露于他人，本条验证码有效期"+codeValidTime+"分钟";

            SytSysParam sytSysParam = paramService.get("emailSubject"); // 邮件主题
            if(sytSysParam != null && org.apache.commons.lang3.StringUtils.isNotBlank(sytSysParam.getValue())){
                subject = sytSysParam.getValue();
            }
            sytSysParam = paramService.get("alSms_templateContent_change"); // 发送内容
            if(sytSysParam != null && org.apache.commons.lang3.StringUtils.isNotBlank(sytSysParam.getValue())){
                content = sytSysParam.getValue();
                // 用户${humanname}，您的账号${humancode}正在修改密码，验证码是：${code}
//                content = content.replaceAll("\\$\\{humanname\\}", account.getHumanname());
//                content = content.replaceAll("\\$\\{humancode\\}", account.getHumancode());
                content = content.replaceAll("\\$\\{code\\}", verifyCode);
            }
            resp = verifyCodeUtils.sendEmail(phoneOrEmail, subject, content);
        }else if("phone".equals(type)){
            keyCount += phoneOrEmail;
            // 判断是否超出次数
            if(verifyCodeUtils.checkVerifyCodeSendCount(keyCount, "codeCount")){
                return Resp.error("发送失败：已超过当天验证码发送限制次数，请明天再来进行相关操作");
            }
            account.setTelmobile1(phoneOrEmail);
            resp = verifyCodeUtils.sendSms(account, verifyCode, "change");
        }
        // 发送成功，将验证码保存到redis
        if(ErrorInfo.CODE_00000.equals(resp.getCode())){
            Map<String, String> map = new HashMap<>();
            map.put("code", verifyCode);
            redisTemplate.opsForHash().putAll(key, map);
            redisTemplate.expire(key, 60*codeValidTime, TimeUnit.SECONDS);

            // 记录验证码发送次数
            setCodeSendCount(keyCount);
        }
        return resp;
    }

    /**
     * 记录验证码发送次数
     * @param keyCount
     */
    private void setCodeSendCount (String keyCount){
        // 记录验证码发送次数
        Long codeCount = 0L;
        Object str = redisTemplate.opsForHash().get(keyCount, "codeCount");
        if(str != null){
            codeCount = Long.parseLong(String.valueOf(str));
        }
        Map<String, Long> mapCount = new HashMap<>();
        mapCount.put("codeCount", codeCount + 1 );
        redisTemplate.opsForHash().putAll(keyCount, mapCount);
        redisTemplate.expire(keyCount, 24, TimeUnit.MINUTES);
    }

    /**
     * 验证改变电话号码的验证码
     *
     * @param param   参数
     * @param request 请求
     * @return {@link Resp}
     * @throws Exception 异常
     */
    @PostMapping("/verifyChangePhoneCode")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "验证修改电话验证码",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp verifyChangePhoneCode(@RequestBody Map<String, String> param, HttpServletRequest request) throws Exception {
        Resp resp = iSytPermissionAccountService.verifyChangePhoneCode(param, currentUser(request));
        return resp;
    }

    /**
     * 更新手机号
     *
     * @param param   参数
     * @return {@link Resp}
     * @throws Exception 异常
     */
    @PostMapping("/updatePhone")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "更改手机号",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp updatePhone(@RequestBody Map<String, String> param, HttpServletRequest request) throws Exception {
        return iSytPermissionAccountService.updatePhone(param, currentUser(request));
    }

    /**
     * 更新人员排序
     */
    @PostMapping("/updateDisplayorder")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "更新人员排序",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp updateDisplayorder(@RequestBody Map<String, Object> param){
        return iSytPermissionAccountService.updateDisplayorder(param);
    }
}
