package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytCodeJslxb;
import com.sanyth.auth.server.service.ISytCodeJslxbService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * ${table.comment}前端控制器
 * Created by JIANGPING on 2020-05-15.
 */
@RestController
@RequestMapping("/sytCodeJslxb")
public class SytCodeJslxbController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytCodeJslxbController.class);
    @Autowired
    private ISytCodeJslxbService iSytCodeJslxbService;

    /**
     * 分页查询SytCodeJslxb信息
     *
     * @param query
     * @return
     */
    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<SytCodeJslxb> query) {
        try {
            Page<SytCodeJslxb> page = new Page<>(query.getPage(), query.getPageSize());
            QueryWrapper<SytCodeJslxb> wrapper = new QueryWrapper<>();
            // Query condition...

            page = iSytCodeJslxbService.page(page, wrapper);
            return Resp.success(page);
        } catch (Exception e) {
            log.error("查询SytCodeJslxb信息出现异常", e);
            return Resp.error();
        }
    }

    @PostMapping("/list")
    public Resp List() {
        try {
            List<SytCodeJslxb> list = iSytCodeJslxbService.list(new QueryWrapper<>());
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 编辑SytCodeJslxb信息
     *
     * @param sytCodeJslxb
     * @return
     */
    @PostMapping("/edit")
    public Resp edit(@RequestBody SytCodeJslxb sytCodeJslxb) {
        try {
            iSytCodeJslxbService.saveOrUpdate(sytCodeJslxb);
            return Resp.success();
        } catch (Exception e) {
            log.error("编辑SytCodeJslxb信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 删除SytCodeJslxb信息
     *
     * @return
     */
    @PostMapping("/delete")
    public Resp delete(@RequestBody JSONObject param) {
        String id = param.getString("id");
        try {
            if (StringUtils.isBlank(id)) {
                return Resp.error(ErrorInfo.MSG_0003);
            }
            iSytCodeJslxbService.removeByIds(Arrays.asList(id));
            return Resp.success();
        } catch (Exception e) {
            log.error("删除SytCodeJslxb信息出现异常", e);
            return Resp.error();
        }
    }
}
