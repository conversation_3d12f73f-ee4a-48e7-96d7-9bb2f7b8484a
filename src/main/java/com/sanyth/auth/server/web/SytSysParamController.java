package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.service.SytSysParamService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 系统参数
 * Created by JIANGPING on 2020/5/15.
 */
@RestController
@RequestMapping("/sytSysParam")
public class SytSysParamController extends BaseController {
    private Logger log = LoggerFactory.getLogger(SytSysParamController.class);
    @Autowired
    SytSysParamService paramService;

    @PostMapping("/queryPage")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询系统参数",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#query.queryParam}}"
    )
    public Resp queryPage(@RequestBody BaseQuery<SytSysParam> query) {
        Page<SytSysParam> sytSysParamPage = paramService.queryPage(query);
        return Resp.success(sytSysParamPage);
    }

    @PostMapping("/get")
    public Resp get(@RequestBody Map<String,String> idOrNameOrType) {
        SytSysParam param = paramService.get(idOrNameOrType.get("idOrNameOrType"));
        return Resp.success(param);
    }

    @PostMapping("/edit")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "编辑系统参数",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#sysParam}}"
    )
    public Resp edit(@RequestBody SytSysParam sysParam) {
        if (StringUtils.isNotBlank(sysParam.getId())) {
            paramService.update(sysParam);
        } else {
            paramService.save(sysParam);
        }
        return Resp.success();
    }

    @PostMapping("/delete")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "删除系统参数",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp delete(@RequestBody JSONObject param) {
        String id = param.getString("id");
        if (StringUtils.isBlank(id)) {
            return Resp.error(ErrorInfo.MSG_00005);
        }
        paramService.delete(id);
        return Resp.success();
    }

    @PostMapping("/getNoLogin")
    public Resp getNoLogin(@RequestBody Map<String,String> idOrNameOrType) {
        SytSysParam param = paramService.get(idOrNameOrType.get("idOrNameOrType"));
        return Resp.success(param);
    }

    @PostMapping("/getListNoLogin")
    public Resp getListNoLogin(@RequestBody BaseQuery query) {
        List<SytSysParam> list = paramService.queryList(query);
        return Resp.success(list);
    }
}
