package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytLogRecordPO;
import com.sanyth.auth.server.service.SytLogRecordService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 操作日志
 */
@RestController
@RequestMapping("/sytLogRecord")
public class SytLogRecordController extends BaseController {
    private Logger log = LoggerFactory.getLogger(SytLogRecordController.class);
    @Autowired
    SytLogRecordService logRecordService;

    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<SytLogRecordPO> query) {
        Page<SytLogRecordPO> sytSysParamPage = logRecordService.queryPage(query);
        return Resp.success(sytSysParamPage);
    }

    @PostMapping("/get")
    public Resp get(@RequestBody Map<String,String> idOrNameOrType) {
        SytLogRecordPO param = logRecordService.get(idOrNameOrType.get("idOrNameOrType"));
        return Resp.success(param);
    }

    @PostMapping("/delete")
    public Resp delete(@RequestBody JSONObject param) {
        String id = param.getString("id");
        if (StringUtils.isBlank(id)) {
            return Resp.error(ErrorInfo.MSG_00005);
        }
        logRecordService.delete(id);
        return Resp.success();
    }

}
