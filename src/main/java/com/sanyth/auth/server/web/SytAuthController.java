package com.sanyth.auth.server.web;

import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.JustAuthUtils;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytJustAuthUser;
import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.service.NoAuthQywxToAuthService;
import com.sanyth.auth.server.service.ISytPermissionAccountService;
import com.sanyth.auth.server.service.MyUserDetailsService;
import com.sanyth.auth.server.service.SytJustAuthUserService;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Controller
@RequestMapping("/user/auth")
public class SytAuthController extends BaseController {

    @Autowired
    JustAuthUtils justAuthUtils;
    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;
    @Resource
    private SytJustAuthUserService sytJustAuthUserService;
    @Resource
    private MyUserDetailsService myUserDetailsService;
    @Resource
    private QywxUserDao qywxUserDao;
    @Resource
    private NoAuthQywxToAuthService noAuthQywxToAuthService;

    /**
     *
     * @param oauthType 授权类型，第三方授权平台
     */
    @RequestMapping("/login/{oauthType}")
    public void renderAuth(@PathVariable("oauthType") String oauthType, HttpServletResponse response) throws IOException {
        // 创建授权request
        AuthRequest authRequest = justAuthUtils.getAuthRequest(oauthType);
        String authorizeUrl = authRequest.authorize(AuthStateUtils.createState());
        response.sendRedirect(authorizeUrl);
    }

    /**
     *
     * @param oauthType 第三方授权平台
     * @param token  login成功后返回的refreshToken
     */
    @RequestMapping("/refresh/{oauthType}")
    @ResponseBody
    public Object refreshAuth(@PathVariable("oauthType") String oauthType, String token){
        AuthRequest authRequest = justAuthUtils.getAuthRequest(oauthType);
        return authRequest.refresh(AuthToken.builder().refreshToken(token).build());
    }

    /**
     *
     * @param oauthType 第三方授权平台
     * @param token  login成功后返回的accessToken
     */
    @RequestMapping("/revoke/{oauthType}/{token}")
    @ResponseBody
    public Object revokeAuth(@PathVariable("oauthType") String oauthType, @PathVariable("token") String token) throws IOException {
        AuthRequest authRequest = justAuthUtils.getAuthRequest(oauthType);
        return authRequest.revoke(AuthToken.builder().accessToken(token).build());
    }

    @RequestMapping("/revoke")
    @ResponseBody
    public Resp revokeAuth2(@RequestBody Map<String, String> param) throws IOException {
        String id = param.get("id");
        if(StringUtils.isBlank(id)){
            return Resp.error("参数id不能为空");
        }
        SytJustAuthUser authUser = sytJustAuthUserService.getById(id);
        authUser.setSfBind("否");
        sytJustAuthUserService.updateById(authUser);
        return Resp.success();
    }
    /**
     * 绑定账号
     */
    @RequestMapping("/bindCallback/{oauthType}")
    @ResponseBody
    public Resp bindCallback(@PathVariable("oauthType") String oauthType, String code, String state ) {
        try {
            sytJustAuthUserService.bindCallback(oauthType, code, state);
            return Resp.success();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Resp.error();
    }

}
