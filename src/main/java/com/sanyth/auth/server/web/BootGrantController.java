package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.auth.server.model.OauthClientDetails;
import com.sanyth.auth.server.service.OauthClientDetailsService;
import org.springframework.security.oauth2.provider.AuthorizationRequest;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 自定义授权页面
 *
 * <AUTHOR>
 * @date 2022/08/17
 */
@Controller
@SessionAttributes("authorizationRequest")
public class BootGrantController {

    @Resource
    private OauthClientDetailsService oauthClientDetailsService;

    @RequestMapping("/oauth/confirm_access")
    public ModelAndView getAccessConfirmation(Map<String, Object> model, HttpServletRequest request) throws Exception {
        AuthorizationRequest authorizationRequest = (AuthorizationRequest) model.get("authorizationRequest");
        ModelAndView view = new ModelAndView();
        view.setViewName("base-grant");
//        view.addObject("clientId", authorizationRequest.getClientId());
        QueryWrapper<OauthClientDetails> wrapper = new QueryWrapper<>();
        wrapper.eq("CLIENT_ID", authorizationRequest.getClientId());
        OauthClientDetails oauthClientDetails = oauthClientDetailsService.getOne(wrapper);
        view.addObject("app", oauthClientDetails);
        view.addObject("scopes", authorizationRequest.getScope());
        return view;
    }
}