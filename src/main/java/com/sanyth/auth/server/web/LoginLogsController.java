package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.LoginLogs;
import com.sanyth.auth.server.service.LoginLogsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/loginLogs")
public class LoginLogsController extends BaseController {
    private Logger log = LoggerFactory.getLogger(LoginLogsController.class);

    @Resource
    LoginLogsService loginLogsService;

    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<LoginLogs> query, HttpServletRequest request) {
        try {
            LoginLogs queryParam = query.getQueryParam();
            if(queryParam == null)
                queryParam = new LoginLogs();
            CurrentUser user = currentUser(request);
            if (!Constants.ROLE_KEY_SUPERADMIN.equals(user.getRoleKey())) {
                queryParam.setHumanCode(currentUser(request).getHumancode());
            }
            Page<LoginLogs> page = new Page<>(query.getPage(), query.getPageSize());
            page = loginLogsService.queryPage(page, queryParam);
            return Resp.success(page);
        } catch (Exception e) {
            log.error("查询日志信息出现异常", e);
            return Resp.error();
        }
    }
}
