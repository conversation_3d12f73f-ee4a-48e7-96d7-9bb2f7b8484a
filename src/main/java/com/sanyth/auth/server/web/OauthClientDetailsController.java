package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.mapper.OauthClientDetailsMapper;
import com.sanyth.auth.server.model.CasClientDetails;
import com.sanyth.auth.server.model.OauthClientDetails;
import com.sanyth.auth.server.service.CasClientDetailsService;
import com.sanyth.auth.server.service.OauthClientDetailsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.security.Principal;
import java.util.Date;
import java.util.Map;

/**
 * ClientInfo
 * Created by JIANGPING on 2020-04-07.
 */
@RestController
@RequestMapping("/oauthClientDetails")
public class OauthClientDetailsController extends BaseController {
    private Logger log = LoggerFactory.getLogger(OauthClientDetailsController.class);

    @Resource
    private OauthClientDetailsService oauthClientDetailsService;
    @Resource
    OauthClientDetailsMapper oauthClientDetailsMapper;
    @Resource
    CasClientDetailsService casClientDetailsService;

    @GetMapping("/test")
    public Resp test(Principal principal, HttpSession session) {
        return Resp.success(currentUser(session));
    }


    @PostMapping("/queryPage")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询认证应用",
            type = LogRecordType.OAUTH_CLIENT_MANAGEMENT,
            bizNo = "{{#query.queryParam}}"
    )
    public Resp queryPage(@RequestBody BaseQuery<OauthClientDetails> query) {
        try {
            Page<OauthClientDetails> page = new Page<>(query.getPage(), query.getPageSize());
            page = oauthClientDetailsService.page(page);
            return Resp.success(page);
        } catch (Exception e) {
            log.error("查询客户端信息出现异常", e);
            return Resp.error();
        }
    }

    @PostMapping("/edit")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "编辑认证应用",
            type = LogRecordType.OAUTH_CLIENT_MANAGEMENT,
            bizNo = "{{#client}}"
    )
    public Resp clientEdit(@RequestBody OauthClientDetails client) {
        if (StringUtils.isNotBlank(client.getId())) {
            OauthClientDetails oauthClientDetails = oauthClientDetailsService.getById(client.getId());
            oauthClientDetails.setClientName(client.getClientName());
            oauthClientDetails.setWebServerRedirectUri(client.getWebServerRedirectUri());
            oauthClientDetails.setRemark(client.getRemark());
            oauthClientDetails.setClientIndex(client.getClientIndex());
            oauthClientDetails.setAuthType(client.getAuthType());
            oauthClientDetailsService.updateById(oauthClientDetails);


            if(StringUtils.isEmpty(oauthClientDetails.getAuthType()) || oauthClientDetails.getAuthType().equals(Constants.CAS)){
                QueryWrapper<CasClientDetails> wrapper = new QueryWrapper<>();
                wrapper.eq("theme",oauthClientDetails.getId());
                CasClientDetails casClientDetails=casClientDetailsService.getOne(wrapper);
                if(!client.getAuthType().equals(Constants.CAS)){
                    casClientDetailsService.remove(wrapper);
                }else{
                    if(casClientDetails==null){
                        casClientDetails=new CasClientDetails();
                    }
                    casClientDetails.setName(client.getClientName());
                    casClientDetails.setDescription(client.getDescription());
                    casClientDetails.setServiceId(client.getClientIndex());
                    casClientDetails.setTheme(client.getClientName());
                    if(casClientDetails.getId()==null){
                        casClientDetails.setId(System.currentTimeMillis());
                        casClientDetails.setTheme(client.getId());
                        casClientDetailsService.save(casClientDetails);
                    }else{
                        casClientDetailsService.updateById(casClientDetails);
                    }
                }
            }
        } else {
            if (StringUtils.isNotBlank(client.getClientId())) {
                QueryWrapper<OauthClientDetails> countWrapper = new QueryWrapper<>();
                countWrapper.eq("client_id", client.getClientId());
                boolean exists = oauthClientDetailsMapper.exists(countWrapper);
                if (exists) {
                    return Resp.error(ErrorInfo.MSG_00013);
                }
                client.setClientId(client.getClientId());
            } else {
                client.setClientId(ToolsUtil.getRandomCode(16));
            }
            client.setClientSecret(ToolsUtil.getRandomCode(32));
            client.setCreateTime(new Date());
            client.setAuthorizedGrantTypes("authorization_code,password");   // 默认authorization_code
            client.setScope("web");                                 // 默认web
            client.setAutoapprove(Boolean.TRUE.toString());
            client.setAuthType(client.getAuthType());
            oauthClientDetailsService.save(client);
            if(client.getAuthType().equals(Constants.CAS)){
                CasClientDetails casClientDetails=new CasClientDetails();
                casClientDetails.setName(client.getClientName());
                casClientDetails.setDescription(client.getDescription());
                casClientDetails.setServiceId(client.getClientIndex());
                casClientDetails.setTheme(client.getId());
                casClientDetailsService.save(casClientDetails);
            }
        }
        return Resp.success();
    }

    @PostMapping("/delete")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "删除认证应用",
            type = LogRecordType.OAUTH_CLIENT_MANAGEMENT,
            bizNo = "{{#params}}"
    )
    public Resp delete(@RequestBody Map<String, String> params) {
        try {
            if (params.get("id") != null) {
                OauthClientDetails oauthClientDetails=oauthClientDetailsService.getById(params.get("id"));
                oauthClientDetailsService.removeById(params.get("id"));

                if(Constants.CAS.equals(oauthClientDetails.getAuthType())){
                    QueryWrapper<CasClientDetails> wrapper = new QueryWrapper<>();
                    wrapper.eq("theme",oauthClientDetails.getId());
                    casClientDetailsService.removeById(wrapper);
                }
            }
        } catch (Exception e) {
            log.error("删除客户端信息出现异常", e);
            return Resp.error();
        }
        return Resp.success();
    }
}
