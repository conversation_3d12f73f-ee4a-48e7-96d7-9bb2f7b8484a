package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytCodeDqztb;
import com.sanyth.auth.server.service.ISytCodeDqztbService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * ${table.comment}前端控制器
 */
@RestController
@RequestMapping("/sytCodeDqztb")
public class SytCodeDqztbController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytCodeDqztbController.class);
    @Autowired
    private ISytCodeDqztbService iSytCodeDqztbService;

    /**
     * 分页查询SytCodeDqztb信息
     *
     * @param query
     * @return
     */
    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<SytCodeDqztb> query) {
        try {
            return Resp.success(iSytCodeDqztbService.queryPage(query));
        } catch (Exception e) {
            log.error("查询SytCodeDqztb信息出现异常", e);
            return Resp.error();
        }
    }

    @PostMapping("/list")
    public Resp List(@RequestBody SytCodeDqztb sytCodeDqztb) {
        try {
            List<SytCodeDqztb> list = iSytCodeDqztbService.queryList(sytCodeDqztb);
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 编辑SytCodeDqztb信息
     *
     * @param SytCodeDqztb
     * @return
     */
    @PostMapping("/edit")
    public Resp edit(@RequestBody SytCodeDqztb SytCodeDqztb) {
        try {
            iSytCodeDqztbService.saveOrUpdate(SytCodeDqztb);
            return Resp.success();
        } catch (Exception e) {
            log.error("编辑SytCodeDqztb信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 删除SytCodeDqztb信息
     *
     * @return
     */
    @PostMapping("/delete")
    public Resp delete(@RequestBody JSONObject param) {
        String id = param.getString("id");
        try {
            if (StringUtils.isBlank(id)) {
                return Resp.error(ErrorInfo.MSG_0003);
            }
            iSytCodeDqztbService.removeByIds(Arrays.asList(id.split(",")));
            return Resp.success();
        } catch (Exception e) {
            log.error("删除SytCodeDqztb信息出现异常", e);
            return Resp.error();
        }
    }
}
