package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.model.SytJustAuthConfig;
import com.sanyth.auth.server.service.SytJustAuthConfigService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Locale;


/**
 * 用第三方平台参数配置
 */
@Controller
@RequestMapping("/sytJustAuthConfig")
public class SytJustAuthConfigController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytJustAuthConfigController.class);
    @Resource
    private SytJustAuthConfigService sytJustAuthConfigService;


    /**
     * @param baseQuery
     * @return
     */
    @PostMapping("/queryPage")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询快捷认证",
            type = LogRecordType.JUST_AUTH_MANAGEMENT,
            bizNo = "{{#baseQuery.queryParam}}"
    )
    public Resp list(@RequestBody BaseQuery<SytJustAuthConfig> baseQuery, HttpServletRequest request) {
        Page<SytJustAuthConfig> page = sytJustAuthConfigService.queryPage(baseQuery);
        return Resp.success(page);
    }

    @PostMapping("/get")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "获取快捷认证信息",
            type = LogRecordType.JUST_AUTH_MANAGEMENT,
            bizNo = "{{#id}}"
    )
    public Resp get(String id) {
        SytJustAuthConfig account = sytJustAuthConfigService.getById(id);
        return Resp.success(account);
    }

    @PostMapping("/edit")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "编辑快捷认证",
            type = LogRecordType.JUST_AUTH_MANAGEMENT,
            bizNo = "{{#params}}"
    )
    public Resp edit(@RequestBody JSONObject params) throws Exception {
        sytJustAuthConfigService.edit(params);
        return Resp.success();
    }

    @PostMapping("/delete")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "删除快捷认证",
            type = LogRecordType.JUST_AUTH_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp delete(@RequestBody JSONObject param) throws Exception {
        sytJustAuthConfigService.delete(param.getString("id"));
        return Resp.success();
    }
    /**
     * 获取绑定信息
     */
    @PostMapping("/getBindInfo")
    @ResponseBody
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "获取快捷认证绑定信息",
            type = LogRecordType.JUST_AUTH_MANAGEMENT,
            bizNo = "{{#param}}"
    )
    public Resp getBindInfo(@RequestBody JSONObject param, HttpServletRequest request) throws Exception {
        try {
            String oauthType = param.getString("oauthType");
            if(StringUtils.isBlank(oauthType)){
                return Resp.error(ErrorInfo.MSG_0003);
            }
            SytJustAuthConfig sytJustAuthConfig = sytJustAuthConfigService.getByAuthType(oauthType.toUpperCase(Locale.ROOT));
            HashMap<String, Object> map = new HashMap<>();
            if(sytJustAuthConfig != null){
                CurrentUser currentUser = this.currentUser(request);
                map.put( "agentId",sytJustAuthConfig.getAgentId());
                map.put( "appId",sytJustAuthConfig.getClientId());
                // String urlRoot = PlatformRequestUtil.getUrlRoot(request);
                String redirectUri = sytJustAuthConfig.getRedirectUri();
                String[] arr = redirectUri.split("/user/auth/callback");
                String urlRoot = arr[0];
                String encodeUrl = URLEncoder.encode(urlRoot+"/noAuth/platform/qywx/user_bind_page/bindCallback", StandardCharsets.UTF_8.toString());
                map.put( "redirectUri",encodeUrl);
                map.put( "humancode",currentUser.getHumancode());
                return Resp.success(map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Resp.error();
    }

}
