package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.mapper.SytPermissionRoleMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytPermissionRole;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.service.ISytPermissionAccountService;
import com.sanyth.auth.server.service.ISytPermissionRoleService;
import com.sanyth.auth.server.service.SytSysParamService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.security.Principal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RequestMapping("/user")
@RestController
public class UserController extends BaseController {
    private Logger log = LoggerFactory.getLogger(UserController.class);

    @Resource
    ISytPermissionRoleService iSytPermissionRoleService;
    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    SytPermissionRoleMapper sytPermissionRoleMapper;
    @Autowired
    VerifyCodeUtils verifyCodeUtils;
    @Autowired
    SytSysParamService paramService;

    static final String FIND_PASSWORD_INFO = "FIND_PASSWORD_INFO";
    static final String PHONE_LOGIN_INFO = "PHONE_LOGIN_INFO";

    @RequestMapping
    public Resp index(HttpServletRequest request) {
        CurrentUser currentUser = currentUser(request);
        List<SytPermissionRole> roles = iSytPermissionRoleService.getByAccount(currentUser.getId());
        String s = JSON.toJSONString(currentUser);
        JSONObject jsonObject = JSONObject.parseObject(s, JSONObject.class);
        JSONArray array = new JSONArray();
        for (SytPermissionRole role : roles) {
            array.add(role.getRolename());
        }
        jsonObject.put("roleList", array);
        return Resp.success(jsonObject);
    }

    @RequestMapping("/me")
    public Map<String, Object> currentUser(Principal principal) {
        return getStringObjectMap(principal);
    }

    @RequestMapping("/detail")
    public Resp userinfo(HttpServletRequest request) {
//        return getStringObjectMap(principal);
        return Resp.success(currentUser(request));
    }

    private Map<String, Object> getStringObjectMap(Principal principal) {
        String s = JSONObject.toJSONString(principal);
        JSONObject jsonObject = (JSONObject) JSON.parse(s);
        Map<String, Object> result = new HashMap<>();

        JSONArray authorities = jsonObject.getJSONArray("authorities");
        StringBuilder authorityStr = new StringBuilder();
        if (!CollectionUtils.isEmpty(authorities)) {
            for (Object authority : authorities) {
                JSONObject auth = (JSONObject) authority;
                authorityStr.append(auth.getString("name")).append(",");
            }
            authorityStr.deleteCharAt(authorityStr.length() - 1);
        }
        result.put("authorities", authorityStr.toString());
        result.put("name", jsonObject.getString("name"));
        result.put("username", jsonObject.getString("name"));
        return result;
    }

    @PostMapping("/switchRole")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "切换角色",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = "{{#params}}"
    )
    public Resp switchRole(@RequestBody Map<String, String> params, HttpServletRequest request) {
        if (params == null || StringUtils.isEmpty(params.get("role"))) {
            return Resp.error("角色信息错误");
        }
        String s = params.get("role");
        List<SytPermissionRole> roles = iSytPermissionRoleService.getByAccount(currentUser(request).getId());
        boolean flag = false;
        SytPermissionRole r = null;
        if (!CollectionUtils.isEmpty(roles)) {
            for (SytPermissionRole role : roles) {
                if (role.getRolename().equals(s)) {
                    flag = true;
                    r = role;
                    break;
                }
            }
        }

        if (!flag) {
            return Resp.error("角色信息错误");
        }

        Object spring_security_context = request.getSession().getAttribute("SPRING_SECURITY_CONTEXT");
        SecurityContextImpl securityContextImpl = (SecurityContextImpl) spring_security_context;
        Authentication authentication = securityContextImpl.getAuthentication();
        SytPermissionAccount account = (SytPermissionAccount) authentication.getPrincipal();
        account.setRoles(Arrays.asList(r));
        UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(
                account,
                authentication.getCredentials(),
                Arrays.asList(new SimpleGrantedAuthority(r.getRolekey())));
        auth.setDetails(authentication.getDetails());
        securityContextImpl.setAuthentication(auth);
        return Resp.success();
    }
    @RequestMapping("/getUserInfo")
    @LogRecord(
            subType = LogRecordType.SUB_TYPE_MANAGER,
            extra = "",
            success = "查询个人信息",
            type = LogRecordType.SYSTEM_MANAGEMENT,
            bizNo = ""
    )
    public Resp getUserInfo(HttpServletRequest request) {
        CurrentUser currentUser = currentUser(request);
        SytPermissionAccount account = iSytPermissionAccountService.getById(currentUser.getId());
        account.setHumanpassword(null);
        String roleNames = "";
        List<SytPermissionRole> roleList = sytPermissionRoleMapper.getByAccount(account.getId());
        if (!CollectionUtils.isEmpty(roleList)) {
            for (SytPermissionRole sytPermissionRole : roleList) {
                roleNames += sytPermissionRole.getRolename()+",";
            }
        }
        if(StringUtils.isNotBlank(roleNames)){
            roleNames = roleNames.substring(0, roleNames.length() - 1);
        }
        account.setRoleNames(roleNames);

        return Resp.success(account);
    }
    // 根据用户账号查询手机号、邮箱
    @RequestMapping("/getEmailAndPhoneByHumancode")
    public Resp getEmailAndPhoneByHumancode(@RequestBody Map<String, String> param, HttpServletRequest request) {
        String humancode = param.get("humancode");
        if(StringUtils.isBlank(humancode)){
            return Resp.error("参数humancode不能为空");
        }
        SytPermissionAccount account = null;
        SytPermissionAccount accountTmp = new SytPermissionAccount();
        accountTmp.setHumancode(humancode);
        List<SytPermissionAccount> list = iSytPermissionAccountService.queryList(accountTmp);
        if(list != null && list.size() > 0){
            account = list.get(0);
        }else{
            return Resp.error(humancode + "用户不存在");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("email", account.getEmail());
        map.put("phone", account.getTelmobile1());
        return Resp.success(map);
    }
    @RequestMapping("/sendVerifyCode")
    public Resp sendVerifyCode(@RequestBody Map<String, String> param, HttpServletRequest request) throws Exception {
        return sendCode(param, request);
    }

    private Resp sendCode(Map<String, String> param, HttpServletRequest request) throws Exception {

        // 判断验证码发送是否在指定时间
        String msg = verifyCodeUtils.checkVerifyCodeSendTime();
        if(StringUtils.isNotBlank(msg)){
            return Resp.error(msg);
        }

        int codeValidTime = 5; // 验证码有效期，默认5分钟
        String type = param.get("verifyType"); // 验证方式；区分是手机，还是邮箱
        if(StringUtils.isBlank(type)){
            return Resp.error("参数verifyType不能为空");
        }
        if(!"email".equals(type) && !"phone".equals(type)){
            return Resp.error("参数verifyType值不正确");
        }
        String humancode = param.get("humancode");
        if(StringUtils.isBlank(humancode)){
            return Resp.error("参数humancode不能为空");
        }
        SytPermissionAccount account = null;
        SytPermissionAccount accountTmp = new SytPermissionAccount();
        accountTmp.setHumancode(humancode);
        List<SytPermissionAccount> list = iSytPermissionAccountService.queryList(accountTmp);
        if(list != null && list.size() > 0){
            account = list.get(0);
        }else{
            return Resp.error(humancode + "用户不存在");
        }
        // <EMAIL>
        String key = FIND_PASSWORD_INFO+"_"+humancode + "_";
        if("email".equals(type)) {
            String email = account.getEmail();
            if (StringUtils.isBlank(email)) {
                return Resp.error("邮箱不存在");
            }
            key += email;
        }else if("phone".equals(type)){
            String telmobile1 = account.getTelmobile1();
            if(StringUtils.isBlank(telmobile1)){
                return Resp.error("手机号不存在");
            }
            key += telmobile1;
        }
        String tmpEmail = account.getEmail();
        String tmpPhone = account.getTelmobile1();
        if (StringUtils.isNotBlank(tmpEmail)) {
            int indexOf = tmpEmail.indexOf("@");
            tmpEmail = tmpEmail.substring(0, indexOf - 3).concat("******").concat(tmpEmail.substring(indexOf, tmpEmail.length()));
        }
        if (StringUtils.isNotBlank(tmpPhone)) {
            tmpPhone = tmpPhone.substring(0, 3) + "****" + tmpPhone.substring(7, tmpPhone.length());
        }
        // 查询是否有已发送的验证码；
        Object str = redisTemplate.opsForHash().get(key, "code");
        if(str != null && StringUtils.isNotBlank(String.valueOf(str))){
            // 返回手机 邮箱
            Map<String, Object> info = new HashMap<>();
            info.put("email",tmpEmail);
            info.put("phone", tmpPhone);
            info.put("codeValid", true);
            // 验证码存在，直接返回
            return Resp.success(info);
        }
        String verifyCode = verifyCodeUtils.createRandom(true, 6);
        Resp resp = Resp.error();
        // 验证码发送次数,CodeSendCount_年月日_手机号或者邮箱  <EMAIL> CodeSendCount_20220303_17799887766
        String keyCount = "CodeSendCount_" + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMdd") + "_";
        if("email".equals(type)){
            String email = account.getEmail();
            if(StringUtils.isBlank(email)){
                return Resp.error("邮箱不存在");
            }
//            key += email;
            keyCount += email;

            // 判断是否超出次数
            if(verifyCodeUtils.checkVerifyCodeSendCount(keyCount, "codeCount")){
                return Resp.error("发送失败：已超过当天验证码发送限制次数，请明天再来进行相关操作");
            }
            String subject = "密码验证"; // 邮件主题，必填
            String content = "您正在进行密码验证，验证码："+verifyCode+"，切勿将验证码泄露于他人，本条验证码有效期"+codeValidTime+"分钟";

            SytSysParam sytSysParam = paramService.get("emailSubject"); // 邮件主题
            if(sytSysParam != null && StringUtils.isNotBlank(sytSysParam.getValue())){
                subject = sytSysParam.getValue();
            }
            sytSysParam = paramService.get("alSms_templateContent_change"); // 发送内容
            if(sytSysParam != null && StringUtils.isNotBlank(sytSysParam.getValue())){
                content = sytSysParam.getValue();
                // 用户${humanname}，您的账号${humancode}正在修改密码，验证码是：${code}
//                content = content.replaceAll("\\$\\{humanname\\}", account.getHumanname());
//                content = content.replaceAll("\\$\\{humancode\\}", account.getHumancode());
                content = content.replaceAll("\\$\\{code\\}", verifyCode);
            }
            resp = verifyCodeUtils.sendEmail(email, subject, content);
        }else if("phone".equals(type)){
            String telmobile1 = account.getTelmobile1();
            if(StringUtils.isBlank(telmobile1)){
                return Resp.error("手机号不存在");
            }
//            key += telmobile1;
            keyCount += telmobile1;
            // 判断是否超出次数
            if(verifyCodeUtils.checkVerifyCodeSendCount(keyCount, "codeCount")){
                return Resp.error("发送失败：已超过当天验证码发送限制次数，请明天再来进行相关操作");
            }
            resp = verifyCodeUtils.sendSms(account, verifyCode, "change");
        }
        // 发送成功，将验证码保存到redis
        if(ErrorInfo.CODE_00000.equals(resp.getCode())){
            Map<String, String> map = new HashMap<>();
            map.put("code", verifyCode);
            redisTemplate.opsForHash().putAll(key, map);
            redisTemplate.expire(key, 60*codeValidTime, TimeUnit.SECONDS);
            // 返回手机 邮箱
            Map<String, Object> info = new HashMap<>();
            info.put("email", tmpEmail);
            info.put("phone", tmpPhone);
            resp.setInfo(info);

            // 记录验证码发送次数
            setCodeSendCount(keyCount);
        }
        return resp;
    }

    @PostMapping("/resetPasswdByCode")
    @ResponseBody
    public Resp resetPasswd(@RequestBody Map<String, String> param, HttpServletRequest request) throws Exception {
        Resp resp = iSytPermissionAccountService.resetPassword(param);
        return resp;
    }

    /**
     * 手机号登录，获取验证码
     * @param param
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/sendPhoneLoginVerifyCode")
    public Resp sendPhoneLoginVerifyCode(@RequestBody Map<String, String> param, HttpServletRequest request) throws Exception {

        // 判断验证码发送是否在指定时间
        String msg = verifyCodeUtils.checkVerifyCodeSendTime();
        if(StringUtils.isNotBlank(msg)){
            return Resp.error(msg);
        }
        String phone = param.get("phone");
        if(StringUtils.isBlank(phone)){
            return Resp.error("手机号不能为空");
        }
        SytPermissionAccount account = null;
        SytPermissionAccount accountTmp = new SytPermissionAccount();
        accountTmp.setTelmobile1(phone);
        List<SytPermissionAccount> list = iSytPermissionAccountService.queryList(accountTmp);
        if(list != null && list.size() > 0){
            account = list.get(0);
        }else{
            return Resp.error(phone + "对应的用户不存在");
        }

        // 查询是否有已发送的验证码；
        Object strCode = redisTemplate.opsForHash().get("PHONE_LOGIN_INFO" + "_" + phone, "code");
        if(strCode != null && StringUtils.isNotBlank(String.valueOf(strCode))){
            // 验证码存在，直接返回
            return Resp.success();
        }
        // 验证码发送次数,CodeSendCount_年月日_手机号或者邮箱  <EMAIL> CodeSendCount_20220303_17799887766
        String keyCount = "CodeSendCount_" + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMdd") + "_" + phone;
        // 判断是否超出次数
        if(verifyCodeUtils.checkVerifyCodeSendCount(keyCount, "codeCount")){
            return Resp.error("发送失败：已超过当天验证码发送限制次数，请明天再来进行相关操作");
        }

        int codeValidTime = 5; // 验证码有效期，默认5分钟
        // PHONE_LOGIN_INFO_135
        String key = PHONE_LOGIN_INFO+"_"+phone;
        // 查询是否有已发送的验证码；
        Object str = redisTemplate.opsForHash().get(key, "code");
        if(str != null && StringUtils.isNotBlank(String.valueOf(str))){
            // 验证码存在，直接返回
            return Resp.success();
        }

        String verifyCode = verifyCodeUtils.createRandom(true, 6);
        Resp resp = Resp.error();
        resp = verifyCodeUtils.sendSms(account, verifyCode, "login");
//        System.err.println("验证码已发送至手机："+phone+"；【"+verifyCode+"】");
        // 发送成功，将验证码保存到redis
        if(ErrorInfo.CODE_00000.equals(resp.getCode())){
            Map<String, String> map = new HashMap<>();
            map.put("code", verifyCode);
            redisTemplate.opsForHash().putAll(key, map);
            redisTemplate.expire(key, 60*codeValidTime, TimeUnit.SECONDS);

            // 记录验证码发送次数
            setCodeSendCount(keyCount);
        }
        return resp;
    }

    /**
     * 记录验证码发送次数
     * @param keyCount
     */
    private void setCodeSendCount (String keyCount){
        // 记录验证码发送次数
        Long codeCount = 0L;
        Object str = redisTemplate.opsForHash().get(keyCount, "codeCount");
        if(str != null){
            codeCount = Long.parseLong(String.valueOf(str));
        }
        Map<String, Long> mapCount = new HashMap<>();
        mapCount.put("codeCount", codeCount + 1 );
        redisTemplate.opsForHash().putAll(keyCount, mapCount);
        redisTemplate.expire(keyCount, 24, TimeUnit.MINUTES);
    }

}
