package com.sanyth.auth.server.web;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SysAuthLogs;
import com.sanyth.auth.server.service.SysAuthLogsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 认证日志
 */
@RestController
@RequestMapping("/sysAuthLogs")
public class SysAuthLogsController extends BaseController {

    @Resource
    SysAuthLogsService sysAuthLogsService;

    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<SysAuthLogs> query, HttpServletRequest request) {
        try {
            SysAuthLogs queryParam = query.getQueryParam();
            if(queryParam == null) {
                queryParam = new SysAuthLogs();
            }
            queryParam.setHumanCode(currentUser(request).getHumancode());
            Page<SysAuthLogs> page = new Page<>(query.getPage(), query.getPageSize());
            page = sysAuthLogsService.queryPage(page, queryParam);
            return Resp.success(page);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }
}

