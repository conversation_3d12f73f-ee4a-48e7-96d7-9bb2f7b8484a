package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytSysOrganizationUser;
import com.sanyth.auth.server.service.ISytSysOrganizationUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

/**
 * ${table.comment}前端控制器
 * Created by JIANGPING on 2020-05-15.
 */
@RestController
@RequestMapping("/sytSysOrganizationUser")
public class SytSysOrganizationUserController extends BaseController {


    private Logger log = LoggerFactory.getLogger(SytSysOrganizationUserController.class);
    @Resource
    private ISytSysOrganizationUserService iSytSysOrganizationUserService;

    /**
     * 分页查询SytSysOrganizationUser信息
     *
     * @param query
     * @return
     */
    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<SytSysOrganizationUser> query) {
        Page<SytSysOrganizationUser> sytSysOrganizationUserPage = iSytSysOrganizationUserService.queryPage(query);
        return Resp.success(sytSysOrganizationUserPage);
    }

    /**
     * 编辑SytSysOrganizationUser信息
     *
     * @param sytSysOrganizationUser
     * @return
     */
    @PostMapping("/edit")
    public Resp edit(@RequestBody SytSysOrganizationUser sytSysOrganizationUser) {
        iSytSysOrganizationUserService.saveOrUpdate(sytSysOrganizationUser);
        return Resp.success();
    }

    /**
     * 删除SytSysOrganizationUser信息
     *
     * @param param
     * @return
     */
    @PostMapping("/delete")
    public Resp delete(@RequestBody Map<String, String> param ) {
        String id = param.get("id");
        if (StringUtils.isBlank(id)) {
            return Resp.error(ErrorInfo.MSG_0003);
        }
        iSytSysOrganizationUserService.removeByIds(Arrays.asList(id));
        return Resp.success();
    }
}
