package com.sanyth.auth.server.core.config;

import com.sanyth.auth.server.core.common.SpringTool;
import org.springframework.session.Session;
import org.springframework.session.SessionRepository;

import java.time.Duration;

public class MySessionManager {


    public void updateSessionExpiration(String sessionId, int newTimeoutInSeconds) {
        SessionRepository sessionRepository = SpringTool.getBean(SessionRepository.class);
        Session session = sessionRepository.findById(sessionId);
        if (session != null) {
            session.setMaxInactiveInterval(Duration.ofSeconds(newTimeoutInSeconds));
            sessionRepository.save(session);
        } else {
            // 处理找不到会话的情况
        }
    }
}