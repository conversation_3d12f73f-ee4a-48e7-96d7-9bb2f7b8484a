package com.sanyth.auth.server.core.common;

import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytPermissionRole;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextImpl;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

public class BaseController {

    protected CurrentUser currentUser(HttpServletRequest request) {
        return currentUser(request.getSession());
    }

    protected CurrentUser currentUser(HttpSession session) {
        Object spring_security_context = session.getAttribute("SPRING_SECURITY_CONTEXT");
        SecurityContextImpl securityContextImpl = (SecurityContextImpl) spring_security_context;
        Authentication authentication = securityContextImpl.getAuthentication();
        SytPermissionAccount account = (SytPermissionAccount) authentication.getPrincipal();
        CurrentUser currentUser = new CurrentUser();
        BeanUtils.copyProperties(account, currentUser);
        SytPermissionRole role = account.getRoles().get(0);
        currentUser.setRoleName(role.getRolename());
        currentUser.setRoleKey(role.getRolekey());
        currentUser.setRoleId(role.getId());
        return currentUser;
    }

}
