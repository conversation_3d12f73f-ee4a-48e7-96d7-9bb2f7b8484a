package com.sanyth.auth.server.core.common;

import java.util.ArrayList;
import java.util.List;

public class ManualPaginationHelper<T> {

    /**
     * 手工分页方法
     * @param fullList 完整数据列表
     * @param page 当前页码，从1开始
     * @param size 每页显示的数据量
     * @return 分页后的数据列表
     */
    public List<T> paginate(List<T> fullList, int page, int size) {
        if (fullList == null || fullList.isEmpty() || size <= 0) {
            return new ArrayList<>(); // 返回空列表
        }

        int fromIndex = (page - 1) * size; // 计算起始索引
        int toIndex = Math.min(fromIndex + size, fullList.size()); // 计算结束索引

        // 如果起始索引超出了列表范围，返回空列表
        if (fromIndex >= fullList.size() || fromIndex < 0) {
            return new ArrayList<>();
        }

        return fullList.subList(fromIndex, toIndex); // 获取子列表并返回
    }
}