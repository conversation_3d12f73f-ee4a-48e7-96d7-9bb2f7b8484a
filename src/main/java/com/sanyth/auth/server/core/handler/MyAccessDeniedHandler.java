package com.sanyth.auth.server.core.handler;

import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 访问异常处理
 * Created by JIANGPING on 2020/6/5.
 */
@Component
public class MyAccessDeniedHandler implements AccessDeniedHandler {
    @Override
    public void handle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AccessDeniedException e) throws IOException, ServletException {
        Resp.render(httpServletResponse, ErrorInfo.CODE_00005, ErrorInfo.CODE_MSG_00005);
    }
}
