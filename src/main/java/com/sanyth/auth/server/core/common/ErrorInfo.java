package com.sanyth.auth.server.core.common;

/**
 * Created by JIANGPING on 2020/3/31.
 */
public class ErrorInfo {

    public static final String MSG_0001 = "解码参数不能为空";
    public static final String MSG_0002 = "请求参数解码错误, 请重试";
    public static final String MSG_0003 = "缺失必填项参数";
    public static final String MSG_0004="当前登录账号已存在";
    public static final String CODE_00000="00000";
    public static final String CODE_MSG_00000="success";

    public static final String CODE_00001="00001";
    public static final String CODE_MSG_00001="服务异常";

    public static final String CODE_00002="00002";
    public static final String CODE_MSG_00002="验证码填写错误";

    public static final String CODE_00003="00003";
    public static final String CODE_MSG_00003="动态key失效, 请重试";

    public static final String CODE_00004="00004";
    public static final String CODE_MSG_00004="用户名或密码错误";

    public static final String CODE_00005="00005";
    public static final String CODE_MSG_00005="权限不足, 无法访问";

    public static final String MSG_00005="缺失必填项参数";

    public static final String MSG_00013 = "clientId已存在";

    public static final String CODE_00006="00006";
    public static final String CODE_MSG_00006="您登录密码长时间未变更，建议修改";

    public static final String CODE_00007="00007";
    public static final String CODE_MSG_00007="登录失败次数已达限制，账户锁定，请稍后再尝试";
}
