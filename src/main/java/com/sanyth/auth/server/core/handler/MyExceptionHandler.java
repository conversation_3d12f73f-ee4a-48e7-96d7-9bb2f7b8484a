package com.sanyth.auth.server.core.handler;

import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.core.exception.BusinessException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@ControllerAdvice
public class MyExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler({RuntimeException.class})
    @ResponseBody
    public Resp<String> handle(RuntimeException e, WebRequest request) {
//        long l = System.currentTimeMillis();
        StringBuilder error = new StringBuilder("全局异常捕获:");
        for (int i = 0; i < 5 && i < e.getStackTrace().length; i++) {
            error.append(e.getStackTrace()[i]).append("\n");
        }
        logger.error(error);
//        long l1 = System.currentTimeMillis();
//        System.out.println(l1 - l);
        if (e instanceof BusinessException) {
            BusinessException be = (BusinessException) e;
            return Resp.error(be.getMessage());
        } else if (e instanceof InsufficientAuthenticationException){
            throw e;
        }
        return Resp.error();
    }
}
