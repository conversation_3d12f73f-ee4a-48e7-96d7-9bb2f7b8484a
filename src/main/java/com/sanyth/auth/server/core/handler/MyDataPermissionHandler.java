package com.sanyth.auth.server.core.handler;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.sanyth.auth.server.core.annotation.DataPermission;
import com.sanyth.auth.server.core.common.Constants;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.util.SecurityUtils;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SelectExpressionItem;
import net.sf.jsqlparser.statement.select.SubSelect;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Data
@Component
public class MyDataPermissionHandler implements DataPermissionHandler {

    private static RedisTemplate redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        MyDataPermissionHandler.redisTemplate = redisTemplate;
    }

    /**
     * @param where             原SQL Where 条件表达式
     * @param mappedStatementId Mapper接口方法ID
     * @return
     */
    @SneakyThrows
    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        try {
            //TODO 优化权限注解加载
            Class<?> clazz = Class.forName(mappedStatementId.substring(0, mappedStatementId.lastIndexOf(".")));
            String methodName = mappedStatementId.substring(mappedStatementId.lastIndexOf(".") + 1);
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                DataPermission annotation = method.getAnnotation(DataPermission.class);
                if (ObjectUtils.isNotEmpty(annotation) && (method.getName().equals(methodName) || (method.getName() + "_COUNT").equals(methodName)||method.getName().contains(methodName))) {
                    return dataScopeFilter(SecurityUtils.getUser(), annotation.tableAlias(), where);
                }
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return where;
    }

    /**
     * 构建过滤条件
     *
     * @param account 当前登录用户
     * @param where 当前查询条件
     * @return 构建后查询条件
     */
    public Expression dataScopeFilter(SytPermissionAccount account, String tableAlias, Expression where) {
        Expression expression = null;
        if (account == null) {
            return where;
        }
//        RedisTemplate redisTemplate = SpringContextUtil.getBean(RedisTemplate.class);
        Map<String, Set<String>> result = redisTemplate.opsForHash().entries(Constants.DATA_SCOPE_INFO);
        Set<String> list = result.get(account.getId());
        if (CollectionUtils.isNotEmpty(list)) {
            /**
             * SELECT COUNT(*) FROM SYT_PERMISSION_ACCOUNT where 1=1 and --原表达式expression
             * --new AndExpression(expression, inExpression)
             * and HUMANCODE in (select HUMANCODE from SYT_PERMISSION_ACCOUNT inner join SYT_SYS_ORGANIZATION_USER on ID=USER_ID
             * where ORGANIZATION_ID in ('2c0bf31100244ebe811ff87daf8196a8','b5445ff2ec1643ff98bf72d6c5350ff3'))
             */
            //sql查询的主表
            InExpression inExpression = new InExpression();
            inExpression.setLeftExpression(buildColumn(tableAlias, "HUMANCODE"));
            //in子查询
            SubSelect subSelect = new SubSelect();
            PlainSelect select = new PlainSelect();
            select.setSelectItems(Collections.singletonList(new SelectExpressionItem(new Column("HUMANCODE"))));
            Table permissionAccountTable = new Table("SYT_PERMISSION_ACCOUNT").withAlias(new Alias("sytacc", false));
            select.setFromItem(permissionAccountTable);
            Table organizationUserTable = new Table("SYT_SYS_ORGANIZATION_USER").withAlias(new Alias("sytou", false));
            Join join = new Join();
            join.withRightItem(organizationUserTable);
            EqualsTo equalsTo = new EqualsTo();
            equalsTo.setLeftExpression(new Column(permissionAccountTable, "ID"));
            equalsTo.setRightExpression(new Column(organizationUserTable, "USER_ID"));
            join.withOnExpression(equalsTo);
            select.addJoins(join);
            InExpression orgInExpression = new InExpression();
            orgInExpression.setLeftExpression(new Column(organizationUserTable, "ORGANIZATION_ID"));
            ItemsList itemsList = new ExpressionList(list.stream().map(StringValue::new).collect(Collectors.toList()));
            orgInExpression.setRightItemsList(itemsList);
            select.setWhere(orgInExpression);
            subSelect.setSelectBody(select);
            inExpression.setRightExpression(subSelect);
            expression = inExpression;
        }
        return ObjectUtils.isNotEmpty(where) ? ObjectUtils.isNotEmpty(expression) ? new AndExpression(where, new Parenthesis(expression)) : expression : expression;
    }

    /**
     * 构建Column
     *
     * @param tableAlias 表别名
     * @param columnName 字段名称
     * @return 带表别名字段
     */
    public static Column buildColumn(String tableAlias, String columnName) {
        if (StringUtils.isNotEmpty(tableAlias)) {
            columnName = tableAlias + "." + columnName;
        }
        return new Column(columnName);
    }
}
