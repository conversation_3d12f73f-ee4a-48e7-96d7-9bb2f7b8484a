package com.sanyth.auth.server.core.rsa;

import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.ToolsUtil;
import com.sanyth.auth.server.core.exception.ValidateException;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * Created by JIANGPING on 2019/4/28.
 */
public class SecJS {

    static StringBuilder ss = null;
    static int endIndex = 32;

    public static String getJS(String salt) {
        StringBuilder js = new StringBuilder();
        js.append("function _DK(){return \"").append(byte2hex(salt.getBytes())).append("\";}\n");
        if (ss == null) {
            synchronized (SecJS.class) {
                PublicKeyMap pm = RSAUtils.getPublicKeyMap();
                if (ss == null) {
                    ss = new StringBuilder();
                    ss.append("function _M(){return \"").append(byte2hex(pm.getModulus().getBytes())).append("\";}\n");
                    ss.append("function _E(){return \"").append(byte2hex(pm.getExponent().getBytes())).append("\";}\n");
                    ss.append("function encrypt(s){var pk = RSAUtils.getKeyPair(_E(), '', _M());var ss=encodeURIComponent(_DK()+s);var text = RSAUtils.encryptedString(pk,ss); return text;}\n");
                }
            }
        }
        js.append(ss.toString());
        return js.toString();
    }

    private static String byte2hex(byte[] bstr) {
        StringBuffer hs = new StringBuffer();
        String stmp = "";
        for (int n = 0; n < bstr.length; ++n) {
            stmp = Integer.toHexString(bstr[n] & 0xFF);
            hs.append("\\x");
            if (stmp.length() == 1) {
                hs.append("0");
                hs.append(stmp);
            } else {
                hs.append(stmp);
            }
        }
        return hs.toString();
    }

    private static final String newDynamicKey = "newDynamicKey";
    public static String newDynamicKey(HttpServletRequest request) {
        long t = System.currentTimeMillis();
        String v = ToolsUtil.md5encrypt(String.valueOf(t + ToolsUtil.getRandomCode(4)));
        request.getSession().setAttribute(newDynamicKey, v);
        return getJS(v);
    }

    public static String decryptAndGet(String text) {
        String passText = null;
        try {
            passText = URLDecoder.decode(RSAUtils.decryptStringByJs(text), "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return passText.substring(endIndex, passText.length());
    }

    public static String checkDynamicKeyAndGet(String text, HttpServletRequest request) throws ValidateException {
        if (StringUtils.isBlank(text)) {
            newDynamicKey(request);
            throw new ValidateException(ErrorInfo.MSG_0001);
        }
        try {
            text = RSAUtils.decryptStringByJs(text);
            text = URLDecoder.decode(text, "utf-8");
        } catch (Exception e) {
            throw new ValidateException(ErrorInfo.CODE_MSG_00004, ErrorInfo.CODE_00004);
        }
        String dk = text.substring(0, endIndex);
        HttpSession session = request.getSession();
        String attribute = (String) session.getAttribute(newDynamicKey);
        if (!dk.equals(attribute)) {
            throw new ValidateException(ErrorInfo.CODE_MSG_00004, ErrorInfo.CODE_00004);
        }
        return text.substring(endIndex, text.length());
    }
}
