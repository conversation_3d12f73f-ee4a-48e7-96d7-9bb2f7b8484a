package com.sanyth.auth.server.core.common;

import com.mongodb.BasicDBObject;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.service.SytSysParamService;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.Date;

/**
 * @Description:登录失败
 *
 * @author: wdl
 * @create: 2021/4/15
 */
@Component
public class LoginFailUtils {

//    private static int maxAge = 60 * 60 * 1; // 有效期
    private static int maxAge = -1;     // 有效期
    private static String loginFailCount_key = "loginFailCount";    // 登录失败次数的key
    private static final String collectionName = "sytAuth_login_fail";    // 登录失败记录表

    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    SytSysParamService paramService;


    public void loginFail(String userName, String password, HttpServletRequest request) {
        MongoCollection<Document> collection = mongoTemplate.getCollection(collectionName);

        Document fail = new Document();
        fail.put("ip", ToolsUtil.getIpAddr(request));
        fail.put("uname", userName);
        fail.put("pwd", password);
        fail.put("dt", new Date());

        collection.insertOne(fail);
    }
    public long getLoginFailCount(HttpServletRequest request) {
        MongoCollection<Document> collection = mongoTemplate.getCollection(collectionName);

        BasicDBObject query = new BasicDBObject();
        query.append("ip", ToolsUtil.getIpAddr(request));

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date date = calendar.getTime();
        query.append("dt", new BasicDBObject("$gte", date));

        return collection.countDocuments(query);
    }
    public long getLoginFailCount(String key, String val) {
        MongoCollection<Document> collection = mongoTemplate.getCollection(collectionName);

        BasicDBObject query = new BasicDBObject();
        query.append(key, val);

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date date = calendar.getTime();
        query.append("dt", new BasicDBObject("$gte", date));

        return collection.countDocuments(query);
    }
    public long getLoginFailCount(String key, String val, int minute){
        long interval = minute * 60 * 1000L;
        long endtime = System.currentTimeMillis();
        long begintime = endtime - interval;
        MongoCollection<Document> collection = mongoTemplate.getCollection(collectionName);

        BasicDBObject query = new BasicDBObject();
        query.put(key, val);
        query.put("dt", new BasicDBObject("$gte", new Date(begintime)).append("$lte", new Date(endtime)));

        return collection.countDocuments(query);
    }
    public Document getLoginFailLastRecord(String key, String val){
        MongoCollection<Document> collection = mongoTemplate.getCollection(collectionName);

        BasicDBObject query = new BasicDBObject();
        query.append(key, val);
        FindIterable<Document> findIterable = collection.find(query).limit(1).sort(new BasicDBObject("dt", -1));
        MongoCursor<Document> iterator = findIterable.iterator();
        while (iterator.hasNext()) {
            Document document = iterator.next();
            return document;
        }
        return null;
    }

    /**
     * @Description
     * @Param request
     * @Return boolean
     * <AUTHOR>
     * @Date  2021/4/15
     */
    public boolean isValidCaptcha(HttpServletRequest request) {
        int loginFailCount = 3;
        // 获取设定的登录失败次数限制
        loginFailCount = getLoginFailCount(loginFailCount);
//        String username = request.getParameter("username");
//        if(StringUtils.isBlank(username)){
//            return false;
//        }
//        username = SecJS.decryptAndGet(username);
//        return getLoginFailCount("uname", username) >= loginFailCount;
        return getLoginFailCount(request) >= loginFailCount;
    }

    public boolean isValidCaptcha(HttpServletRequest request, HttpServletResponse response) {
        int loginFailCount = 3;
        // 获取设定的登录失败次数限制
        loginFailCount = getLoginFailCount(loginFailCount);
        SytSysParam sysParam = paramService.get("loginCaptcha");
        boolean res = (sysParam == null || Constants.HAS_YES.equals(sysParam.getValue())) && getLoginFailCount(request, response) >= loginFailCount;
        if((sysParam == null || Constants.HAS_YES.equals(sysParam.getValue()))){ // 显示验证码
            Cookie cookie = new Cookie(loginFailCount_key, Long.toString(getLoginFailCount(request, response)));//(key,value)
            cookie.setPath("/");// 这个要设置
            cookie.setMaxAge(maxAge);// 以秒为单位
            response.addCookie(cookie); //添加Cookie
        }else{
            Cookie cookie = new Cookie(loginFailCount_key, null);//(key,value)
            cookie.setPath("/");// 这个要设置
            cookie.setMaxAge(0);// 以秒为单位
            response.addCookie(cookie); //添加Cookie
        }
        return res;
    }

    /**
     * 获取设定的登录失败次数限制
     * @param loginFailCount
     * @return int
     */
    private int getLoginFailCount(int loginFailCount) {
        try {
            SytSysParam param = paramService.get("loginFailCount");
            if (param != null && StringUtils.isNotBlank(param.getValue())) {
                loginFailCount = Integer.parseInt(param.getValue());
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return loginFailCount;
    }

    public long getLoginFailCount(HttpServletRequest request, HttpServletResponse response) {
        long loginFailCount = 0;
        Cookie[] cookies = request.getCookies();
        if (cookies != null && cookies.length > 0) {
            for (Cookie cookie : cookies) {
                if(loginFailCount_key.equals(cookie.getName())){
                    loginFailCount = Long.parseLong(cookie.getValue());
                    break;
                }
            }
        }
        return loginFailCount;
    }
    public void loginFail(HttpServletRequest request, HttpServletResponse response) {
        long loginFailCount = 0;
        Cookie[] cookies = request.getCookies();
        if (cookies != null && cookies.length > 0) {
            for (Cookie cookie : cookies) {
                if(loginFailCount_key.equals(cookie.getName())){
                    loginFailCount = Long.parseLong(cookie.getValue());
                    break;
                }
            }
        }
        loginFailCount++;
        SytSysParam sysParam = paramService.get("loginCaptcha");    // 判断是否启用登录验证码
        if((sysParam == null || Constants.HAS_YES.equals(sysParam.getValue()))){ // 显示验证码
            Cookie cookie = new Cookie(loginFailCount_key, Long.toString(loginFailCount));//(key,value)
            cookie.setPath("/");// 这个要设置
            cookie.setMaxAge(maxAge);// 以秒为单位
            response.addCookie(cookie); //添加Cookie
        }else{
            Cookie cookie = new Cookie(loginFailCount_key, null);//(key,value)
            cookie.setPath("/");// 这个要设置
            cookie.setMaxAge(0);// 以秒为单位
            response.addCookie(cookie); //添加Cookie
        }
    }

}
