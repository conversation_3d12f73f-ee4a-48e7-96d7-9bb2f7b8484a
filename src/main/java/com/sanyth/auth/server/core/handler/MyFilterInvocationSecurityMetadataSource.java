package com.sanyth.auth.server.core.handler;

import com.sanyth.auth.server.core.common.Constants;
import com.sanyth.auth.server.service.RoleResourceService;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MyFilterInvocationSecurityMetadataSource implements FilterInvocationSecurityMetadataSource {
    AntPathMatcher antPathMatcher = new AntPathMatcher();
    @Resource
    RoleResourceService roleResourceService;

    @Override
    public Collection<ConfigAttribute> getAttributes(Object o) throws IllegalArgumentException {
        String requestUrl = ((FilterInvocation) o).getRequestUrl();
        if (PermissionUtil.ignore(requestUrl) || PermissionUtil.notCheck(requestUrl)) {
            return SecurityConfig.createList(Constants.ROLE_DEF);
        }

        Map<String, List<String>> permissionAndRole = roleResourceService.permission();
        for (Map.Entry<String, List<String>> map : permissionAndRole.entrySet()) {
            String pattern = map.getKey();
            if (antPathMatcher.match(pattern, requestUrl)) {
                if (CollectionUtils.isEmpty(map.getValue())) {
                    return SecurityConfig.createList(Constants.ROLE_DEF);
                }
                return SecurityConfig.createList(map.getValue().toArray(new String[]{}));
            }
        }
        return SecurityConfig.createList(Constants.ROLE_DEF);
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return true;
    }
}
