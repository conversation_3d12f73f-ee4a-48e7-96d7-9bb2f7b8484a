package com.sanyth.auth.server.core.common;

/**
 * Created by JIANGPING on 2020/4/7.
 */
public class BaseQuery<T> implements java.io.Serializable{

    private Integer page = 1;
    private Integer pageSize = 20;
    private T queryParam;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public T getQueryParam() {
        return queryParam;
    }

    public void setQueryParam(T queryParam) {
        this.queryParam = queryParam;
    }
}
