package com.sanyth.auth.server.core.provider;

import com.sanyth.auth.server.core.handler.MyAuthenticationFailHander;
import com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

@Component
public class TokenAuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    // 自定义的成功失败处理器
    @Autowired
    private MyAuthenticationSuccessHandler myAuthenticationSuccessHandler;
    @Autowired
    private MyAuthenticationFailHander myAuthenticationFailHander;

    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 加入自定义的filter , provider
     * @param http
     * @throws Exception
     */
    @Override
    public void configure(HttpSecurity http) throws Exception {

        TokenAuthenticationFilter tokenAuthenticationFilter = new TokenAuthenticationFilter();
        tokenAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        tokenAuthenticationFilter.setAuthenticationSuccessHandler(myAuthenticationSuccessHandler);
        tokenAuthenticationFilter.setAuthenticationFailureHandler(myAuthenticationFailHander);

        TokenAuthenticationProvider tokenAuthenticationProvider = new TokenAuthenticationProvider();
        tokenAuthenticationProvider.setUserDetailsService(userDetailsService);

        http.authenticationProvider(tokenAuthenticationProvider)
                .addFilterAfter(tokenAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

    }

}
