package com.sanyth.auth.server.core.handler;

import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.core.exception.ValidateException;
import com.sanyth.auth.server.core.rsa.SecJS;
import com.sanyth.auth.server.service.SytSysSafetyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class MyAuthenticationFailHander implements AuthenticationFailureHandler {
    @Autowired
    LoginFailUtils loginFailUtils;
    @Autowired
    SytSysSafetyService sytSysSafetyService;
    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        String code = ErrorInfo.CODE_00001;
        String message = exception.getMessage();
        if (exception instanceof BadCredentialsException) {
            code = ErrorInfo.CODE_00004;
            message = ErrorInfo.CODE_MSG_00004;
            // 判断是否开启登录失败锁定账户限制
            checkLockAccount(request);
        } else if (exception instanceof ValidateException) {
            ValidateException validateException = (ValidateException) exception;
            message = validateException.getMessage();
            if (StringUtils.isNotBlank(validateException.getCode()))
                code = validateException.getCode();
        }

        // 判断登录失败次数是否超过限制
        if(!loginFailUtils.isValidCaptcha(request, response)){
            loginFailUtils.loginFail(request, response);
        }
        SecJS.newDynamicKey(request);
        response.setStatus(HttpStatus.OK.value());
        Resp.render(response, code, message);
    }

    private void checkLockAccount(HttpServletRequest request) {
        Map<String, Object> strategySafetyeMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_SAFETY);
        if(strategySafetyeMap != null && strategySafetyeMap.get("lockaccount") != null
                && Objects.equals("开启", strategySafetyeMap.get("lockaccount").toString())) {
            // 连续登录失败时长(分钟)
            Object loginfailtimeObj = strategySafetyeMap.get("loginfailtime");
            int loginfailtime = loginfailtimeObj != null && StringUtils.isNotBlank(loginfailtimeObj.toString()) ? Integer.valueOf(loginfailtimeObj.toString()) : 5;
            // 密码输错次数限制
            Object numberObj = strategySafetyeMap.get("number");
            long number = numberObj != null && StringUtils.isNotBlank(numberObj.toString()) ? Long.valueOf(numberObj.toString()) : 5;
            // 锁定时长(分钟)
            Object locktimeObj = strategySafetyeMap.get("locktime");
            long locktime = locktimeObj != null && StringUtils.isNotBlank(locktimeObj.toString()) ? Long.valueOf(locktimeObj.toString()) : 5;
            // 锁定类型
            Object locktypeObj = strategySafetyeMap.get("locktype");
            if(locktypeObj != null){
                String username = request.getParameter("username");
                username = SecJS.decryptAndGet(username);
                String password = request.getParameter("password");
                password = SecJS.decryptAndGet(password);
                // 失败记录添加标识
                boolean addflag = true;
                long loginFailCount = 0;
                String key = "LOCK_ACCOUNT_";
                // 锁定IP
                if(Objects.equals(locktypeObj.toString(), "ip")){
                    loginFailCount = loginFailUtils.getLoginFailCount("ip", ToolsUtil.getIpAddr(request), loginfailtime);
                    key += ToolsUtil.getIpAddr(request);
                }
                // 锁定账户
                if(Objects.equals(locktypeObj.toString(), "usercode")){
                    loginFailCount = loginFailUtils.getLoginFailCount("uname", username, loginfailtime);
                    key += username;
                }

                if(loginFailCount == number - 1 || loginFailCount >= number){
                    Object str = redisTemplate.opsForHash().get(key, "locktime");
                    if(str == null){
                        Map<String, Long> map = new HashMap<>();
                        map.put("locktime", locktime );
                        redisTemplate.opsForHash().putAll(key, map);
                        redisTemplate.expire(key, locktime, TimeUnit.MINUTES);
                    }
                }
                if (loginFailCount >= number) {
                    addflag = false;
                }
                if(addflag){
                    // 登录失败记录
                    loginFailUtils.loginFail(username, password, request);
                }
            }
        }
    }
}
