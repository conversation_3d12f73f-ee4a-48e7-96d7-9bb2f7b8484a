package com.sanyth.auth.server.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Created by JIANGPING on 2020/4/9.
 */
//@Configuration
public class WebConfig extends WebMvcConfigurationSupport {
//    @Value("${spring.resources.static-locations}")
//    public String resourcesStaticLocations;
//    @Value("${spring.mvc.static-path-pattern}")
//    public String staticPathPattern;
//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler(staticPathPattern).addResourceLocations(resourcesStaticLocations);
//    }
}
