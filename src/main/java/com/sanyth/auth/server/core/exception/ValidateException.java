package com.sanyth.auth.server.core.exception;

import org.springframework.security.core.AuthenticationException;

/**
 * Created by JIANGPING on 2020/3/31.
 */
public class ValidateException extends AuthenticationException {

    public ValidateException(String msg){
        super(msg);
    }

    public ValidateException(String msg, String code){
        super(msg);
        this.code = code;
    }

    public ValidateException(String msg, Throwable t) {
        super(msg, t);
    }

    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
