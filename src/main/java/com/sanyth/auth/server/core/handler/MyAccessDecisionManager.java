package com.sanyth.auth.server.core.handler;

import com.sanyth.auth.server.core.common.Constants;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.UserAgentUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.authentication.AnonymousAuthenticationProvider;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.web.FilterInvocation;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Component
public class MyAccessDecisionManager implements AccessDecisionManager {
    private static Logger logger = LoggerFactory.getLogger(MyAccessDecisionManager.class);

    @Override
    public void decide(Authentication authentication, Object o, Collection<ConfigAttribute> configAttributes) throws AccessDeniedException, InsufficientAuthenticationException {
        FilterInvocation filterInvocation = (FilterInvocation) o;
        String requestUrl = filterInvocation.getRequestUrl();
        Collection<? extends GrantedAuthority> grantedAuthorities = authentication.getAuthorities();
        for (ConfigAttribute attribute : configAttributes) {
            if (Constants.ROLE_DEF.equals(attribute.getAttribute())) {
                if (authentication instanceof AnonymousAuthenticationToken) {
                    return;
                } else if (authentication instanceof UsernamePasswordAuthenticationToken
                        || authentication instanceof OAuth2Authentication) {
                    if (PermissionUtil.ignore(requestUrl) || PermissionUtil.notCheck(requestUrl)) {
                        return;
                    } else {
                        accessDenied(requestUrl, attribute.getAttribute());
                    }
                }
            }

            for (GrantedAuthority authority : grantedAuthorities) {
                if (attribute.getAttribute().equals(authority.getAuthority())) {
                    return;
                }
            }
        }
        accessDenied(requestUrl, null);
    }

    private void accessDenied(String requestUrl, String role) {
        logger.error("Access is denied, {}, {}", requestUrl, role);
        throw new AccessDeniedException(ErrorInfo.CODE_MSG_00005);
    }

    @Override
    public boolean supports(ConfigAttribute configAttribute) {
        return true;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return true;
    }
}
