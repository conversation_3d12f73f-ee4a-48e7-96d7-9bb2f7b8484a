package com.sanyth.auth.server.core.handler;

import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.core.rsa.SecJS;
import com.sanyth.auth.server.model.LoginLogs;
import com.sanyth.auth.server.model.SysAuthLogs;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.service.*;
import com.sanyth.auth.server.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Created by JIANGPING on 2020/4/1.
 */
@Component
public class MyAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    @Resource
    LoginLogsService loginLogsService;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    SytJustAuthUserService sytJustAuthUserService;
    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;
    @Resource
    SytSysSafetyService sytSysSafetyService;
    @Autowired
    SytSysParamService paramService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    SysAuthLogsService sysAuthLogsService;

    private void saveLoginLogs(HttpServletRequest request, Authentication authentication) {
        try {
            LoginLogs loginLogs = new LoginLogs();
            loginLogs.setTokenId(request.getRequestedSessionId());
            loginLogs.setIp(ToolsUtil.getIpAddr(request));
            loginLogs.setLoginTime(new Date());
            loginLogs.setUserAgent(request.getHeader("user-agent"));

            SytPermissionAccount account = (SytPermissionAccount) authentication.getPrincipal();
            loginLogs.setHumanCode(account.getHumancode());
            loginLogs.setAddress(ToolsUtil.getAddressByIp(loginLogs.getIp()));
            loginLogsService.save(loginLogs);
            // 更新用户的登录时间
            account.setLogintime(new Date());
            account.setLogininfo(ToolsUtil.getIpAddr(request));
            iSytPermissionAccountService.updateInfo(account);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("记录登录日志出现异常", e);
        }
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws ServletException, IOException {
        // saveLoginLogs(request, authentication);
        logger.info("Success hanlder");  // 这里加入需要的处理
        String redirectUrl = "/index";      // 缺省的登陆成功页面
        SavedRequest savedRequest = (SavedRequest) request.getSession().getAttribute("SPRING_SECURITY_SAVED_REQUEST");
        if (savedRequest != null) {
            redirectUrl = savedRequest.getRedirectUrl();
            request.getSession().removeAttribute("SPRING_SECURITY_SAVED_REQUEST");
            // 记录认证日志
            saveAuthLogs(request, savedRequest, authentication);
        }
        boolean isChangePasswd = false;
        // 判断是否首次登录需要修改密码
        SytSysParam sysParam = paramService.get("isFirstLoginChangePassword");
        if(sysParam != null && Objects.equals(Constants.HAS_YES, sysParam.getValue())){
            SytPermissionAccount sytAccount = (SytPermissionAccount) authentication.getPrincipal();
            SytPermissionAccount account = iSytPermissionAccountService.getByHumancode(sytAccount.getHumancode());
            if(account.getLogintime() == null){
                // 跳转密码修改页面
                redirectUrl = "/#/changePasswd";
                isChangePasswd = true;
            }
        }
        saveLoginLogs(request, authentication);
        Cookie sessionCookie = new Cookie("sessionId", request.getSession().getId());
        response.addCookie(sessionCookie);
        sytSysSafetyService = (SytSysSafetyService) SpringTool.getApplicationContext().getBean("sytSysSafetyService");
        // 查询密码安全策略：用户密码有效期 mmyxq  单位是：天
        Map<String, Object> strategySafetyeMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_SAFETY);
        if(strategySafetyeMap != null && strategySafetyeMap.get("mmyxq") != null
                && StringUtils.isNotBlank(strategySafetyeMap.get("mmyxq").toString())
                && !Objects.equals("0", strategySafetyeMap.get("mmyxq").toString())
                && !Objects.equals("longTime", strategySafetyeMap.get("mmyxq").toString())) {
            SytPermissionAccount sytAccount = (SytPermissionAccount) authentication.getPrincipal();
            SytPermissionAccount account = iSytPermissionAccountService.getByHumancode(sytAccount.getHumancode());
            long mmyxq = Long.parseLong(strategySafetyeMap.get("mmyxq").toString());
            long now  = System.currentTimeMillis();                            // 当前时间
            long interval = mmyxq * 24 * 60 * 60 * 1000L;
            long deadline = account.getModifypasstime() == null ? 0L : account.getModifypasstime().getTime() + interval; // 过期时间
            if(now>deadline){
                // 跳转密码修改页面
                redirectUrl = "/#/changePasswd";
                isChangePasswd = true;
            }
        }
        String uri = request.getRequestURI();
        if(uri.indexOf("user/auth/callback/") > -1){
            response.sendRedirect(redirectUrl);
        }

        Map<String, String> result = new HashMap<>();
        result.put("message", ErrorInfo.CODE_MSG_00000);
        result.put("redirectUrl", redirectUrl);
        if (isChangePasswd) {
            SytSysParam param = paramService.get(Constants.MODIFY_PASSWORD_REDIRECT_URL);
            if (param != null) {
                result.put("redirectLogout", param.getValue());
            }
        }
        SecJS.newDynamicKey(request);
        Resp.render(response, ErrorInfo.CODE_00000, result);
    }

    private void saveAuthLogs(HttpServletRequest request, SavedRequest savedRequest, Authentication authentication) {
        try {
            String[] parameterValues = savedRequest.getParameterValues("client_id");
            if (parameterValues != null && parameterValues.length > 0) {
                String client_id = parameterValues[0];
                SysAuthLogs authLogs = new SysAuthLogs();
                authLogs.setIp(ToolsUtil.getIpAddr(request));
                authLogs.setLoginTime(new Date());
                authLogs.setUserAgent(request.getHeader("user-agent"));
                SytPermissionAccount account = (SytPermissionAccount) authentication.getPrincipal();
                authLogs.setHumanCode(account.getHumancode());
                authLogs.setAddress(ToolsUtil.getAddressByIp(authLogs.getIp()));
                authLogs.setClientId(client_id);
                sysAuthLogsService.save(authLogs);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("记录认证日志出现异常", e);
        }
    }
}
