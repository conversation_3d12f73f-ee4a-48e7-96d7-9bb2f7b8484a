package com.sanyth.auth.server.core.provider;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.auth.server.core.common.SpringTool;
import com.sanyth.auth.server.core.exception.ValidateException;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.util.AESUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

public class TokenAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    @Resource
    SytPermissionAccountMapper sytPermissionAccountMapper;
    @Resource
    private RedisTemplate redisTemplate;

    public static final String IMOOC_FORM_TOKEN = "token";
    public static final String IMOOC_FORM_CALLBACK_URL = "url";

    private String tokenParameter = IMOOC_FORM_TOKEN;
    private String urlParameter = IMOOC_FORM_CALLBACK_URL;
    private boolean postOnly = true;

    public TokenAuthenticationFilter() {
        super(new AntPathRequestMatcher("/tokenLogin", "POST"));
    }

    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (postOnly && !request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }

        String token = request.getParameter(tokenParameter);
        if (StringUtils.isBlank(token)) {
            throw new ValidateException("令牌错误");
        }
        String decrypt = AESUtil.decrypt(token).trim();
        String[] split = decrypt.split("\\.");
        String uName = split[0].trim();
        /*String timestamp = split[1].trim();
        if (StringUtils.isNotBlank(timestamp)) {
            long l = Long.parseLong(timestamp);
            long l1 = System.currentTimeMillis();
            if ((l1 - l) > 20000) {
                throw new ValidateException("验证超时");
            }
        }*/
        String url = request.getParameter(urlParameter);
        redisTemplate = (RedisTemplate) SpringTool.getApplicationContext().getBean("redisTemplate");
        addCallbakUrl(url);
        sytPermissionAccountMapper = (SytPermissionAccountMapper) SpringTool.getApplicationContext().getBean("sytPermissionAccountMapper");
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("HUMANCODE", uName);
        SytPermissionAccount account = sytPermissionAccountMapper.selectOne(wrapper);
        String humancode = "";
        if (null != account) {
            humancode = account.getHumancode();
        }else{
            throw new ValidateException("用户不存在");
        }

        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(humancode, account.getPassword());
        setDetails(request, authRequest);
        return this.getAuthenticationManager().authenticate(authRequest);
    }

    protected void setDetails(HttpServletRequest request, UsernamePasswordAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    public void setPostOnly(boolean postOnly) {
        this.postOnly = postOnly;
    }

    public void addCallbakUrl(String url) {
        if (StringUtils.isNotBlank(url)) {
            HashMap<Object, Object> map = new HashMap<>();
            map.put("url", url);
            String key = "TOKEN_AUTH_CALLBACK_URL";
            redisTemplate.opsForHash().putAll(key, map);
            redisTemplate.expire(key, 30, TimeUnit.SECONDS);
        }
    }
}
