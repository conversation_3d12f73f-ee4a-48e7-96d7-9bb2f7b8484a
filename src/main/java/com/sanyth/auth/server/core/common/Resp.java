package com.sanyth.auth.server.core.common;

import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Created by JIANGPING on 2020/4/7.
 */
public class Resp<T> implements java.io.Serializable {

    private String code;
    private T info;

    public Resp() {
        this.code = ErrorInfo.CODE_00000;
    }

    public Resp(T info) {
        this.code = ErrorInfo.CODE_00000;
        this.info = info;
    }

    public Resp(String code, T info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public T getInfo() {
        return info;
    }

    public void setInfo(T info) {
        this.info = info;
    }

    public static Resp<String> success() {
        return new Resp(ErrorInfo.CODE_00000, ErrorInfo.CODE_MSG_00000);
    }

    public static <T> Resp<T> success(T info) {
        return new Resp(ErrorInfo.CODE_00000, info);
    }

    public static <T> Resp<T> success(String code, T info) {
        return new Resp(code, info);
    }

    public static Resp<String> error(String errorMsg) {
        return new Resp(ErrorInfo.CODE_00001, errorMsg);
    }

    public static Resp<String> error() {
        return new Resp(ErrorInfo.CODE_00001, ErrorInfo.CODE_MSG_00001);
    }

    public static <T> void render(HttpServletResponse response, String code, T info) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            response.setContentType("text/json;charset=utf-8");
            String s = objectMapper.writeValueAsString(new Resp<T>(code, info));
            response.getWriter().println(s);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
