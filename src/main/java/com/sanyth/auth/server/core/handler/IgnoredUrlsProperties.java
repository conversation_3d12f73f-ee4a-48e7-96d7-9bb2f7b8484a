package com.sanyth.auth.server.core.handler;

import cn.hutool.core.util.ReUtil;
import com.sanyth.auth.server.core.annotation.IgnorePermission;
import com.sanyth.auth.server.core.annotation.LimitPermission;
import lombok.Data;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

@Data
@Configuration
@ConfigurationProperties(prefix = "ignored")
public class IgnoredUrlsProperties implements InitializingBean, ApplicationContextAware {

    private static final Pattern PATTERN = Pattern.compile("\\{(.*?)\\}");
    private List<String> urls = new ArrayList<>();

    private List<String> limitUrls = new ArrayList<>();

    private ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() {
        RequestMappingHandlerMapping mapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();

        map.keySet().forEach(info -> {
            HandlerMethod handlerMethod = map.get(info);
            IgnorePermission ignoreMethod = AnnotationUtils.findAnnotation(handlerMethod.getMethod(), IgnorePermission.class);
            Optional.ofNullable(ignoreMethod).ifPresent(inner -> info.getPatternsCondition().getPatterns()
                    .forEach(url -> urls.add(ReUtil.replaceAll(url, PATTERN, "*"))));
            IgnorePermission ignoreController = AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), IgnorePermission.class);
            Optional.ofNullable(ignoreController).ifPresent(inner -> info.getPatternsCondition().getPatterns()
                    .forEach(url -> urls.add(ReUtil.replaceAll(url, PATTERN, "*"))));
            LimitPermission limitMethod = AnnotationUtils.findAnnotation(handlerMethod.getMethod(), LimitPermission.class);
            Optional.ofNullable(limitMethod).ifPresent(inner -> info.getPatternsCondition().getPatterns()
                    .forEach(url -> limitUrls.add(ReUtil.replaceAll(url, PATTERN, "*"))));
            LimitPermission limitController = AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), LimitPermission.class);
            Optional.ofNullable(limitController).ifPresent(inner -> info.getPatternsCondition().getPatterns()
                    .forEach(url -> limitUrls.add(ReUtil.replaceAll(url, PATTERN, "*"))));
        });
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
