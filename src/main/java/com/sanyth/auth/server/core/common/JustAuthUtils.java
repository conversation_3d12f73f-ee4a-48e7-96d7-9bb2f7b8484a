package com.sanyth.auth.server.core.common;

import com.sanyth.auth.server.justauth.JustAuthConstants;
import com.sanyth.auth.server.model.SytJustAuthConfig;
import com.sanyth.auth.server.service.SytJustAuthConfigService;
import com.sanyth.auth.server.service.SytSysParamService;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.request.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description:
 *
 * @author: wdl
 * @create: 2021/3/25
 */
@Component
public class JustAuthUtils {

    @Autowired
    SytSysParamService paramService;
    @Autowired
    SytJustAuthConfigService sytJustAuthConfigService;

    /**
     * 创建授权请求
     * @param oauthType 授权类型
     * @return
     */
    public AuthRequest getAuthRequest(String oauthType) {
        String clientId = "";
        String clientSecret = "";
        String redirectUri = "";
        String agentId = "";    // 企业微信，授权方的网页应用ID
        String alipayPublicKey = "";    // 支付宝公钥：当选择支付宝登录时，该值可用对应“RSA2(SHA256)密钥”中的“支付宝公钥”
        if(StringUtils.isNotBlank(oauthType)){
            oauthType = oauthType.toUpperCase();
        }
        SytJustAuthConfig sytJustAuthConfig = sytJustAuthConfigService.getByAuthType(oauthType);
        if(sytJustAuthConfig != null){
            clientId = sytJustAuthConfig.getClientId();
            clientSecret = sytJustAuthConfig.getClientSecret();
            redirectUri = sytJustAuthConfig.getRedirectUri();
            agentId = sytJustAuthConfig.getAgentId();
            alipayPublicKey = sytJustAuthConfig.getAlipayPublicKey();
        }
        switch(oauthType) {
            case JustAuthConstants.AUTH_TYPE_DINGTALK:
                return new AuthDingTalkRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .ignoreCheckState(true)
                        .build());
            case JustAuthConstants.AUTH_TYPE_BAIDU:
                return new AuthBaiduRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .build());
            case JustAuthConstants.AUTH_TYPE_QQ:
                return new AuthQqRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .build());
            case JustAuthConstants.AUTH_TYPE_WECHAT:
                return new AuthWeChatOpenRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .build());
            case JustAuthConstants.AUTH_TYPE_WECHAT_ENTERPRISE: // 企业微信二维码
                return new AuthWeChatEnterpriseQrcodeRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .agentId(agentId)
                        .ignoreCheckState(true)
                        .build());
            case JustAuthConstants.AUTH_TYPE_WECHAT_ENTERPRISE_WEB:   // 企业微信网页
                return new AuthWeChatEnterpriseWebRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .agentId(agentId)
                        .ignoreCheckState(true)
                        .build());
            case JustAuthConstants.AUTH_TYPE_WEIBO:
                return new AuthWeiboRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .build());
            case JustAuthConstants.AUTH_TYPE_ALIPAY:
                return new AuthAlipayRequest(AuthConfig.builder()
                        .clientId(clientId)
                        .clientSecret(clientSecret)
                        .redirectUri(redirectUri)
                        .alipayPublicKey(alipayPublicKey)
                        .build());
            default:
                throw new AuthException(AuthResponseStatus.UNSUPPORTED);
        }
    }

}
