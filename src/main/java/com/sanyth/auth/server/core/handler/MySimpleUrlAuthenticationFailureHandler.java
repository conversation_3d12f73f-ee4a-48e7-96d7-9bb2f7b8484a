package com.sanyth.auth.server.core.handler;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 验证失败处理
 * Created by JIANGPING on 2020/3/27.
 */
//@Component
public class MySimpleUrlAuthenticationFailureHandler extends SimpleUrlAuthenticationFailureHandler {

    public MySimpleUrlAuthenticationFailureHandler() {
        this.setDefaultFailureUrl("/?error");
    }

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        request.setAttribute("MESSAGE", exception.getMessage());
        super.onAuthenticationFailure(request, response, exception);
    }
}
