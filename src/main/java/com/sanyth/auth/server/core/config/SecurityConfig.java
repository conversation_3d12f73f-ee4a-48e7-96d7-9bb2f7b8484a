package com.sanyth.auth.server.core.config;

import cn.hutool.log.StaticLog;
import com.sanyth.auth.server.core.common.Constants;
import com.sanyth.auth.server.core.handler.*;
import com.sanyth.auth.server.core.provider.TokenAuthenticationSecurityConfig;
import com.sanyth.auth.server.filter.MyFilterSecurityInterceptor;
import com.sanyth.auth.server.filter.MyUsernamePasswordAuthenticationFilter;
import com.sanyth.auth.server.filter.MyVerificationFilter;
import com.sanyth.auth.server.justauth.MyOAuth2ClientAuthenticationSecurityConfig;
import com.sanyth.auth.server.mobile.SmsCodeAuthenticationSecurityConfig;
import com.sanyth.auth.server.service.MyUserDetailsService;
import com.sanyth.auth.server.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.session.security.SpringSessionBackedSessionRegistry;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * Created by JIANGPING on 2020/2/13.
 */
@Configuration
@EnableWebSecurity
//@EnableRedisHttpSession(redisNamespace = "spring:session:session",maxInactiveIntervalInSeconds = 1800)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${spring.resources.static-locations}")
    String resourcesStaticLocations;
    @Autowired
    MyUserDetailsService userService;
    @Autowired
    MyAuthenticationSuccessHandler myAuthenticationSuccessHandler;
    @Autowired
    MyAuthenticationFailHander myAuthenticationFailHander;
    @Autowired
    MyAccessDeniedHandler myAccessDeniedHandler;
    //    @Autowired
//    MyFilterInvocationSecurityMetadataSource myFilterInvocationSecurityMetadataSource;
    @Autowired
    MyAccessDecisionManager myAccessDecisionManager;
    @Autowired
    IgnoredUrlsProperties ignoredUrlsProperties;
    @Autowired
    private SmsCodeAuthenticationSecurityConfig smsCodeAuthenticationSecurityConfig;
    @Autowired
    private TokenAuthenticationSecurityConfig tokenAuthenticationSecurityConfig;
    @Autowired
    private MyOAuth2ClientAuthenticationSecurityConfig myOAuth2ClientAuthenticationSecurityConfig;

    @Resource
    private RedisUtil redisUtil;
    /**
     * redis获取sessionRepository
     * RedisIndexedSessionRepository实现 FindByIndexNameSessionRepository接口
     */
    @Autowired
    //不加@Lazy这个会报什么循环引用...
    // Circular reference involving containing bean '.RedisHttpSessionConfiguration'
    @Lazy
    private FindByIndexNameSessionRepository<? extends Session> sessionRepository;

    @Override
    public void configure(WebSecurity web) throws Exception {
        web.ignoring().antMatchers(resourcesStaticLocations);
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userService).passwordEncoder(new PasswordEncoder() {
            @Override
            public String encode(CharSequence charSequence) {
                if(charSequence.toString().startsWith("{MD5}")){
                    return charSequence.toString().substring(5);
                }
                return DigestUtils.md5DigestAsHex(charSequence.toString().getBytes());
            }

            @Override
            public boolean matches(CharSequence charSequence, String s) {
                String encode="";
                if(charSequence.toString().startsWith("{MD5}")){
                    encode = charSequence.toString().substring(5);
                }else{
                    encode = DigestUtils.md5DigestAsHex(charSequence.toString().getBytes());
                }
                return s.equals(encode);
            }
        });
    }

    public static void main(String[] args) {
        System.out.println(DigestUtils.md5DigestAsHex("123456".getBytes()));
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
//        String patterns = "/,/findPassword,/js/**,/css/**,/img/**,/fonts/**,/cdn/**,/svg/**,/util/**,/static/**,/timeout,/captcha,/sec_js,/oauth/**,/favicon.ico,/file/view/**,/file/download/**";
//        List<String> antPatterns = new ArrayList<>();
//        antPatterns.addAll(Arrays.asList(patterns.split(",")));

//        antPatterns.add("/oauthClientDetails/**");
//        antPatterns.add("/resource/**");
//        antPatterns.add("/role/**");
        List<String> antPatterns = ignoredUrlsProperties.getUrls();//只取了urls 顾limitUrls需要登录

        http.csrf().disable();
        http.headers().frameOptions().disable();
        http.addFilterAt(myUsernamePasswordAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(myVerificationFilter(), UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(myFilterSecurityInterceptor(), FilterSecurityInterceptor.class)
                .authorizeRequests()                    // 授权配置
                /*.withObjectPostProcessor(new ObjectPostProcessor<FilterSecurityInterceptor>() {

                    @Override
                    public <O extends FilterSecurityInterceptor> O postProcess(O o) {
                        o.setSecurityMetadataSource(myFilterInvocationSecurityMetadataSource);
                        o.setAccessDecisionManager(myAccessDecisionManager);
                        return o;
                    }
                })*/
                .antMatchers(antPatterns.toArray(new String[antPatterns.size()])).permitAll()   // 无需认证的请求路径
                .anyRequest().authenticated()           // 任何请求, 都需认证
                .and().formLogin().loginPage("/").loginProcessingUrl("/login").permitAll()  // 指定登录页, 登录请求地址
                .and().logout().permitAll().invalidateHttpSession(true).deleteCookies("JSESSIONID").logoutSuccessHandler(logoutSuccessHandler())
                .and().exceptionHandling().authenticationEntryPoint(new MyAuthenticationEntryPoint())/*sessionManagement().invalidSessionUrl("/timeout")*//*.maximumSessions(-1).expiredUrl("/timeout")*/;


        http.exceptionHandling().accessDeniedHandler(myAccessDeniedHandler);    // 无权访问自定以handler处理
        http.apply(smsCodeAuthenticationSecurityConfig);
        http.apply(tokenAuthenticationSecurityConfig);
        http.apply(myOAuth2ClientAuthenticationSecurityConfig);
        http.sessionManagement()    //session超时管理
                .maximumSessions(1)
                .maxSessionsPreventsLogin(false) //false表示不阻止登录，就是新的覆盖旧的
                .sessionRegistry(sessionRegistry())
                .expiredSessionStrategy(new MySessionInformationExpiredStrategy())
        ;
    }

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new PasswordEncoder() {
            @Override
            public String encode(CharSequence charSequence) {
                return charSequence.toString();
            }

            @Override
            public boolean matches(CharSequence charSequence, String s) {
                return Objects.equals(charSequence.toString(), s);
            }
        };
    }

    @Bean
    public MyVerificationFilter myVerificationFilter() throws Exception {
        return new MyVerificationFilter(myAuthenticationFailHander);
    }

    @Bean
    public MyUsernamePasswordAuthenticationFilter myUsernamePasswordAuthenticationFilter() throws Exception {
        MyUsernamePasswordAuthenticationFilter filter = new MyUsernamePasswordAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManager());
        filter.setAuthenticationFailureHandler(myAuthenticationFailHander);
        filter.setAuthenticationSuccessHandler(myAuthenticationSuccessHandler);
        return filter;
    }

    @Bean
    public LogoutSuccessHandler logoutSuccessHandler() {
        return new LogoutSuccessHandler() {
            @Override
            public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
                /*try {
                    User user = (User) authentication.getPrincipal();
                    logger.info("USER : {} LOGOUT SUCCESS ! ", user.getUsername());
                } catch (Exception e) {
                    logger.error("printStackTrace", e);
                }*/
                redisUtil.delete(Constants.USER_ONLINE_REDIS_KEY + request.getRequestedSessionId());
                String contextPath = request.getContextPath();
                String redirectUrl = request.getParameter("service");
                if (StringUtils.isNotEmpty(redirectUrl)) {
                    response.sendRedirect(redirectUrl);
                } else {
                    if (null == contextPath)
                        contextPath = "";
                    response.sendRedirect(contextPath + "/");
                }
            }
        };
    }

    @Bean
    public MyFilterSecurityInterceptor myFilterSecurityInterceptor() {
        MyFilterSecurityInterceptor myFilterSecurityInterceptor = new MyFilterSecurityInterceptor();
        myFilterSecurityInterceptor.setMyAccessDecisionManager(myAccessDecisionManager);
        return myFilterSecurityInterceptor;
    }

    /**
     * 是spring session为Spring Security提供的,
     * 用于在集群环境下控制会话并发的会话注册表实现
     * @return
     */
    @Bean
    public SpringSessionBackedSessionRegistry sessionRegistry(){
        return new SpringSessionBackedSessionRegistry<>(sessionRepository);
    }

}
