package com.sanyth.auth.server.core.config;

import com.sanyth.auth.server.core.common.Constants;
import com.sanyth.auth.server.core.common.Resp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
public class MySessionInformationExpiredStrategy implements SessionInformationExpiredStrategy {

    @Override
    public void onExpiredSessionDetected(SessionInformationExpiredEvent event) throws IOException {
        if (log.isDebugEnabled()) {
           log.debug("{} {}", event.getSessionInformation(), Constants.SESSION_EVICT);
        }
        HttpServletResponse response = event.getResponse();
        Resp.render(response, "403", Constants.SESSION_EVICT);
    }
}