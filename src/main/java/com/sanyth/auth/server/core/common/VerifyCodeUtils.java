package com.sanyth.auth.server.core.common;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.service.SytSysParamService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

/**
 * @Description:生成验证码  发送验证码
 *
 * @author: wdl
 * @create: 2021/3/25
 */
@Component
public class VerifyCodeUtils {

    @Autowired
    SytSysParamService paramService;
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 创建指定数量的随机字符串
     * @param numberFlag 是否是数字
     * @param length 长度
     * @return
     */
    public String createRandom(boolean numberFlag, int length){
        String retStr = "";
        String strTable = numberFlag ? "1234567890" : "1234567890abcdefghijkmnpqrstuvwxyz";
        int len = strTable.length();
        boolean bDone = true;
        do {
            retStr = "";
            int count = 0;
            for (int i = 0; i < length; i++) {
                double dblR = Math.random() * len;
                int intR = (int) Math.floor(dblR);
                char c = strTable.charAt(intR);
                if (('0' <= c) && (c <= '9')) {
                    count++;
                }
                retStr += strTable.charAt(intR);
            }
            if (count >= 2) {
                bDone = false;
            }
        } while (bDone);
        return retStr;
    }

    /**
     * 发送短信
     * @param account 用户信息
     * @param verifyCode 验证码
     * @param yzmlx 验证码类型，区分是登录时的验证码，还是修改密码时的验证码
     * @return
     */
    public Resp sendSms(SytPermissionAccount account, String verifyCode, String yzmlx) {
        try {
            String phoneNumbers = account.getTelmobile1();  // 手机号
//            String humancode = account.getHumancode();      //  用户账号
//            String humanname = account.getHumanname();      //  用户名
            //必填:短信签名-可在短信控制台中找到
            String signName = "";
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            String templateCode = "";
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            String outId = "yourOutId";

            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            String templateParamName = "code";

            //替换成你的AK
            String accessKeyId = "";//你的accessKeyId
            String accessKeySecret = "";//你的accessKeySecret

            // TODO 这些参数值从参数表获取
            SytSysParam param = paramService.get("alSms_SignName");
            if(param != null){
                signName = param.getValue();
            }
            param = paramService.get("alSms_templateCode");
            if("change".equalsIgnoreCase(yzmlx)){
                SytSysParam paramTmp = paramService.get("alSms_templateCode_change");
                if(paramTmp != null){
                    param = paramTmp;
                }
            }
            if(param != null){
                templateCode = param.getValue();
            }
            param = paramService.get("alSms_templateParamName");
            if(param != null){
                templateParamName = param.getValue();
            }
            param = paramService.get("alSms_accessKeyId");
            if(param != null){
                accessKeyId = param.getValue();
            }
            param = paramService.get("alSms_accessKeySecret");
            if(param != null){
                accessKeySecret = param.getValue();
            }
            if(StringUtils.isBlank(signName) || StringUtils.isBlank(templateCode) || StringUtils.isBlank(templateParamName)
                 || StringUtils.isBlank(accessKeyId) || StringUtils.isBlank(accessKeySecret)){
                return Resp.error();
            }

            //设置超时时间-可自行调整
            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
            System.setProperty("sun.net.client.defaultReadTimeout", "10000");
            //初始化ascClient需要的几个参数
            final String product = "Dysmsapi";//短信API产品名称（短信产品名固定，无需修改）
            final String domain = "dysmsapi.aliyuncs.com";//短信API产品域名（接口地址固定，无需修改）
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);

            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，
            //批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,验证码类型的短信推荐使用单条调用的方式；
            //发送国际/港澳台消息时，接收号码格式为国际区号+号码，如“85200000000”
            request.setPhoneNumbers(phoneNumbers);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
            request.setTemplateCode(templateCode);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,
            //比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            //参考：request.setTemplateParam("{\"变量1\":\"值1\",\"变量2\":\"值2\",\"变量3\":\"值3\"}")
//            if(StringUtils.isNotBlank(templateParamName)) {
//                request.setTemplateParam("{ \""+ templateParamName +"\":\"" + verifyCode+ "\"}");
//            }
            String templateParam = "{ ";
            if(StringUtils.isNotBlank(templateParamName)) {
                templateParam += " \""+ templateParamName +"\":\"" + verifyCode+ "\"";
            }
//            if("change".equalsIgnoreCase(yzmlx)){
//                templateParam += " ,\"name\":\"" + humanname+ "\"";
//                templateParam += " ,\"zh\":\"" + humancode+ "\"";
//            }
            templateParam += "} ";
            request.setTemplateParam(templateParam);
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");

//            if(StringUtils.isNotBlank(outId)) {
//                //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//                request.setOutId(outId);
//            }

            //请求失败这里会抛ClientException异常
            SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);

            System.err.println("RequestId=="+sendSmsResponse.getRequestId());
            System.out.println("Code=="+sendSmsResponse.getCode());
            System.err.println("Message=="+sendSmsResponse.getMessage());
            System.out.println("BizId=="+sendSmsResponse.getBizId());
            String responseCode = sendSmsResponse.getCode();
            if(responseCode != null && responseCode.equals("OK")) {
                //请求成功
                return Resp.success();
            }else{
                Resp.error(sendSmsResponse.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Resp.error("服务异常");
    }

    /**
     * 发送邮件
     * @param to 接收的邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return
     */
    public Resp sendEmail(String to, String subject, String content) throws Exception {
        try{
            String from = "";  // 发送的邮箱
            String pwd = "";   // 授权码
            // 企业QQ邮箱smtp.exmail.qq.com QQ邮箱:smtp.qq.com 网易163:smtp.163.com
            String smtpHost = ""; // 邮箱的smtphost

            // TODO 这些参数值从参数表获取
            SytSysParam param = paramService.get("emailSmtpHost");
            if(param != null){
                smtpHost = param.getValue();
            }
            param = paramService.get("emailFrom");
            if(param != null){
                from = param.getValue();
            }
            // 授权码
            param = paramService.get("emailAuthCode");
            if(param != null){
                pwd = param.getValue();
            }

            if(StringUtils.isBlank(to) || StringUtils.isBlank(subject) || StringUtils.isBlank(content)){
//                System.out.println("郵件地址（"+to+"）、主題（"+subject+"）、內容（"+content+"）有為空項，不符合發送條件");
                return Resp.error();
            }
            Properties props = new Properties();
            props.setProperty("mail.transport.protocol", "smtp"); // 使用的协议（JavaMail规范要求）
            props.setProperty("mail.smtp.host", smtpHost); // 发件人的邮箱的 SMTP服务器地址
            props.setProperty("mail.smtp.auth", "true"); // 请求认证，参数名称与具体实现有关
            // 创建Session实例对象
            Session session = Session.getDefaultInstance(props);
            // 创建MimeMessage实例对象
            MimeMessage message = new MimeMessage(session);
            // 设置发件人
            message.setFrom(new InternetAddress(from));
            // 设置收件人
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
            // 设置发送日期
            message.setSentDate(new Date());
            // 设置邮件主题
            message.setSubject(subject);
            // 设置纯文本内容的邮件正文
            message.setText(content);
            // 保存并生成最终的邮件内容
            message.saveChanges();
            // 设置为debug模式, 可以查看详细的发送 log
            session.setDebug(true);
            // 获取Transport对象
            Transport transport = session.getTransport("smtp");
            // 第2个参数需要填写的是QQ邮箱的SMTP的授权码
            transport.connect(from, pwd);
            // 发送，message.getAllRecipients() 获取到的是在创建邮件对象时添加的所有收件人, 抄送人, 密送人
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();
//            System.out.println("電子郵件接口執行完成，執行成功！");
            return Resp.success();
        }catch(Exception e){
            e.printStackTrace();
        }
//        System.out.println("电子邮件接口异常！");
        return Resp.error("服务异常");
    }

    /**
     * 判断验证码发送时间段
     * @return java.lang.String
     */
    public String checkVerifyCodeSendTime(){
        String result = null;
        try {
            SytSysParam param = paramService.get("verifyCodeSendTime"); // 验证码发送时间段；时间格式为10:00-20:25
            String time = "";
            if(param != null){
                time = param.getValue();
            }
            // 判断当前时间段是否在指定时间段内
            if(StringUtils.isNotBlank(time)){
                SimpleDateFormat dateFormater = new SimpleDateFormat("HHmm");
                String dateNow = dateFormater.format(new Date());
                System.out.println(dateNow);
                String[] timeArr = time.split("-");
                Long timeNow = Long.parseLong(dateNow);
                if(timeArr.length == 2){
                    Long timeStart = Long.parseLong(timeArr[0].replace(":", ""));
                    Long timeEnd = Long.parseLong(timeArr[1].replace(":", ""));
                    if (timeNow >= timeStart && timeNow <= timeEnd) {
                        return null;
                    } else {
                        result = "请在每天的 " + time + " 进行操作";
                    }
                } else if(timeArr.length == 1){
                    Long timeStart = Long.parseLong(timeArr[0].replace(":", ""));
                    if (timeNow >= timeStart) {
                        return null;
                    } else {
                        result = "请在每天的 " + time + " 以后进行操作";
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
    /**
     * 判断已发送验证码的次数是否超过限制次数
     * @param key
     * @param hashkey
     * @return boolean true，表示已达到限制次数；否则未超过次数
     */
    public boolean checkVerifyCodeSendCount(String key, String hashkey){
        try {
            SytSysParam paramCount = paramService.get("verifyCodeSendCount"); // 验证码发送次数
            if(paramCount != null && StringUtils.isNotBlank(paramCount.getValue())){
                Long count = Long.parseLong(paramCount.getValue());
                if(count <= 0){
                    return true;
                }
                // 已发送验证码次数
                Object str = redisTemplate.opsForHash().get(key, hashkey);
                if(str != null){
                    long sendCount = Long.parseLong(String.valueOf(str));
                    if(sendCount >= count){
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

}
