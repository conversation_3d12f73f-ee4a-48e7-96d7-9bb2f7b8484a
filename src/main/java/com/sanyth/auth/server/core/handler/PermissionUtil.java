package com.sanyth.auth.server.core.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

@Component
public class PermissionUtil {

    private static IgnoredUrlsProperties ignoredUrlsProperties;

    @Autowired
    public void setIgnoredUrlsProperties(IgnoredUrlsProperties ignoredUrlsProperties) {
        this.ignoredUrlsProperties = ignoredUrlsProperties;
    }

    static AntPathMatcher antPathMatcher = new AntPathMatcher();
//    public static final List<String> ignorePermissionList = Arrays.asList(
//            "/", "/index", "/css/**", "/js/**", "/img/**", "/fonts/**", "/cdn/**",
//            "/svg/**", "/util/**", "/static/**", "/captcha*", "/favicon.ico", "/oauth/**", "/user/**","/findPassword*"
//            ,"/file/view/**","/file/download/**");

    public static boolean ignore(String requestUrl) {
        boolean matchFlag = false;
        for (String s : ignoredUrlsProperties.getUrls()) {
            if (antPathMatcher.match(s, requestUrl)) {
                matchFlag = true;
                break;
            }
        }
        return matchFlag;
    }

    /**
     * 是否是无需检查权限但需登录的请求
     * @param requestUrl
     * @return
     */
    public static boolean notCheck(String requestUrl) {
        boolean matchFlag = false;
        for (String s : ignoredUrlsProperties.getLimitUrls()) {
            if (antPathMatcher.match(s, requestUrl)) {
                matchFlag = true;
                break;
            }
        }
        return matchFlag;
    }
}
