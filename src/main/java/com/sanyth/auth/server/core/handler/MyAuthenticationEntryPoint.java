package com.sanyth.auth.server.core.handler;

import com.sanyth.auth.server.core.common.Resp;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class MyAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException, ServletException {
        String method = request.getMethod();
        String accept = request.getHeader("Accept");
        String contentType = request.getHeader("Content-Type");
        // 前端异步请求session失效不跳转至登录页, 临时处理下...
        if (method.equalsIgnoreCase("GET")) {
            response.sendRedirect("/");
        } else {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            Resp.render(response, "401", "unauthorized");
        }
    }
}
