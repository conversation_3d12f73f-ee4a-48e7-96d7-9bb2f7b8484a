package com.sanyth.auth.server.core.common;

import com.alibaba.fastjson.JSON;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.sanyth.auth.server.core.exception.BusinessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created by JIANGPING on 2020/5/26.
 */
@Component
public class FileOperationUtil {
    /*
    * A component required a bean of type 'org.springframework.data.mongodb.MongoDbFactory' that could not be found
    * org.springframework.data.mongodb.MongoDbFactory-->org.springframework.data.mongodb.core.MongoTemplate
    * */
    @Resource
    private MongoTemplate mongoDbFactory;
    @Resource
    private GridFsTemplate gridFsTemplate;
    @Value("${server.servlet.context-path:#{'/'}}")
    protected String contextPath;
    /**
     * 保存附件
     *
     * @param files
     * @return
     * @throws Exception
     */
    public FileInfo save(MultipartFile... files) throws Exception {
//        List<FileInfo> list = new ArrayList<>();
        if (files != null && files.length > 0) {
            FileInfo file = new FileInfo();
            for (MultipartFile m : files) {
                ObjectId objectId = gridFsTemplate.store(m.getInputStream(), m.getOriginalFilename(), m.getContentType());
                file.setId(objectId.toString());
                file.setName(m.getOriginalFilename());
                file.setContentype(m.getContentType());
                file.setUrl(contextPath+"file/view/"+objectId.toString());
//                list.add(file);
            }
            return file;
        }
        throw new BusinessException("上传失败，附件信息不能为空");
    }


    /**
     * 取得附件
     *
     * @param id
     * @return
     */
    public GridFSFile get(String id) throws Exception {
        GridFSFile fsFile = gridFsTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)));
        if (fsFile == null) {
            throw new BusinessException("No file with ID: " + id);
        }
        return fsFile;
    }

    public void download(String id, boolean view, HttpServletRequest request, HttpServletResponse response) throws Exception {
        GridFSFile fsFile = get(id);
        GridFsResource gridFsResource = new GridFsResource(fsFile,
                GridFSBuckets.create(mongoDbFactory.getDb()).
                        openDownloadStream(fsFile.getObjectId()));
        String fileName = fsFile.getFilename().replace(",", "");
        if (request.getHeader("User-Agent").toUpperCase().contains("MSIE") ||
                request.getHeader("User-Agent").toUpperCase().contains("TRIDENT")
                || request.getHeader("User-Agent").toUpperCase().contains("EDGE")) {
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
        } else {
            fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
        }
        String ContentType = "application/octet-stream";
        String ContentDisposition = "attachment";
        if (view) {
            ContentType = ToolsUtil.contentType(fileName.substring(fileName.lastIndexOf(".") + 1));
            ContentDisposition = "inline";
        }
        response.setContentType(ContentType);
        response.setHeader("Content-Disposition", ContentDisposition + ";filename= " + fileName);
        IOUtils.copy(gridFsResource.getInputStream(), response.getOutputStream());
    }

    /**
     * 删除附件
     *
     * @param id
     */
    public void remove(String... id) throws Exception {
        for (String i : id) {
            gridFsTemplate.delete(new Query().addCriteria(Criteria.where("_id").is(i)));
        }
    }

    /**
     * 删除附件
     *
     * @param text
     */
    public void removeByJson(String text) {
        if (StringUtils.isNotEmpty(text)) {
            List<FileInfo> list = JSON.parseArray(text, FileInfo.class);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(f -> {
                    gridFsTemplate.delete(new Query().addCriteria(Criteria.where("_id").is(f.getId())));
                });
            }
        }
    }
}
