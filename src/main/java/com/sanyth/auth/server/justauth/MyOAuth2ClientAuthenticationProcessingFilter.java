package com.sanyth.auth.server.justauth;

import com.sanyth.auth.server.core.common.JustAuthUtils;
import com.sanyth.auth.server.core.common.SpringTool;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;
import com.sanyth.auth.server.service.ISytPermissionAccountService;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class MyOAuth2ClientAuthenticationProcessingFilter extends AbstractAuthenticationProcessingFilter {

    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;
    @Resource
    SytPermissionAccountMapper sytPermissionAccountMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private QywxUserDao qywxUserDao;

    public MyOAuth2ClientAuthenticationProcessingFilter(){
        super(new AntPathRequestMatcher("/user/auth/callback/**", "GET"));
        sytPermissionAccountMapper = (SytPermissionAccountMapper) SpringTool.getApplicationContext().getBean("sytPermissionAccountMapper");
        redisTemplate = (RedisTemplate) SpringTool.getApplicationContext().getBean("redisTemplate");
        iSytPermissionAccountService = SpringTool.getApplicationContext().getBean(ISytPermissionAccountService.class);
        qywxUserDao = SpringTool.getApplicationContext().getBean(QywxUserDao.class);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException, ServletException {
        String uri = request.getRequestURI();
        String oauthType = uri.split("/auth/callback/")[1].toUpperCase();
        String code = request.getParameter("code");
        String state = request.getParameter("state");
        AuthCallback callback = new AuthCallback();
        callback.setCode(code);
        callback.setState(state);
        JustAuthUtils justAuthUtils = (JustAuthUtils) SpringTool.getApplicationContext().getBean("justAuthUtils");
        AuthRequest authRequest = justAuthUtils.getAuthRequest(oauthType);
        AuthResponse authResponse = authRequest.login(callback);
        if(authResponse.getCode() != 2000) {
            throw new AuthenticationServiceException("登录失败：" + authResponse.getMsg());
        }
        SytPermissionAccount account = getUsernamePasswordAuthenticationToken(oauthType, authResponse);
        if (account == null) {
            // 页面提示
            Cookie cookie = new Cookie("loginTips", "nobind");//(key,value)
            cookie.setPath("/");// 这个要设置
            cookie.setMaxAge(60 * 30);// 以秒为单位
            response.addCookie(cookie); //添加Cookie
            throw new AuthenticationServiceException("用户不存在");
        }
        UsernamePasswordAuthenticationToken authenticationToken
                = new UsernamePasswordAuthenticationToken(account.getHumancode(), account.getPassword());
        setDetails(request, authenticationToken);
        return this.getAuthenticationManager().authenticate(authenticationToken);
    }

    protected void setDetails(HttpServletRequest request, UsernamePasswordAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    private SytPermissionAccount getUsernamePasswordAuthenticationToken(String oauthType, AuthResponse authResponse) {
        AuthUser data = (AuthUser)authResponse.getData();
        String userId = data.getUuid();
        String humanCode = null;

        SytPermissionAccount account = iSytPermissionAccountService.getByHumancode(userId);
        if (account == null) {
            QywxUser qywxUserQ = new QywxUser();
            qywxUserQ.setWxId(userId);
            QywxUser qywxUser = qywxUserDao.queryFirst(qywxUserQ);
            if (qywxUser != null) {
                humanCode = qywxUser.getSysId();
            }
        }
        if (StringUtils.isNotBlank(humanCode)) {
            account = iSytPermissionAccountService.getByHumancode(humanCode);
        }
        return account;
    }

}

