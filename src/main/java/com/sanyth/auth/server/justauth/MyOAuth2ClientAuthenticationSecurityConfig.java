package com.sanyth.auth.server.justauth;

import com.sanyth.auth.server.core.handler.MyAuthenticationFailHander;
import com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

@Component
public class MyOAuth2ClientAuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    // 自定义的成功失败处理器
    @Autowired
    private MyAuthenticationSuccessHandler myAuthenticationSuccessHandler;
    @Autowired
    private MyAuthenticationFailHander myAuthenticationFailHander;

    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 加入自定义的filter , provider
     * @param http
     * @throws Exception
     */
    @Override
    public void configure(HttpSecurity http) throws Exception {

        MyOAuth2ClientAuthenticationProcessingFilter myOAuth2ClientAuthenticationProcessingFilter = new MyOAuth2ClientAuthenticationProcessingFilter();
        myOAuth2ClientAuthenticationProcessingFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        myOAuth2ClientAuthenticationProcessingFilter.setAuthenticationSuccessHandler(myAuthenticationSuccessHandler);
        myOAuth2ClientAuthenticationProcessingFilter.setAuthenticationFailureHandler(myAuthenticationFailHander);

        MyOAuth2ClientAuthenticationProvider myOAuth2ClientAuthenticationProvider = new MyOAuth2ClientAuthenticationProvider();
        myOAuth2ClientAuthenticationProvider.setUserDetailsService(userDetailsService);

        http.authenticationProvider(myOAuth2ClientAuthenticationProvider)
                .addFilterAfter(myOAuth2ClientAuthenticationProcessingFilter, UsernamePasswordAuthenticationFilter.class);

    }

}
