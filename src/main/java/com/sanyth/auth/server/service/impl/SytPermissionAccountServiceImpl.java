package com.sanyth.auth.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.core.exception.BusinessException;
import com.sanyth.auth.server.ldap.entity.Person;
import com.sanyth.auth.server.ldap.service.PersonService;
import com.sanyth.auth.server.mapper.*;
import com.sanyth.auth.server.model.*;
import com.sanyth.auth.server.service.ISytPermissionAccountService;
import com.sanyth.auth.server.service.SytSysParamService;
import com.sanyth.auth.server.service.SytSysSafetyService;
import com.sanyth.auth.server.util.ValidationUtils;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by JIANGPING on 2020/5/15.
 */
@Service
public class SytPermissionAccountServiceImpl extends ServiceImpl<SytPermissionAccountMapper, SytPermissionAccount> implements ISytPermissionAccountService {

    @Resource
    SytPermissionAccountMapper sytPermissionAccountMapper;
    @Resource
    SytPermissionAccountRoleMapper sytPermissionAccountRoleMapper;
    @Resource
    SytSysOrganizationMapper sytSysOrganizationMapper;
    @Resource
    SytSysOrganizationUserMapper sytSysOrganizationUserMapper;
    @Resource
    SytPermissionRoleMapper sytPermissionRoleMapper;
    @Resource
    PersonService personService;
    @Resource
    SytSysParamService paramService;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    SytSysSafetyService sytSysSafetyService;


    @Override
    public SytPermissionAccount getByHumancode(String humancode) {
        SytPermissionAccount account = new SytPermissionAccount();
        account.setHumancode(humancode);
        Wrapper<SytPermissionAccount> accountWrapper = buildWrapper(account);
        return getOne(accountWrapper);
    }

    @Override
    public List<SytPermissionAccount> queryList(SytPermissionAccount account) {
        return list(buildWrapper(account));
    }

    @Override
    public List<SytPermissionAccount> queryListAndRole(BaseQuery<Map<String, Object>> query) {
        Page<SytPermissionAccount> page = new Page<>(query.getPage(), query.getPageSize());
        List<SytPermissionAccount> list = sytPermissionAccountMapper.queryList(query.getQueryParam(), page);
        if (!CollectionUtils.isEmpty(list)) {
            List<Map<String, String>> mapGroupByAccount = sytPermissionRoleMapper.queryMapGroupByAccount(null);
            for (SytPermissionAccount account : list) {
                JSONArray roles = new JSONArray();
                for (Map<String, String> stringMap : mapGroupByAccount) {
                    String accountId = stringMap.get("ACCOUNT_ID");
                    if (Objects.equals(accountId, account.getId())) {
                        JSONObject r = new JSONObject();
                        r.put("label", stringMap.get("ROLENAME"));
                        roles.add(r);
                    }
                }
                account.setRole(roles);
            }
        }
        return list;
    }

    @Override
    public Page<SytPermissionAccount> queryPage(BaseQuery<Map<String, Object>> query) {
        Page<SytPermissionAccount> page = new Page<>(query.getPage(), query.getPageSize());
        List<SytPermissionAccount> list = sytPermissionAccountMapper.queryList(query.getQueryParam(), page);
        if (!CollectionUtils.isEmpty(list)) {
            for (SytPermissionAccount account : list) {
                JSONArray roles = new JSONArray();
                List<SytPermissionRole> roleList = sytPermissionRoleMapper.getByAccount(account.getId());
                if (!CollectionUtils.isEmpty(roleList)) {
                    for (SytPermissionRole sytPermissionRole : roleList) {
                        JSONObject r = new JSONObject();
                        r.put("label", sytPermissionRole.getRolename());
                        r.put("value", sytPermissionRole.getId());
                        roles.add(r);
                    }
                }
                account.setRole(roles);

                // 组织关系
                QueryWrapper<SytSysOrganizationUser> param = new QueryWrapper<>();
                param.eq("USER_ID", account.getId());
                List<SytSysOrganizationUser> organizationUsers = sytSysOrganizationUserMapper.selectList(param);
                JSONArray org = new JSONArray();
                if (!CollectionUtils.isEmpty(organizationUsers)) {
                    for (SytSysOrganizationUser organizationUser : organizationUsers) {
                        org.add(organizationUser.getOrganizationId());
                    }
                }
                account.setOrganization(org);
            }
        }
        page.setRecords(list);
        return page;
    }

    @Transactional
    @Override
    public void edit(JSONObject params) throws Exception {
        JSONObject accountJson = params.getJSONObject("account");
        JSONArray roleJson = params.getJSONArray("role");
        JSONArray organizationJson = params.getJSONArray("organization");
        if (accountJson == null || roleJson == null || organizationJson == null) {
            throw new BusinessException(ErrorInfo.MSG_0003);
        }

        SytPermissionAccount account = JSON.toJavaObject(accountJson, SytPermissionAccount.class);
        ValidationUtils.validate(account);
        StringBuilder organizationBuider = new StringBuilder();
        StringBuilder orgshortnameBuider = new StringBuilder();
       /* Wrapper<SytSysOrganization> organizationWrapper = new EntityWrapper<>();
        organizationWrapper.isNull("parent");
        List<SytSysOrganization> organizations = sytSysOrganizationMapper.selectList(organizationWrapper);
        if (!CollectionUtils.isEmpty(organizations)) {
            organizationBuider.append(organizations.get(0).getOrgname()).append(";");
            if (StringUtils.isNotBlank(organizations.get(0).getOrgshortname())) {
                orgshortnameBuider.append(organizations.get(0).getOrgshortname()).append(";");
            }
        }*/

        List<SytSysOrganizationUser> organizationUsers = new ArrayList<>();
        for (int i = 0; i < organizationJson.size(); i++) {
            Object obj = organizationJson.get(i);
            String o = (String) obj;
            SytSysOrganization organization = sytSysOrganizationMapper.selectById(o);
            if (i == 0 && organization.getParent() != null) {
                SytSysOrganization parent = sytSysOrganizationMapper.selectById(organization.getParent());
                organizationBuider.append(parent.getOrgname()).append(";");
                orgshortnameBuider.append(parent.getOrgshortname()).append(";");
            }
            organizationBuider.append(organization.getOrgname()).append(",");
            orgshortnameBuider.append(organization.getOrgshortname()).append(",");
            SytSysOrganizationUser ou = new SytSysOrganizationUser();
            ou.setOrganizationId(o);
            organizationUsers.add(ou);
        }
        account.setOrganizationnames(organizationBuider.deleteCharAt(organizationBuider.length() - 1).toString());
        account.setOrgshortname(orgshortnameBuider.deleteCharAt(orgshortnameBuider.length() - 1).toString());

        SytPermissionAccount accountParam = new SytPermissionAccount();
        accountParam.setHumancode(account.getHumancode());
        List<SytPermissionAccount> list = list(buildWrapper(accountParam));// 当前登录账号是否存在

        if (StringUtils.isBlank(account.getId())) {
            if (!CollectionUtils.isEmpty(list))
                throw new BusinessException(ErrorInfo.MSG_0004);
            String pass = passwordHandler(account);
            account.setHumanpassword(DigestUtils.md5DigestAsHex(pass.getBytes()));
            account.setCreatedate(new Date());
            account.setModifypasstime(new Date());
            save(account);
            saveLdapPerson(account, pass);
        } else {
            if (!CollectionUtils.isEmpty(list) && !list.get(0).getHumancode().equals(account.getHumancode()))
                throw new BusinessException(ErrorInfo.MSG_0004);

            SytPermissionAccount accountIndb = getById(account.getId());
            accountIndb.setHumancode(accountJson.getString("humancode"));
            accountIndb.setHumanname(accountJson.getString("humanname"));
            accountIndb.setTelmobile1(accountJson.getString("telmobile1"));
            accountIndb.setDutyid(accountJson.getString("dutyid"));
            accountIndb.setIdtype(accountJson.getString("idtype"));
            accountIndb.setIdcode(accountJson.getString("idcode"));
            accountIndb.setTeloffice(accountJson.getString("teloffice"));
            accountIndb.setTelhome(accountJson.getString("telhome"));
            accountIndb.setTelmobile2(accountJson.getString("telmobile2"));
            if (StringUtils.isNotEmpty(accountJson.getString("birthday")))
                accountIndb.setBirthday(new SimpleDateFormat("yyyy-MM-dd").parse(accountJson.getString("birthday")));
            accountIndb.setEmail(accountJson.getString("email"));
            accountIndb.setPostalcode(accountJson.getString("postalcode"));
            accountIndb.setValidflag(accountJson.getDouble("validflag"));
            accountIndb.setDisplayorder(accountJson.getDouble("displayorder"));
            accountIndb.setSex(accountJson.getString("sex"));
            accountIndb.setAddress(accountJson.getString("address"));
            accountIndb.setValidfromdate(accountJson.getDate("validfromdate"));
            accountIndb.setValidtodate(accountJson.getDate("validtodate"));
            accountIndb.setEmployeeType(accountJson.getString("employeeType"));

//            accountIndb.setOrganizationnames(organizationBuider.deleteCharAt(organizationBuider.length() - 1).toString());
            accountIndb.setOrganizationnames(organizationBuider.toString().trim());
            accountIndb.setOrgshortname(orgshortnameBuider.toString().trim());
            accountIndb.setHumanpassword(null);
            accountIndb.setCurrentState(account.getCurrentState());
            updateById(accountIndb);
            saveLdapPerson(accountIndb, "");
            sytPermissionAccountRoleMapper.delete(new QueryWrapper<SytPermissionAccountRole>().eq("ACCOUNT_ID", accountIndb.getId()));
            sytSysOrganizationUserMapper.delete(new QueryWrapper<SytSysOrganizationUser>().eq("USER_ID", accountIndb.getId()));
        }

        for (int i = 0; i < roleJson.size(); i++) {
            String roleid = (String) roleJson.get(i);
            SytPermissionAccountRole accountRole = new SytPermissionAccountRole();
            accountRole.setAccountId(account.getId());
            accountRole.setRoleId(roleid);
            sytPermissionAccountRoleMapper.insert(accountRole);
        }

        for (SytSysOrganizationUser ou : organizationUsers) {
            ou.setUserId(account.getId());
            sytSysOrganizationUserMapper.insert(ou);
        }

    }

    @Transactional
    @Override
    public void delete(String... id) throws Exception {
        if (id != null && id.length > 0) {
            sytPermissionAccountRoleMapper.delete(new QueryWrapper<SytPermissionAccountRole>().in("ACCOUNT_ID", id));
            sytSysOrganizationUserMapper.delete(new QueryWrapper<SytSysOrganizationUser>().in("USER_ID", id));
            personService.deleteByIds(id);
            removeByIds(Arrays.asList(id));
        } else {
            throw new BusinessException(ErrorInfo.MSG_0003);
        }
    }

    private QueryWrapper<SytPermissionAccount> buildWrapper(SytPermissionAccount account) {
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(account.getHumancode()))
            wrapper.eq("humancode", account.getHumancode());
        if (StringUtils.isNotBlank(account.getTelmobile1()))
            wrapper.eq("telmobile1", account.getTelmobile1());
        if (StringUtils.isNotBlank(account.getHumanpassword()))
            wrapper.eq("humanpassword", account.getHumanpassword());
        return wrapper;
    }

    @Transactional
    @Override
    public Resp updateInfo(SytPermissionAccount account) {
        if (StringUtils.isBlank(account.getId())) {
            return Resp.error("id不能为空");
        }
        SytPermissionAccount accountIndb = getById(account.getId());
        if(accountIndb == null){
            return Resp.error("用户不存在");
        }
        if (StringUtils.isNotBlank(account.getHumanname())) {
            accountIndb.setHumanname(account.getHumanname());
        }
        accountIndb.setSex(account.getSex());
//        accountIndb.setTelmobile1(account.getTelmobile1());
        accountIndb.setEmail(account.getEmail());
        accountIndb.setIdtype(account.getIdtype());
        accountIndb.setIdcode(account.getIdcode());
        accountIndb.setAddress(account.getAddress());
        accountIndb.setLogintime(account.getLogintime());
        accountIndb.setLogininfo(account.getLogininfo());
        accountIndb.setHumanpassword(null);
        updateById(accountIndb);
        saveLdapPerson(accountIndb,"");
        return Resp.success();
    }

    /**
     * 编辑ldap里的用户信息
     * @param account
     */
    private void saveLdapPerson(SytPermissionAccount account,String password) {
        Person person = personService.findById(account.getHumancode());
        if (person == null) {
            person = new Person(account.getHumancode(), account.getHumanname(), account.getHumanname(), account.getEmail(), account.getTelmobile1()
                    , LdapEncoderByMd5(password), account.getEmployeeType()
                    , (account.getValidtodate() == null ? "2099-12-31 00:00:00" : DateFormatUtils.format(account.getValidtodate(), "yyyy-MM-dd HH:mm:ss"))
                    , account.getOrganizationnames(), account.getOrgshortname()
                    , account.getIdcode());
            person.setOlcAllows(account.getValidflag() == 0 ? true : false);
            personService.saveBean(person);
        } else {
            person.setUid(account.getHumancode());
            person.setCn(account.getHumanname());
            person.setSn(account.getHumanname());
            person.setMail(account.getEmail());
            person.setTelephoneNumber(account.getTelmobile1());
            person.setOlcTimeLimit((account.getValidtodate() == null ? "2099-12-31 00:00:00" : DateFormatUtils.format(account.getValidtodate(), "yyyy-MM-dd HH:mm:ss")));
            person.setEmployeeType(account.getEmployeeType());
            person.setOu(account.getOrganizationnames());
            person.setOrganizationName(account.getOrgshortname());
            person.setIdcode(account.getIdcode());
            person.setOlcAllows(account.getValidflag() == 0 ? true : false);
            if (StringUtils.isEmpty(password)) {
                personService.updateBean(person);
            } else {
                person.setUserPassword(LdapEncoderByMd5(password));
//                person.setUserPassword( person.getUserPassword());
                personService.changePassword(person);
            }
        }
    }

    /**
     * ldap md5加密
     * @param psw
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    @SneakyThrows
    public static String LdapEncoderByMd5(String psw) {
        MessageDigest md5= MessageDigest.getInstance("MD5");
        BASE64Encoder base64en = new BASE64Encoder();
        String md5psw=base64en.encode(md5.digest(psw.getBytes("utf-8")));
        return "{MD5}"+ md5psw;
    }

    @Transactional
    @Override
    public Resp changePasswd(String oldpassword, String newpassword, String newpasswords, CurrentUser currentUser) {
        if(StringUtils.isNotBlank(oldpassword) && StringUtils.isNotBlank(newpassword) && StringUtils.isNotBlank(newpasswords)){
            String humancode = currentUser.getHumancode();
            // 校验密码修改策略
            Resp tmp = checkStrategyModify(humancode, newpassword, newpasswords);
            if (tmp != null) {
                return tmp;
            }
            SytPermissionAccount account = new SytPermissionAccount();
            account.setHumancode(humancode);
            account.setHumanpassword(DigestUtils.md5DigestAsHex(oldpassword.getBytes()));
            List<SytPermissionAccount> list = queryList(account);
            if(list != null && list.size() > 0){
                SytPermissionAccount currentAccount = list.get(0);
                currentAccount.setHumanpassword(DigestUtils.md5DigestAsHex(newpassword.getBytes()));
                currentAccount.setModifypasstime(new Date());
                updateById(currentAccount);
                saveLdapPerson(currentAccount, newpassword);
                return Resp.success();
            }else{
                return Resp.error(currentUser.getHumancode()+"用户原始密码不正确");
            }
        }
        return Resp.error("请完整填写密码");
    }

    @Transactional
    @Override
    public Resp resetPassword(Map<String, String> param) {
        String type = param.get("verifyType"); // 区分是手机验证，还是邮箱验证
        if(StringUtils.isBlank(type)){
            return Resp.error("参数type不能为空");
        }
        String humancode = param.get("humancode");
        if(StringUtils.isBlank(humancode)){
            return Resp.error("账号不能为空");
        }
        String verifyCode = param.get("verifyCode");
        if(StringUtils.isBlank(verifyCode)){
            return Resp.error("验证码不能为空");
        }

        SytPermissionAccount account = new SytPermissionAccount();
        account.setHumancode(humancode);
        List<SytPermissionAccount> list = queryList(account);
        if(list != null && list.size() > 0){
            SytPermissionAccount accountIndb = list.get(0);
            String phone = accountIndb.getTelmobile1();
            String email = accountIndb.getEmail();
            String code = "";
//            Map tmp = redisTemplate.opsForHash().entries("FIND_PASSWORD_INFO");
            String key = "FIND_PASSWORD_INFO" + "_" + humancode + "_";
            if("phone".equals(type)){
                key += phone;
            }else if("email".equals(type)){
                key += email;
            }

            // 获取验证码
            Object str = redisTemplate.opsForHash().get(key, "code");
            if(str != null){
                code = String.valueOf(str);
            }
            if(StringUtils.isBlank(code)){
                return Resp.error("验证码已过期");
            }
            if(!verifyCode.equals(code)){
                return Resp.error("验证码无效");
            }
            String newpassword = param.get("newpassword");
            String newpasswords = param.get("newpasswords");
            if(StringUtils.isNotBlank(newpassword) && StringUtils.isNotBlank(newpasswords)){
                // 校验密码修改策略
                Resp tmp = checkStrategyModify(humancode, newpassword, newpasswords);
                if (tmp != null) {
                    return tmp;
                }
                accountIndb.setHumancode(humancode);
                accountIndb.setHumanpassword(DigestUtils.md5DigestAsHex(newpassword.getBytes()));
                accountIndb.setModifypasstime(new Date());
                updateById(accountIndb);
                saveLdapPerson(accountIndb, newpassword);
                // 删除验证码
                redisTemplate.opsForHash().delete(key, "code");
                return Resp.success();
            }
        }
        return Resp.error("请完整填写密码");
    }

    /**
     * 校验密码修改策略
     * @param humancode
     * @param newpassword
     * @param newpasswords
     */
    private Resp checkStrategyModify(String humancode, String newpassword, String newpasswords) {
        if(!newpassword.equals(newpasswords)){
            return Resp.error("两次新密码输入不一致");
        }
        // 查询密码修改策略
        Map<String, Object> strategyModifyMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_MODIFY);
        String pattern = "";
        if(strategyModifyMap != null){
            if(strategyModifyMap.get("passRegExp") != null && StringUtils.isNotBlank(strategyModifyMap.get("passRegExp").toString())){
                pattern = strategyModifyMap.get("passRegExp").toString();
            }
            // 是否允许包含用户名
            if(strategyModifyMap.get("sfyhm") != null){
                String sfyhm = strategyModifyMap.get("sfyhm").toString();
                if(Objects.equals(Constants.HAS_NO, sfyhm) && newpasswords.indexOf(humancode) > -1){
                    return Resp.error("密码不允许包含用户名");
                }
            }
        }
        if (!ToolsUtil.checkPasswordStrength(pattern, newpassword)&&!ToolsUtil.checkPasswordStrength(pattern, newpasswords)) {
            return Resp.error("密码强度不符");
        }
        return null;
    }

    @Transactional
    @Override
    public Resp resetPasswordByAdmin(JSONObject param) {
        String humancode = param.getString("humancode");
        SytPermissionAccount account = this.getByHumancode(humancode);
        if (account == null) {
            return Resp.error("未找到用户,操作失败");
        }
        String password = passwordHandler(account);
        account.setHumanpassword(DigestUtils.md5DigestAsHex(password.getBytes()));
        account.setModifypasstime(new Date());
        updateById(account);
        saveLdapPerson(account, password);
        return Resp.success();
    }

    @Override
    public Resp getAccountAuditData(JSONObject param) {
//        String zhsjlx = param.getString("zhsjlx"); // 审计项目
        HashMap<String, Object> map = new HashMap<>();
        // 查询200天以上未登录的账号
        Map<String, String> zhsjlxMap = new HashMap<>();
        zhsjlxMap.put("zhsjlx", "wdlrs");
        map.putAll(sytPermissionAccountMapper.queryNumberByZhsjlx(zhsjlxMap));     // 90天以上未登录人数
        zhsjlxMap.put("zhsjlx", "wxgmmrs");
        map.putAll(sytPermissionAccountMapper.queryNumberByZhsjlx(zhsjlxMap));   // 90天以上未修改密码人数
        zhsjlxMap.put("zhsjlx", "zhgqrs");
        map.putAll(sytPermissionAccountMapper.queryNumberByZhsjlx(zhsjlxMap));    // 账号过期人数
        zhsjlxMap.put("zhsjlx", "wbdzzrs");
        map.putAll(sytPermissionAccountMapper.queryNumberByZhsjlx(zhsjlxMap));   // 账号未绑定组织人数
        zhsjlxMap.put("zhsjlx", "wfpjsrs");
        map.putAll(sytPermissionAccountMapper.queryNumberByZhsjlx(zhsjlxMap));   // 账号未分配角色人数
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataMap", map);
        return Resp.success(dataMap);
    }

    /**
     * 用户密码统一生成规则，适用于添加用户、导入用户、重置用户密码，生成按优先级排序
     * 1.参数表默认密码+身份证后六位
     * 2.参数表默认密码+用户账户名
     * 3.身份证后六位
     * 4.用户账户名
     * @param account
     * @return
     */
    public String passwordHandler(SytPermissionAccount account){
        String pass = "";
        SytSysParam sysParam = paramService.get(Constants.DEFAULT_PASSWORD);    // 默认密码前缀
        if (sysParam != null) {
            pass = sysParam.getValue();
        }
        // 查询密码生成策略
        Map<String, Object> strategyGenerateMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_GENERATE);
        if(strategyGenerateMap != null && strategyGenerateMap.get("sccl") != null){
            // 设置的密码
            String passwordTmp = "";
            String sccl = strategyGenerateMap.get("sccl").toString();
            switch (sccl) {
                // 取身份证件号后6位
                case "idcard":
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
                        passwordTmp = account.getIdcode().substring(account.getIdcode().length() - 6);
                    }
                    break;
                // 取账号名
                case "loginnumber":
                    passwordTmp = account.getHumancode();
                    break;
                // 指定密码
                case "manageset":
                    if(strategyGenerateMap.get("setnumber") != null){
                        passwordTmp = strategyGenerateMap.get("setnumber").toString();
                    }
                    break;
                default:
                    break;
            }
            if(StringUtils.isNotBlank(passwordTmp)){
                return pass + passwordTmp;
            }
        }
        if (sysParam != null) {
            pass = sysParam.getValue();
            // 有身份证号，则参数表值加上身份证后六位；没有身份证号的，则加上用户账号
            if (StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
                pass += account.getIdcode().substring(account.getIdcode().length() - 6);
            } else {
                pass += account.getHumancode();
            }
        } else if (StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
            pass = account.getIdcode().substring(account.getIdcode().length() - 6);
        } else {
            pass = account.getHumancode();
        }
        return pass;
    }

    @Transactional
    @Override
    public Resp verifyChangePhoneCode(Map<String, String> param, CurrentUser currentUser) {
        String type = param.get("verifyType"); // 区分是手机验证，还是邮箱验证
        if(StringUtils.isBlank(type)){
            return Resp.error("参数type不能为空");
        }
        String phoneOrEmail = param.get("phoneOrEmail");
        if(StringUtils.isBlank(phoneOrEmail)){
            return Resp.error("参数phoneOrEmail不能为空");
        }
        String verifyCode = param.get("verifyCode");
        if(StringUtils.isBlank(verifyCode)){
            return Resp.error("验证码不能为空");
        }
        String code = "";
        String key = "CHANGE_PHONE_INFO_"+phoneOrEmail;

        // 获取验证码
        Object str = redisTemplate.opsForHash().get(key, "code");
        if(str != null){
            code = String.valueOf(str);
        }
        if(StringUtils.isBlank(code)){
            return Resp.error("验证码已过期");
        }
        if(!verifyCode.equals(code)){
            return Resp.error("验证码无效");
        }
        String verifiedKey = "CHANGE_PHONE_INFO_VERIFIED_KEY"+currentUser.getHumancode();
        Map<String, String> map = new HashMap<>();
        map.put("verified", "true");
        redisTemplate.opsForHash().putAll(verifiedKey, map);
        redisTemplate.expire(verifiedKey, 60 * 5, TimeUnit.SECONDS);
        return Resp.success();
    }

    @Transactional
    @Override
    public Resp updatePhone(Map<String, String> param,CurrentUser currentUser) {
        String type = param.get("verifyType"); // 区分是手机验证，还是邮箱验证
        if(StringUtils.isBlank(type)){
            return Resp.error("参数type不能为空");
        }
        String phoneOrEmail = param.get("phoneOrEmail");
        if(StringUtils.isBlank(phoneOrEmail)){
            return Resp.error("参数phoneOrEmail不能为空");
        }
        String verifyCode = param.get("verifyCode");
        if(StringUtils.isBlank(verifyCode)){
            return Resp.error("验证码不能为空");
        }
        String code = "";
        String key = "CHANGE_PHONE_INFO_"+phoneOrEmail;

        // 获取验证码
        Object str = redisTemplate.opsForHash().get(key, "code");
        if(str != null){
            code = String.valueOf(str);
        }
        if(StringUtils.isBlank(code)){
            return Resp.error("验证码已过期");
        }
        if(!verifyCode.equals(code)){
            return Resp.error("验证码无效");
        }
        SytPermissionAccount account = getByHumancode(currentUser.getHumancode());
        account.setTelmobile1(phoneOrEmail);
        saveOrUpdate(account);
        saveLdapPerson(account, "");
        return Resp.success();
    }

    @Override
    public Resp updateDisplayorder(Map<String, Object> param) {
        Integer currentPage = (Integer) param.get("currentPage");
        Integer pageSize = (Integer) param.get("pageSize");
        List<String> idList = (List<String>) param.get("idList");
        if (currentPage == null || pageSize == null || CollectionUtils.isEmpty(idList)) {
            return Resp.error("参数错误");
        }
        Integer order = (currentPage - 1) * pageSize;
        for (String id : idList) {
            SytPermissionAccount account = this.getById(id);
            if (account == null) {
                continue;
            }
            account.setDisplayorder(Double.parseDouble(String.valueOf(order)));
            updateById(account);
            order++;
        }
        return Resp.success();
    }
}
