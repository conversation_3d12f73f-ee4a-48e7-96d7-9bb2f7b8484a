package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.JustAuthUtils;
import com.sanyth.auth.server.core.common.SpringTool;
import com.sanyth.auth.server.core.exception.BusinessException;
import com.sanyth.auth.server.mapper.SytJustAuthUserMapper;
import com.sanyth.auth.server.model.SytJustAuthUser;
import com.sanyth.auth.server.service.SytJustAuthUserService;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class SytJustAuthUserServiceImpl extends ServiceImpl<SytJustAuthUserMapper, SytJustAuthUser> implements SytJustAuthUserService {

    @Resource
    SytJustAuthUserMapper sytJustAuthUserMapper;

    @Override
    public List<SytJustAuthUser> queryList(SytJustAuthUser onlineUser) {
        return list(buildWrapper(onlineUser));
    }

    @Override
    public Page<SytJustAuthUser> queryPage(BaseQuery<SytJustAuthUser> query) {
        Page<SytJustAuthUser> page = new Page<>(query.getPage(), query.getPageSize());
        QueryWrapper<SytJustAuthUser> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public void delete(String... id) throws Exception {
        if (id != null && id.length > 0) {
            removeByIds(Arrays.asList(id));
        } else {
            throw new BusinessException(ErrorInfo.MSG_0003);
        }
    }

    @Override
    public List<SytJustAuthUser> findByHumanCode(String humanCode) {
        QueryWrapper<SytJustAuthUser> wrapper = new QueryWrapper<>();
        wrapper.eq("humancode", humanCode);
        return list(wrapper);
    }

    @Override
    public SytJustAuthUser getByUserIdAndOauthType(String userId, String oauthType) {
        SytJustAuthUser account = new SytJustAuthUser();
        account.setUserId(userId);
        account.setAuthType(oauthType);
        Wrapper<SytJustAuthUser> accountWrapper = buildWrapper(account);
        return getOne(accountWrapper);
    }

    @Override
    public SytJustAuthUser getByHumanCodeAndOauthType(String humanCode, String oauthType) {
        SytJustAuthUser account = new SytJustAuthUser();
        account.setHumancode(humanCode);
        account.setAuthType(oauthType);
        Wrapper<SytJustAuthUser> accountWrapper = buildWrapper(account);
        return getOne(accountWrapper);
    }

    @Override
    public void bindCallback(String oauthType, String code, String state) {
        AuthCallback callback = new AuthCallback();
        callback.setCode(code);
        callback.setState(state);
        JustAuthUtils justAuthUtils = (JustAuthUtils) SpringTool.getApplicationContext().getBean("justAuthUtils");
        AuthRequest authRequest = justAuthUtils.getAuthRequest(oauthType);
        AuthResponse authResponse = authRequest.login(callback);
        if(authResponse.getCode() == 2000){
            AuthUser authUser = (AuthUser)authResponse.getData();
            SytJustAuthUser justAuthUser = new SytJustAuthUser();
            justAuthUser.setUserId(authUser.getUuid());
            justAuthUser.setAuthType(oauthType);
            justAuthUser.setHumancode(state);
            justAuthUser.setToken(authUser.getToken().getAccessToken());
            justAuthUser.setCreateDate(new Date());
            this.save(justAuthUser);
        }
    }

    private QueryWrapper<SytJustAuthUser> buildWrapper(SytJustAuthUser onlineUser) {
        QueryWrapper<SytJustAuthUser> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(onlineUser.getHumancode()))
            wrapper.eq("humancode", onlineUser.getHumancode());
        if (StringUtils.isNotBlank(onlineUser.getUserId()))
            wrapper.eq("userId", onlineUser.getUserId());
        if (StringUtils.isNotBlank(onlineUser.getAuthType()))
            wrapper.eq("authType", onlineUser.getAuthType());
        return wrapper;
    }
}
