package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.mapper.ResourceLinkMapper;
import com.sanyth.auth.server.mapper.RoleResourceLinkMapper;
import com.sanyth.auth.server.mapper.RoleResourceMapper;
import com.sanyth.auth.server.mapper.SytPermissionRoleMapper;
import com.sanyth.auth.server.model.ResourceLink;
import com.sanyth.auth.server.model.RoleResource;
import com.sanyth.auth.server.model.RoleResourceLink;
import com.sanyth.auth.server.model.SytPermissionRole;
import com.sanyth.auth.server.service.RoleResourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务实现
 * Created by JIANGPING on 2020-02-04.
 */
@Service
@Transactional
public class RoleResourceServiceImpl extends ServiceImpl<RoleResourceMapper, RoleResource> implements RoleResourceService {
    private Logger log = LoggerFactory.getLogger(RoleResourceServiceImpl.class);
    @Resource
    ResourceLinkMapper resourceLinkMapper;
    @Resource
    RoleResourceLinkMapper roleResourceLinkMapper;
    @Resource
    SytPermissionRoleMapper sytPermissionRoleMapper;
    @Resource
    RedisTemplate redisTemplate;

    static final String PERMISSION_ROLE_INFO = "PERMISSION_ROLE_INFO";

    @Override
    public Map<String, List<String>> permission() {
        Map<String, List<String>> result = redisTemplate.opsForHash().entries(PERMISSION_ROLE_INFO);
        if (!CollectionUtils.isEmpty(result)) {
            log.info("缓存获取权限");
            return result;
        } else {
            log.info("更新缓存权限");
            return updatePermission();
        }
    }

    @Override
    public Map<String, List<String>> updatePermission() {
        Map<String, List<String>> result = new HashMap<>();
        List<ResourceLink> resourceLinks = resourceLinkMapper.selectList(new QueryWrapper<>());
        List<RoleResourceLink> roleResourceLinks = roleResourceLinkMapper.selectList(new QueryWrapper<>());
        List<SytPermissionRole> sytPermissionRoles = sytPermissionRoleMapper.selectList(new QueryWrapper<>());
        Map<String, String> roleMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(sytPermissionRoles)) {
            for (SytPermissionRole sytPermissionRole : sytPermissionRoles) {
                roleMap.put(sytPermissionRole.getId(), sytPermissionRole.getRolekey());
            }
        }

        for (ResourceLink resourceLink : resourceLinks) {
            if (StringUtils.isEmpty(resourceLink.getPattern())) {
                continue;
            }
            List<String> roleList = new ArrayList<>();
            for (RoleResourceLink roleResourceLink : roleResourceLinks) {
                if (resourceLink.getId().equals(roleResourceLink.getResourceId())) {
                    String roleKey = roleMap.get(roleResourceLink.getRoleId());
                    if (!StringUtils.isEmpty(roleKey)) {
                        roleList.add(roleKey);
                    }
                }
            }
            result.put(resourceLink.getPattern(), roleList);
        }
        redisTemplate.opsForHash().putAll(PERMISSION_ROLE_INFO, result);
        return result;
    }

}
