package com.sanyth.auth.server.service.impl;

import com.mzt.logapi.beans.LogRecord;
import com.mzt.logapi.service.ILogRecordService;
import com.sanyth.auth.server.model.SytLogRecordPO;
import com.sanyth.auth.server.service.SytLogRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class DbLogRecordServiceImpl implements ILogRecordService {

    @Resource
    private SytLogRecordService logRecordService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void record(LogRecord logRecord) {
        log.info("【logRecord】log={}", logRecord);
        SytLogRecordPO logRecordPO = SytLogRecordPO.from(logRecord);
        logRecordService.save(logRecordPO);
    }

    @Override
    public List<LogRecord> queryLog(String bizNo, String type) {
        return null;
    }

    @Override
    public List<LogRecord> queryLogByBizNo(String bizNo, String type, String subType) {
        return null;
    }

}