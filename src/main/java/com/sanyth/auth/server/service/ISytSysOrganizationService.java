package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.SytSysOrganization;

import java.util.List;

/**
 * 组织机构表 服务接口
 * Created by JIANGPING on 2020-05-14.
 */
public interface ISytSysOrganizationService extends IService<SytSysOrganization> {
    List<SytSysOrganization> getOrganizationByUser(String userId);
    List<SytSysOrganization> queryList(SytSysOrganization organization);
    List<SytSysOrganization> queryParentList();
    Page<SytSysOrganization> queryPage(BaseQuery<SytSysOrganization> query);
}
