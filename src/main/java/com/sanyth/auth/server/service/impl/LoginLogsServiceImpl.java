package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.mapper.LoginLogsMapper;
import com.sanyth.auth.server.model.LoginLogs;
import com.sanyth.auth.server.service.LoginLogsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class LoginLogsServiceImpl extends ServiceImpl<LoginLogsMapper, LoginLogs> implements LoginLogsService {

    @Override
    public Page<LoginLogs> queryPage(Page<LoginLogs> page, LoginLogs loginLogs) {
        QueryWrapper wrapper = buildWrapper(loginLogs);
        wrapper.orderByDesc("login_time");
        return page(page, wrapper);
    }

    private QueryWrapper buildWrapper(LoginLogs loginLogs){
        QueryWrapper<LoginLogs> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(loginLogs.getHumanCode()))
            wrapper.eq("human_code", loginLogs.getHumanCode());
        return wrapper;
    }
}
