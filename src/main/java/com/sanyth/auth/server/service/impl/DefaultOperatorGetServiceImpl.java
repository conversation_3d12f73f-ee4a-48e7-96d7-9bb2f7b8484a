package com.sanyth.auth.server.service.impl;

import com.mzt.logapi.beans.Operator;
import com.mzt.logapi.service.IOperatorGetService;
import com.sanyth.auth.server.util.SecurityUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class DefaultOperatorGetServiceImpl implements IOperatorGetService {

    @Override
    public Operator getUser() {
         return Optional.ofNullable(SecurityUtils.getUser())
                        .map(a -> new Operator(a.getHumancode()))
                        .orElseThrow(()->new IllegalArgumentException("user is null"));
       
    }
}