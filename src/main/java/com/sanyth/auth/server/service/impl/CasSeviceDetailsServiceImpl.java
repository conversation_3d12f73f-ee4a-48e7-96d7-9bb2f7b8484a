package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.mapper.CasClientDetailsMapper;
import com.sanyth.auth.server.model.CasClientDetails;
import com.sanyth.auth.server.service.CasClientDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 服务实现
 * Created by JIANGPING on 2020-04-07.
 */
@Service
@Transactional
public class CasSeviceDetailsServiceImpl extends ServiceImpl<CasClientDetailsMapper, CasClientDetails> implements CasClientDetailsService {

}
