package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.mapper.SytSysOrganizationUserMapper;
import com.sanyth.auth.server.model.SytSysOrganizationUser;
import com.sanyth.auth.server.service.ISytSysOrganizationUserService;
import org.springframework.stereotype.Service;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-15.
 */
@Service
public class SytSysOrganizationUserServiceImpl extends ServiceImpl<SytSysOrganizationUserMapper, SytSysOrganizationUser> implements ISytSysOrganizationUserService {

    @Override
    public Page<SytSysOrganizationUser> queryPage(BaseQuery<SytSysOrganizationUser> query) {
        Page<SytSysOrganizationUser> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytSysOrganizationUser> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    private QueryWrapper<SytSysOrganizationUser> buildWrapper(SytSysOrganizationUser sytSysOrganizationUser) {
        QueryWrapper<SytSysOrganizationUser> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
