package com.sanyth.auth.server.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.SytJustAuthConfig;

import java.util.List;

public interface SytJustAuthConfigService extends IService<SytJustAuthConfig> {

    List<SytJustAuthConfig> queryList(SytJustAuthConfig onlineUser);

    Page<SytJustAuthConfig> queryPage(BaseQuery<SytJustAuthConfig> query);

    void delete(String... id) throws Exception;

    SytJustAuthConfig getByAuthType(String authType);

    void edit(JSONObject params);
}
