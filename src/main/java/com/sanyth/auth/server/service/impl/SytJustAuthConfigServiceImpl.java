package com.sanyth.auth.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.exception.BusinessException;
import com.sanyth.auth.server.mapper.SytJustAuthConfigMapper;
import com.sanyth.auth.server.model.SytJustAuthConfig;
import com.sanyth.auth.server.service.SytJustAuthConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class SytJustAuthConfigServiceImpl extends ServiceImpl<SytJustAuthConfigMapper, SytJustAuthConfig> implements SytJustAuthConfigService {

    @Resource
    SytJustAuthConfigMapper sytJustAuthConfigMapper;

    @Override
    public List<SytJustAuthConfig> queryList(SytJustAuthConfig sytJustAuthConfig) {
        return list(buildWrapper(sytJustAuthConfig));
    }

    @Override
    public Page<SytJustAuthConfig> queryPage(BaseQuery<SytJustAuthConfig> query) {
        Page<SytJustAuthConfig> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytJustAuthConfig> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public void delete(String... id) throws Exception {
        if (id != null && id.length > 0) {
            removeByIds(Arrays.asList(id));
        } else {
            throw new BusinessException(ErrorInfo.MSG_0003);
        }
    }

    @Override
    public SytJustAuthConfig getByAuthType(String authType) {
        SytJustAuthConfig sytJustAuthConfig = new SytJustAuthConfig();
        sytJustAuthConfig.setAuthType(authType.toUpperCase());
        Wrapper<SytJustAuthConfig> accountWrapper = buildWrapper(sytJustAuthConfig);
        return getOne(accountWrapper);
    }

    @Override
    public void edit(JSONObject params) {
        SytJustAuthConfig authConfig = JSON.toJavaObject(params, SytJustAuthConfig.class);
        Date date = new Date();
        authConfig.setAuthType(authConfig.getAuthType().toUpperCase());
        if(StringUtils.isBlank(authConfig.getId())){    // 新增
            authConfig.setCreateDate(date);
            authConfig.setModifyDate(date);
            save(authConfig);
        }else{
            // 修改
            SytJustAuthConfig justAuthConfig = getById(authConfig.getId());
            authConfig.setCreateDate(justAuthConfig.getCreateDate());
            authConfig.setModifyDate(date);
            updateById(authConfig);
        }
    }

    private QueryWrapper<SytJustAuthConfig> buildWrapper(SytJustAuthConfig sytJustAuthConfig) {
        QueryWrapper<SytJustAuthConfig> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(sytJustAuthConfig.getId()))
            wrapper.eq("id", sytJustAuthConfig.getId());
        if (StringUtils.isNotBlank(sytJustAuthConfig.getAuthType()))
            wrapper.eq("authType", sytJustAuthConfig.getAuthType().toUpperCase());
        return wrapper;
    }
}
