package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.SytPermissionRole;

import java.util.List;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-15.
 */
public interface ISytPermissionRoleService extends IService<SytPermissionRole> {
        void edit(SytPermissionRole param);
        List<SytPermissionRole> listManage();
        Page<SytPermissionRole> queryPage(BaseQuery<SytPermissionRole> query);
        List<SytPermissionRole> getByAccount(String accountId);
}
