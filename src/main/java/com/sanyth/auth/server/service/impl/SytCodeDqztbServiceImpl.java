package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.mapper.SytCodeDqztbMapper;
import com.sanyth.auth.server.model.SytCodeDqztb;
import com.sanyth.auth.server.service.ISytCodeDqztbService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ${table.comment} 服务实现
 */
@Service
public class SytCodeDqztbServiceImpl extends ServiceImpl<SytCodeDqztbMapper, SytCodeDqztb> implements ISytCodeDqztbService {

    @Override
    public Page<SytCodeDqztb> queryPage(BaseQuery<SytCodeDqztb> query) {
        Page<SytCodeDqztb> page = new Page<>(query.getPage(), query.getPageSize());
        SytCodeDqztb obj = query.getQueryParam();
        Wrapper<SytCodeDqztb> wrapper = buildWrapper(obj);
        return page(page, wrapper);
    }

    @Override
    public List<SytCodeDqztb> queryList(SytCodeDqztb SytCodeDqztb) {
        return list(buildWrapper(SytCodeDqztb));
    }

    private Wrapper<SytCodeDqztb> buildWrapper(SytCodeDqztb SytCodeDqztb) {
        QueryWrapper<SytCodeDqztb> wrapper = new QueryWrapper<>();
        if (SytCodeDqztb.getName() != null) {
            wrapper.like("name", SytCodeDqztb.getName());
        }
        if (SytCodeDqztb.getCode() != null) {
            wrapper.in("code", SytCodeDqztb.getCode().split(","));
        }
        wrapper.orderByAsc("code");
        return wrapper;
    }
}
