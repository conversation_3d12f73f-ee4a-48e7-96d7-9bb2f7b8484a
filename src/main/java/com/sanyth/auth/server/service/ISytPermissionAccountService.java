package com.sanyth.auth.server.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.CurrentUser;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.model.SytPermissionAccount;

import java.util.List;
import java.util.Map;

/**
 * 用户信息表 服务接口
 * Created by JIANGPING on 2020-05-14.
 */
public interface ISytPermissionAccountService extends IService<SytPermissionAccount> {

    SytPermissionAccount getByHumancode(String humancode);

    List<SytPermissionAccount> queryList(SytPermissionAccount account);
    List<SytPermissionAccount> queryListAndRole(BaseQuery<Map<String, Object>> query);

    Page<SytPermissionAccount> queryPage(BaseQuery<Map<String, Object>> query);

    void edit(JSONObject params) throws Exception;

    void delete(String... id) throws Exception;

    Resp updateInfo(SytPermissionAccount account);

    Resp changePasswd(String oldpassword, String newpassword, String newpasswords, CurrentUser currentUser);

    Resp resetPassword(Map<String, String> param);
    Resp resetPasswordByAdmin(JSONObject param);

    Resp getAccountAuditData(JSONObject param);

    Resp verifyChangePhoneCode(Map<String, String> param, CurrentUser currentUser);

    Resp updatePhone(Map<String, String> param,CurrentUser currentUser);

    /**
     * 更新人员排序
     */
    Resp updateDisplayorder(Map<String, Object> param);
}
