package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.mapper.SytSysOrganizationMapper;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.service.ISytSysOrganizationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 组织机构表 服务实现
 * Created by JIANGPING on 2020-05-14.
 */
@Service("sytSysOrganizationService")
public class SytSysOrganizationServiceImpl extends ServiceImpl<SytSysOrganizationMapper, SytSysOrganization> implements ISytSysOrganizationService {

    @Resource
    SytSysOrganizationMapper sytSysOrganizationMapper;

    @Override
    public List<SytSysOrganization> getOrganizationByUser(String userId) {
        return sytSysOrganizationMapper.getOrganizationByUser(userId);
    }

    @Override
    public List<SytSysOrganization> queryList(SytSysOrganization organization) {
        return list(buildWapper(organization));
    }

    @Override
    public List<SytSysOrganization> queryParentList() {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        wrapper.isNull("parent").or().eq("parent", "");
        return list(wrapper);
    }

    @Override
    public Page<SytSysOrganization> queryPage(BaseQuery<SytSysOrganization> query) {
        Page<SytSysOrganization> page = new Page<>(query.getPage(), query.getPageSize());
        return page(page, buildWapper(query.getQueryParam()));
    }

    private QueryWrapper buildWapper(SytSysOrganization organization){
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(organization.getCode()))
            wrapper.eq("code", organization.getCode());
        if(StringUtils.isNotBlank(organization.getValid()))
            wrapper.eq("valid", organization.getValid());
        if(StringUtils.isNotBlank(organization.getCategoryId()))
            wrapper.eq("categoryId", organization.getCategoryId());
        if(StringUtils.isNotBlank(organization.getParent()))
            wrapper.eq("parent", organization.getParent());
        if(StringUtils.isNotBlank(organization.getOrgname()))
            wrapper.like("orgname", organization.getOrgname());
        if(StringUtils.isNotBlank(organization.getLabel()))
            wrapper.like("orgname", organization.getLabel());
        wrapper.orderByAsc("displayorder");
        return wrapper;
    }
}
