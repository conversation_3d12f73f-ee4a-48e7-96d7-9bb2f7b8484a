package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.mapper.SysAuthLogsMapper;
import com.sanyth.auth.server.model.SysAuthLogs;
import com.sanyth.auth.server.service.SysAuthLogsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 认证日志
 */
@Service
public class SysAuthLogsServiceImpl extends ServiceImpl<SysAuthLogsMapper, SysAuthLogs> implements SysAuthLogsService {

    @Override
    public Page<SysAuthLogs> queryPage(Page<SysAuthLogs> page, SysAuthLogs authLogs) {
        QueryWrapper wrapper = buildWrapper(authLogs);
        wrapper.orderByDesc("login_time");
        return page(page, wrapper);
    }

    private QueryWrapper buildWrapper(SysAuthLogs authLogs){
        QueryWrapper<SysAuthLogs> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(authLogs.getHumanCode())) {
            wrapper.eq("human_code", authLogs.getHumanCode());
        }
        return wrapper;
    }
}
