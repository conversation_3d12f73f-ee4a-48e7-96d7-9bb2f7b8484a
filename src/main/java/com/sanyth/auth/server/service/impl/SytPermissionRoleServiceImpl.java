package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.mapper.*;
import com.sanyth.auth.server.model.*;
import com.sanyth.auth.server.service.ISytPermissionRoleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.util.FieldUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-15.
 */
@Service
public class SytPermissionRoleServiceImpl extends ServiceImpl<SytPermissionRoleMapper, SytPermissionRole> implements ISytPermissionRoleService {

    @javax.annotation.Resource
    RoleResourceMapper resourceRoleMapper;
    @javax.annotation.Resource
    ResourceMapper resourceMapper;
    @javax.annotation.Resource
    ResourceLinkMapper resourceLinkMapper;
    @javax.annotation.Resource
    RoleResourceLinkMapper roleResourceLinkMapper;
    @javax.annotation.Resource
    SytPermissionRoleMapper sytPermissionRoleMapper;

    @Transactional
    @Override
    public void edit(SytPermissionRole param) {
        List<Resource> resources = resourceMapper.selectList(new QueryWrapper<>());
        Map<String, String> resourceMap = getStringMap(resources);

        SytPermissionRole permissionRole = new SytPermissionRole();
        BeanUtils.copyProperties(param, permissionRole);
        saveOrUpdate(permissionRole);
        Set<String> resourceIds = param.getResourceIds();

        Set<String> temp = new HashSet<>();
        for (String resourceId : resourceIds) {
            String parentId = resourceMap.get(resourceId);
            if (!resourceIds.contains(parentId)) {
                temp.add(parentId);
            }
        }
        resourceIds.addAll(temp);
        QueryWrapper<RoleResource> wrapper = new QueryWrapper<>();
        wrapper.eq("ROLE_ID", permissionRole.getId());
        resourceRoleMapper.delete(wrapper);
        if (!CollectionUtils.isEmpty(resourceIds)) {
            resourceIds.forEach(resourceId -> {
                if (StringUtils.isNotEmpty(resourceId)) {
                    RoleResource resourceRole = new RoleResource();
                    resourceRole.setResourceId(resourceId);
                    resourceRole.setRoleId(permissionRole.getId());
                    resourceRoleMapper.insert(resourceRole);
                }
            });
        }

        // 处理后端接口权限逻辑
        List<ResourceLink> resourceLinks = resourceLinkMapper.selectList(new QueryWrapper<>());
        Map<String, String> resourceLinkMap = getStringMap(resourceLinks);
        Set<String> resourceLinkIds = param.getResourceLinkIds();
        temp = new HashSet<>();
        for (String resourceId : resourceLinkIds) {
            String parentId = resourceLinkMap.get(resourceId);
            if (!resourceLinkIds.contains(parentId)) {
                temp.add(parentId);
            }
        }
        resourceLinkIds.addAll(temp);
        QueryWrapper<RoleResourceLink> roleResourceWrapper = new QueryWrapper<>();
        roleResourceWrapper.eq("ROLE_ID", permissionRole.getId());
        roleResourceLinkMapper.delete(roleResourceWrapper);
        if (!CollectionUtils.isEmpty(resourceLinkIds)) {
            resourceLinkIds.forEach(resourceId -> {
                if (StringUtils.isNotEmpty(resourceId)) {
                    RoleResourceLink resourceRole = new RoleResourceLink();
                    resourceRole.setResourceId(resourceId);
                    resourceRole.setRoleId(permissionRole.getId());
                    roleResourceLinkMapper.insert(resourceRole);
                }
            });
        }
    }

    private Map<String, String> getStringMap(List list) {
        Map<String, String> map = new HashMap<>();
        try {
            if (!CollectionUtils.isEmpty(list)) {
                for (Object resource : list) {
                    Object id = FieldUtils.getFieldValue(resource, "id");
                    Object parentId = FieldUtils.getFieldValue(resource, "parentId");
                    if (parentId != null) {
                        map.put((String) id, (String) parentId);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    @Override
    public List<SytPermissionRole> listManage() {
        Wrapper<SytPermissionRole> wrapper = buildWrapper(new SytPermissionRole());
        List<SytPermissionRole> list = list(wrapper);
        List<SytPermissionRole> newlist = new LinkedList<>();
        if (!CollectionUtils.isEmpty(list)) {
            List<Resource> resources = resourceMapper.selectList(new QueryWrapper<>());

            list.forEach(role -> {
                SytPermissionRole rolePermissonResult = new SytPermissionRole();
                BeanUtils.copyProperties(role, rolePermissonResult);
                QueryWrapper<RoleResource> resourceRoleWrapper = new QueryWrapper<>();
                resourceRoleWrapper.eq("ROLE_ID", role.getId());
                List<RoleResource> resourceRoles = resourceRoleMapper.selectList(resourceRoleWrapper);
                Set<String> sets = new HashSet<>();
                if (!CollectionUtils.isEmpty(resourceRoles)) {
                    resourceRoles.forEach(rr -> {
                        if (StringUtils.isNotEmpty(rr.getResourceId())) {
                            sets.add(rr.getResourceId());
                        }
                    });
                }

                // 找出父节点id
                Set<String> parents = new HashSet<>();
                sets.forEach(id -> {
                    resources.forEach(r -> {
                        boolean flag1 = id.equals(r.getId()) && StringUtils.isEmpty(r.getParentId());
                        boolean flag2 = id.equals(r.getParentId());
                        if (flag1 || flag2) {
                            parents.add(id);
                        }
                    });
                });
                // 剔除父节点
                sets.removeAll(parents);
                rolePermissonResult.setResourceIds(sets);


                Map<String, Object> params = new HashMap<>();
                params.put("roleId", role.getId());
                List<ResourceLink> resourceLinks = resourceLinkMapper.queryList(params);
                Set<String> resourceLinkIds = new HashSet<>();
                if (!CollectionUtils.isEmpty(resourceLinks)) {
                    for (ResourceLink resourceLink : resourceLinks) {
                        if (resourceLink.getParentId() != null) {
//                        if (CollectionUtils.isEmpty(resourceLink.getChildren())) { //resourceLink.getChildren() is null
                            resourceLinkIds.add(resourceLink.getId());
                        }
                    }
                }
                rolePermissonResult.setResourceLinkIds(resourceLinkIds);
                newlist.add(rolePermissonResult);
            });
        }
        return newlist;
    }

    @Override
    public Page<SytPermissionRole> queryPage(BaseQuery<SytPermissionRole> query) {
        Page<SytPermissionRole> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytPermissionRole> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public List<SytPermissionRole> getByAccount(String accountId) {
        return sytPermissionRoleMapper.getByAccount(accountId);
    }

    private Wrapper<SytPermissionRole> buildWrapper(SytPermissionRole sytPermissionRole) {
        QueryWrapper<SytPermissionRole> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(sytPermissionRole.getRolename())) {
            wrapper.like("rolename", sytPermissionRole.getRolename());
        }
        wrapper.orderByAsc("displayorder");
        return wrapper;
    }
}
