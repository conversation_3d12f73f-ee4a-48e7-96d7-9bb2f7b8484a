package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.OauthClientDetails;

/**
 *  服务接口
 * Created by JIANGPING on 2020-04-07.
 */
public interface OauthClientDetailsService extends IService<OauthClientDetails> {
    Page<OauthClientDetails> queryPage(BaseQuery<OauthClientDetails> query);
}
