package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.dto.StatisticsDto;
import com.sanyth.auth.server.mapper.IndexDataMapper;
import com.sanyth.auth.server.service.IndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class IndexServiceImpl extends ServiceImpl<IndexDataMapper, StatisticsDto> implements IndexService {

    @Autowired
    IndexDataMapper indexDataMapper;

    @Override
    public Map<String, Object> queryIndexData() {
        HashMap<String, Object> map = new HashMap<>();
        HashMap<String, Object> infoMap = new HashMap<>();
        infoMap.putAll(indexDataMapper.queryAccountNumber());
        List<Map<String, Object>> maps = indexDataMapper.queryNumberByField("VALIDFLAG");
        BigDecimal decimal = new BigDecimal(0);
        for (Map<String, Object> longMap : maps) {
            Object nameObj = longMap.get("NAME");
            Object valueObj = longMap.get("VALUE");

            BigDecimal nameValue = null;
            if (nameObj != null) {
                if (nameObj instanceof BigDecimal) {
                    nameValue = (BigDecimal) nameObj;
                } else if (nameObj instanceof Number) {
                    nameValue = new BigDecimal(nameObj.toString());
                } else {
                    try {
                        nameValue = new BigDecimal(nameObj.toString());
                    } catch (NumberFormatException e) {
                        continue;
                    }
                }
            }

            BigDecimal valueDecimal = decimal; // 默认值为0
            if (valueObj != null) {
                if (valueObj instanceof BigDecimal) {
                    valueDecimal = (BigDecimal) valueObj;
                } else if (valueObj instanceof Number) {
                    valueDecimal = new BigDecimal(valueObj.toString());
                } else {
                    try {
                        valueDecimal = new BigDecimal(valueObj.toString());
                    } catch (NumberFormatException e) {
                        valueDecimal = decimal;
                    }
                }
            }

            if (nameValue != null) {
                if (nameValue.compareTo(decimal) == 0) {
                    infoMap.put("ZXRS", valueDecimal); // 注销人数
                } else if (nameValue.compareTo(new BigDecimal(1)) == 0) {
                    infoMap.put("LXRS", valueDecimal); // 离线人数
                }
            }
        }
        infoMap.putAll(indexDataMapper.queryVisitNumber());
        infoMap.putAll(indexDataMapper.queryAppNumber());
        infoMap.putAll(indexDataMapper.queryOrgNumber());
        map.put("sjgl", infoMap);
        map.put("yfw", indexDataMapper.queryVisitNumberByDay());
        map.put("rylb", indexDataMapper.queryNumberByField("EMPLOYEETYPE"));
        return map;
    }
}
