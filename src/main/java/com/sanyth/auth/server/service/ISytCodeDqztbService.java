package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.SytCodeDqztb;

import java.util.List;

/**
 * ${table.comment} 服务接口
 */
public interface ISytCodeDqztbService extends IService<SytCodeDqztb> {
    Page<SytCodeDqztb> queryPage(BaseQuery<SytCodeDqztb> query);

    List<SytCodeDqztb> queryList(SytCodeDqztb sytCodeDqztb);
	
}
