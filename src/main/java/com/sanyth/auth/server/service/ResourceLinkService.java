package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.ResourceLink;

import java.util.List;

public interface ResourceLinkService extends IService<ResourceLink> {

    Page<ResourceLink> queryPage(BaseQuery<ResourceLink> query);

    List<ResourceLink> getParentList();
}
