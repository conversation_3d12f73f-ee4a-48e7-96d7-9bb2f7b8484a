package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.SytSysOrganizationUser;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-15.
 */
public interface ISytSysOrganizationUserService extends IService<SytSysOrganizationUser> {
        Page<SytSysOrganizationUser> queryPage(BaseQuery<SytSysOrganizationUser> query);
}
