package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.model.SytLogRecordPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

@Service
public class SytLogRecordService {

    @Resource
    MongoTemplate mongoTemplate;

    public void save(SytLogRecordPO logRecordPO) {
        logRecordPO.setId(UUID.randomUUID().toString().replaceAll("-",""));
        mongoTemplate.save(logRecordPO);
    }

    public SytLogRecordPO get(String idOrNameOrType) {
        Criteria criteria = new Criteria();
        criteria.orOperator(
                        Criteria.where("id").is(idOrNameOrType),
                        Criteria.where("name").is(idOrNameOrType));
        return mongoTemplate.findOne(new Query().addCriteria(criteria), SytLogRecordPO.class);
    }


    public void delete(String... id) {
        if (id.length > 0) {
            List<String> list = Arrays.asList(id);
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(list));
            mongoTemplate.remove(query, SytLogRecordPO.class);
        }
    }
    public List<SytLogRecordPO> queryList(BaseQuery<SytLogRecordPO> query){
        Query q = getQuery(query.getQueryParam());
        return mongoTemplate.find(q, SytLogRecordPO.class);
    }

    public Page<SytLogRecordPO> queryPage(BaseQuery<SytLogRecordPO> query) {
        Query q = getQuery(query.getQueryParam());
        long count = mongoTemplate.count(q, SytLogRecordPO.class);
        Pageable pageable = PageRequest.of(query.getPage() - 1,
                query.getPageSize(),
                Sort.by(Sort.Direction.DESC, "createTime"));
        List<SytLogRecordPO> list = mongoTemplate.find(q.with(pageable), SytLogRecordPO.class);
        Page<SytLogRecordPO> objectPage = new Page<>();
        objectPage.setRecords(list);
        objectPage.setTotal(count);
        objectPage.setCurrent(query.getPage());
        objectPage.setSize(query.getPageSize());
        return objectPage;
    }


    private Query getQuery(SytLogRecordPO param) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(param.getId()))
            criteria.and("id").in(param.getId().split(","));
        if (StringUtils.isNotBlank(param.getType()))
            criteria.and("type").in(param.getType().split(","));
        if (StringUtils.isNotBlank(param.getSubType()))
            criteria.and("subType").in(param.getSubType().split(","));
        if (StringUtils.isNotBlank(param.getTenant()))
            criteria.and("tenant").is(param.getTenant());
        if (StringUtils.isNotBlank(param.getBizNo()))
            criteria.and("bizNo").is(param.getBizNo());
        if (StringUtils.isNotBlank(param.getOperator()))
            criteria.and("operator").is(param.getOperator());
        if (StringUtils.isNotBlank(param.getAction())) {
            Pattern pattern = Pattern.compile("^.*" + param.getAction() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("action").regex(pattern);
        }
        query.addCriteria(criteria);
        return query;
    }
}
