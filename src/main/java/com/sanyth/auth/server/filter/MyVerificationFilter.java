package com.sanyth.auth.server.filter;

import com.sanyth.auth.server.core.common.Constants;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.LoginFailUtils;
import com.sanyth.auth.server.core.common.SpringTool;
import com.sanyth.auth.server.core.exception.ValidateException;
import com.sanyth.auth.server.core.rsa.SecJS;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.service.SytSysParamService;
import com.sanyth.auth.server.service.SytSysSafetyService;
import com.wf.captcha.utils.CaptchaUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by JIANGPING on 2020/2/26.
 */
public class MyVerificationFilter extends OncePerRequestFilter {
    private Logger log = LoggerFactory.getLogger(MyVerificationFilter.class);

    public MyVerificationFilter(AuthenticationFailureHandler authenticationFailureHandler) {
        this.authenticationFailureHandler = authenticationFailureHandler;
    }

    private AuthenticationFailureHandler authenticationFailureHandler;
    @Autowired
    LoginFailUtils loginFailUtils;
    @Autowired
    SytSysSafetyService sytSysSafetyService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        // 排除例如magic-api等的登录地址
        List<String> notCheckLoginUriList = new ArrayList<>();
        notCheckLoginUriList.add("/magic/web/login");
        if (uri.endsWith("/login") && !notCheckLoginUriList.contains(uri)) {
            try {
                SytSysParamService paramService = (SytSysParamService) SpringTool.getApplicationContext().getBean("sytSysParamService");
                SytSysParam param = paramService.get("loginCaptcha");
                if ((param == null || Constants.HAS_YES.equals(param.getValue())) && loginFailUtils.isValidCaptcha(request, response)) {
                    validate(request);
                }
                SecJS.checkDynamicKeyAndGet(request.getParameter("username"), request);
                SecJS.checkDynamicKeyAndGet(request.getParameter("password"), request);
            } catch (AuthenticationException e) {
                authenticationFailureHandler.onAuthenticationFailure(request, response, e);
                return;
            }
        } else if (uri.endsWith("/sec_js")) {
            String s = SecJS.newDynamicKey(request);
            out(response, 200, s, "javascript");
            return;
        }

        // 查询密码安全策略：登录超时限制(分钟) overtime
        Map<String, Object> strategySafetyeMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_SAFETY);
        if(strategySafetyeMap != null && strategySafetyeMap.get("overtime") != null) {
            HttpSession session = request.getSession();
            int overtime = Integer.parseInt(strategySafetyeMap.get("overtime").toString());
            session.setMaxInactiveInterval(overtime * 60);
//            MySessionManager mySessionManager = new MySessionManager();
//            mySessionManager.updateSessionExpiration(request.getRequestedSessionId(),overtime * 60);
        }
        filterChain.doFilter(request, response);
    }

    private void out(HttpServletResponse response, int status,
                     String retString, String style) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setContentType("text/" + (StringUtils.isNotBlank(style) ? style : "json") + ";charset=UTF-8");
        response.setStatus(status);
        PrintWriter out = response.getWriter();
        out.print(retString);
        out.flush();
        out.close();
    }

    private void validate(HttpServletRequest request) throws AuthenticationException {
        String vercode = request.getParameter("vercode");
        vercode = SecJS.checkDynamicKeyAndGet(vercode, request);
        if (!CaptchaUtil.ver(vercode, request)) {
            throw new ValidateException(ErrorInfo.CODE_MSG_00002, ErrorInfo.CODE_00002);
        }
        CaptchaUtil.clear(request);
    }
}
