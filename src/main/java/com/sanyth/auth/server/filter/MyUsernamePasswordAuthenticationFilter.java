package com.sanyth.auth.server.filter;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.core.exception.ValidateException;
import com.sanyth.auth.server.core.rsa.SecJS;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.service.SytSysSafetyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

/**
 * Created by JIANGPING on 2020/4/1.
 */
public class MyUsernamePasswordAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    @Resource
    SytPermissionAccountMapper sytPermissionAccountMapper;
    @Autowired
    LoginFailUtils loginFailUtils;
    @Autowired
    SytSysSafetyService sytSysSafetyService;
    @Resource
    private RedisTemplate redisTemplate;

    private boolean postOnly = true;

    public MyUsernamePasswordAuthenticationFilter() {
        super();
    }

    @Override
    protected String obtainUsername(HttpServletRequest request) {
        String usernameText = request.getParameter(this.SPRING_SECURITY_FORM_USERNAME_KEY);
        return SecJS.decryptAndGet(usernameText);
    }

    @Override
    protected String obtainPassword(HttpServletRequest request) {
        String passwordText = request.getParameter(this.SPRING_SECURITY_FORM_PASSWORD_KEY);
        return SecJS.decryptAndGet(passwordText);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (this.postOnly && !request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        } else {
            String username = this.obtainUsername(request);
            String password = this.obtainPassword(request);
            if(StringUtils.isBlank(username)){
                throw new ValidateException("用户名或密码错误");
            }
            sytPermissionAccountMapper = (SytPermissionAccountMapper) SpringTool.getApplicationContext().getBean("sytPermissionAccountMapper");
            QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
            wrapper.eq("humancode", username).or().eq("email", username)
                    .or().eq("telmobile1", username).or().eq("idcode", username);
            SytPermissionAccount account = sytPermissionAccountMapper.selectOne(wrapper);
            if(account == null){
                throw new ValidateException("用户名或密码错误");
            }else{
                username = account.getHumancode();
                // 判断是否开启登录失败锁定账户限制
                checkLoginFailLock(request, username);
            }
            if (username == null) {
                username = "";
            }

            if (password == null) {
                password = "";
            }

            username = username.trim();
            UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);
            this.setDetails(request, authRequest);
            return this.getAuthenticationManager().authenticate(authRequest);
        }
    }

    /**
     * 判断是否开启登录失败锁定账户限制
     * @param request
     * @param username
     */
    private void checkLoginFailLock(HttpServletRequest request, String username) {
        Map<String, Object> strategySafetyeMap = sytSysSafetyService.getByCode(Constants.PASSWORD_STRATEGY_SAFETY);
        if(strategySafetyeMap != null && strategySafetyeMap.get("lockaccount") != null
                && Objects.equals("开启", strategySafetyeMap.get("lockaccount").toString())) {
            // 锁定类型
            Object locktypeObj = strategySafetyeMap.get("locktype");
            if(locktypeObj != null){
                String key = "LOCK_ACCOUNT_";
                // 锁定IP
                if(Objects.equals(locktypeObj.toString(), "ip")){
                    // 查询IP是否锁定
                    key += ToolsUtil.getIpAddr(request);
                }
                // 锁定账户
                if(Objects.equals(locktypeObj.toString(), "usercode")){
                    // 查询账户是否锁定
                    key += username;
                }
                // 查询是否锁定
                Object str = redisTemplate.opsForHash().get(key, "locktime");
                if(str != null){
                    throw new ValidateException(ErrorInfo.CODE_MSG_00007, ErrorInfo.CODE_00007);
                }
            }

        }
    }
}
