package com.sanyth.auth.server.filter;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CorsFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletResponse res = (HttpServletResponse) response;
        HttpServletRequest req = (HttpServletRequest) request;
//        String[] origins={"http://localhost:9529","http://localhost:8081","http://localhost:8082","http://localhost:8083"};
//        Set allowedOrigins= new HashSet(Arrays.asList(origins));
        String originHeader=req.getHeader("Origin");
//        if(allowedOrigins.contains(originHeader)){
//            res.setHeader("Access-Control-Allow-Origin", originHeader);
//        }
        res.setHeader("Access-Control-Allow-Origin", originHeader);
//        res.setHeader("Access-Control-Allow-Methods", "*");
//        res.setHeader("Access-Control-Allow-Headers", "*");
//        res.setHeader("Access-Control-Allow-Credentials","true");

//        if ("OPTIONS".equalsIgnoreCase(req.getMethod())) {
//            res.setStatus(HttpServletResponse.SC_OK);
//        } else {
//            chain.doFilter(request, response);
//        }
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {

    }
}
