package com.sanyth.auth.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.model.SytPermissionAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户信息表Mapper 接口
 * Created by JIANGPING on 2020-05-14.
 */
@Mapper
public interface SytPermissionAccountMapper extends BaseMapper<SytPermissionAccount> {

    List<SytPermissionAccount> queryList(Map<String,Object> map, Page<SytPermissionAccount> page);

    /**
     * 账号审计：查询各审计类型的人数
     * 参数zhsjlx取值：wdlrs 90天以上未登录人数，wxgmmrs 90天以上未修改密码人数，
     * zhgqrs 账号过期人数, wbdzzrs 账号未绑定组织人数， wfpjsrs 账号未分配角色人数
     * @param map 账号审计类型
     */
    Map<String, Long> queryNumberByZhsjlx(@Param("map") Map<String, String> map);
}