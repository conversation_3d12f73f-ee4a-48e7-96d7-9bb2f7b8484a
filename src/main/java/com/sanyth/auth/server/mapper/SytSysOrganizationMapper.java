package com.sanyth.auth.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanyth.auth.server.model.SytSysOrganization;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组织机构表Mapper 接口
 * Created by JIANGPING on 2020-05-14.
 */
@Mapper
public interface SytSysOrganizationMapper extends BaseMapper<SytSysOrganization> {

    List<SytSysOrganization> getOrganizationByUser(@Param("userId") String userId);

}