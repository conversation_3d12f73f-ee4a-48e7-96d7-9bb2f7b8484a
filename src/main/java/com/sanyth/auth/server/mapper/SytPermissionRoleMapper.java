package com.sanyth.auth.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanyth.auth.server.model.SytPermissionRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * ${table.comment}Mapper 接口
 * Created by JIANGPING on 2020-05-15.
 */
@Mapper
public interface SytPermissionRoleMapper extends BaseMapper<SytPermissionRole> {

    List<SytPermissionRole> getByAccount(String accountId);

    List<Map<String, String>> queryMapGroupByAccount(String accountId);
}