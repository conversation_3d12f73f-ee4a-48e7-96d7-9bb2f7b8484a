package com.sanyth.auth.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.model.SytJustAuthUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface SytJustAuthUserMapper extends BaseMapper<SytJustAuthUser> {

    List<SytJustAuthUser> queryList(Map<String, Object> params, Page<SytJustAuthUser> page);
}