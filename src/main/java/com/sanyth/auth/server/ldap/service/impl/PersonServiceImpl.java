package com.sanyth.auth.server.ldap.service.impl;

import com.sanyth.auth.server.ldap.entity.Person;
import com.sanyth.auth.server.ldap.repository.PersonRepository;
import com.sanyth.auth.server.ldap.service.PersonService;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.service.ISytPermissionAccountService;
import com.sanyth.auth.server.web.LoginLogsController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.ldap.repository.LdapRepository;
import org.springframework.ldap.core.DirContextOperations;
import org.springframework.ldap.core.LdapOperations;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.ldap.query.LdapQuery;
import org.springframework.ldap.query.LdapQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class PersonServiceImpl implements PersonService {

    private Logger log = LoggerFactory.getLogger(LoginLogsController.class);
    @Autowired
    ISytPermissionAccountService accountService;
    @Autowired
    private PersonRepository personRepository;
    @Autowired
    private LdapOperations ldapOperations;
    @Autowired
    private LdapTemplate ldapTemplate;

    @Value("${ldap.switch}")
    private boolean ldapSwitch;


    @Override
    public LdapRepository<Person> ldapRepository() {
        return personRepository;
    }

    @Override
    public boolean authenticate(String userName, String passWord) {
        LdapQuery ldapQuery = LdapQueryBuilder.query().base("ou=person")
                .filter(new EqualsFilter("uid", userName));
        try {
            ldapOperations.authenticate(ldapQuery, passWord);
            return true;
        } catch (Exception e) {
			log.error(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void saveBean(Person s) {
        if (ldapSwitch) {
            save(s);
        }
    }

    @Override
    public void updateBean(Person s) {
        if (ldapSwitch) {
            DirContextOperations dtx = ldapTemplate.lookupContext(s.getDn());
            dtx.setAttributeValue("uid", s.getUid());
            dtx.setAttributeValue("cn", s.getCn());
            dtx.setAttributeValue("sn", s.getSn());
            dtx.setAttributeValue("mail", s.getMail());
            dtx.setAttributeValue("telephoneNumber", s.getTelephoneNumber());
            dtx.setAttributeValue("employeeType", s.getEmployeeType());
            dtx.setAttributeValue("olcAllows", s.isOlcAllows() + "");
            dtx.setAttributeValue("olcTimeLimit", s.getOlcTimeLimit());
            dtx.setAttributeValue("ou", s.getOu());
            dtx.setAttributeValue("o", s.getOrganizationName());
            dtx.setAttributeValue("uniqueIdentifier", s.getIdcode());
            ldapTemplate.modifyAttributes(dtx);
        }
    }

    @Override
    public void changePassword(Person s) {
        if (ldapSwitch) {
            DirContextOperations dtx = ldapTemplate.lookupContext(s.getDn());
            dtx.setAttributeValue("uid", s.getUid());
            dtx.setAttributeValue("cn", s.getCn());
            dtx.setAttributeValue("sn", s.getSn());
            dtx.setAttributeValue("mail", s.getMail());
            dtx.setAttributeValue("telephoneNumber", s.getTelephoneNumber());
            dtx.setAttributeValue("employeeType", s.getEmployeeType());
            dtx.setAttributeValue("olcAllows", s.isOlcAllows() + "");
            dtx.setAttributeValue("olcTimeLimit", s.getOlcTimeLimit());
            dtx.setAttributeValue("ou", s.getOu());
            dtx.setAttributeValue("o", s.getOrganizationName());
            dtx.setAttributeValue("uniqueIdentifier", s.getIdcode());
            dtx.setAttributeValue("userPassword", s.getUserPassword());
            ldapTemplate.modifyAttributes(dtx);
        }
    }

    @Override
    public void deleteBean(Person s) {
        if (ldapSwitch) {
            delete(s);
        }
    }

    @Override
    public void deleteByIds(String... id) {
        if (ldapSwitch) {
            for (String s : id) {
                SytPermissionAccount account = accountService.getById(s);
                if (account != null) {
                    Person p = new Person(account.getHumancode());
                    delete(p);
                }
            }
        }
    }

    @Override
    public Person findById(String id) {
        if (ldapSwitch) {
            Optional<Person> optional = findOne(LdapQueryBuilder.query().base("ou=person").where("uid").is(id));
            if (optional.isPresent()) {
                return optional.get();
            }
        }
        return null;
    }

}
