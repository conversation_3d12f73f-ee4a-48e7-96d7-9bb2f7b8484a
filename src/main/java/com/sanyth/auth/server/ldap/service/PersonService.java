package com.sanyth.auth.server.ldap.service;


import com.sanyth.auth.server.ldap.common.LdapService;
import com.sanyth.auth.server.ldap.entity.Person;

public interface PersonService extends LdapService<Person>, Authentication {

    void saveBean(Person s);

    void updateBean(Person s);

    void changePassword(Person s);

    void deleteBean(Person s);

    void deleteByIds(String... id);

    Person findById(String id);

}
