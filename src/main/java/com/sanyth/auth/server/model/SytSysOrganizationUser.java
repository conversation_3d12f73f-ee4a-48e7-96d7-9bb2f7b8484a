package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * ${table.comment}实体类
 * Created by JIANGPING on 2020-05-15.
 */
@TableName("SYT_SYS_ORGANIZATION_USER")
public class SytSysOrganizationUser extends Model<SytSysOrganizationUser> {

    private static final long serialVersionUID = 1L;

	@TableField("organization_id")
	private String organizationId;
	@TableField("user_id")
	private String userId;


	public String getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	@Override
	public Serializable pkVal() {
		return null;
	}

}
