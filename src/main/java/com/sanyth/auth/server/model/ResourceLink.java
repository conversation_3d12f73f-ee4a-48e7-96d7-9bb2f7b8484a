package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.List;

@TableName("SYS_RESOURCE_LINK")
public class ResourceLink extends Model<ResourceLink> {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String name;
    private String pattern;
    @TableField("parent_id")
    private String parentId;
    private String type;

    @TableField(exist = false)
    private List<ResourceLink> children;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<ResourceLink> getChildren() {
        return children;
    }

    public void setChildren(List<ResourceLink> children) {
        this.children = children;
    }

    @Override
    public Serializable pkVal() {
        return id;
    }
}
