package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 认证日志
 */
@Getter
@Setter
@TableName("SYS_AUTH_LOGS")
public class SysAuthLogs extends Model<SysAuthLogs> {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("CLIENT_ID")
    private String clientId;

    @TableField("HUMAN_CODE")
    private String humanCode;

    @TableField("IP")
    private String ip;

    @TableField("LOGIN_TIME")
    private Date loginTime;

    @TableField("LOGOUT_TIME")
    private Date logoutTime;

    @TableField("USER_AGENT")
    private String userAgent;

    @TableField("ADDRESS")
    private String address;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
