package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * 系统资源
 * Created by JIANGPING on 2020/4/13.
 */
@TableName("sys_resource")
public class Resource extends Model<Resource> {

    @TableField("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 标题
     */
    private String label;
    /**
     * 是否按钮
     */
    @TableField("IS_BTN")
    private String isBtn;
    private String icon;
    private String param;
    private String path;
    @TableField("PARENT_ID")
    private String parentId;
    /**
     * 是否可用
     */
    @TableField("STATUS")
    private String status;
    @TableField("DESCRIPTION")
    private String description;
    @TableField("URL")
    private String url;
    @TableField("COMPONENT")
    private String component;
    /**
     * 类型0: 后端api, 1:前端地址
     */
    private Integer type;
    private Integer sort;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getIsBtn() {
        return isBtn;
    }

    public void setIsBtn(String isBtn) {
        this.isBtn = isBtn;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
