package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @description
 * @creatTime 2024/5/14 9:29 AM
 * //    {
 * //        "@class" : "org.apereo.cas.services.RegexRegisteredService",
 * //            "serviceId" : "^https://www.apereo.org",
 * //            "name" : "Apereo",
 * //            "theme" : "apereo",
 * //            "id" : 10000002,
 * //            "description" : "Apereo foundation sample service",
 * //            "evaluationOrder" : 1
 * //    }
 */
@TableName("REGEXREGISTEREDSERVICE")
public class CasClientDetails {
    @TableField("serviceId")
    private String serviceId;

    private String name;

    private String theme;
//    @TableField("CLIENTID")
//    private String clientId;

    @TableField("EXPRESSION_TYPE")
    private String expressionType="regex";

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getEvaluationOrder() {
        return evaluationOrder;
    }

    public void setEvaluationOrder(Integer evaluationOrder) {
        this.evaluationOrder = evaluationOrder;
    }

    private String description;

    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField("EVALUATION_ORDER")
    private Integer evaluationOrder=0;


//    public String getClientId() {
//        return clientId;
//    }
//
//    public void setClientId(String clientId) {
//        this.clientId = clientId;
//    }


    public String getExpressionType() {
        return expressionType;
    }

    public void setExpressionType(String expressionType) {
        this.expressionType = expressionType;
    }
}
