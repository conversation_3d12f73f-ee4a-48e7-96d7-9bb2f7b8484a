package com.sanyth.auth.server.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by JIANGPING on 2020/5/15.
 */
@Document(collection = "SytSysParam")
public class SytSysParam implements java.io.Serializable{

    private static final long serialVersionUID = -1468461967061031259L;
    @Id
    private String id;
    private String name;			//名称
    private String value;			//值
    private String type;			//类型
    private String status;			//状态
    private String sort;			//排序
    private String img;             //图片
    private String bz;				//说明
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getValue() {
        return value;
    }
    public void setValue(String value) {
        this.value = value;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    public String getBz() {
        return bz;
    }
    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }
}
