package com.sanyth.auth.server.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * 安全设置，密码策略
 * <AUTHOR>
 * @date 2022-11-19
 */
@Data
@Document(collection = "SytSysSafety")
public class SytSysSafety implements Serializable {

    private static final long serialVersionUID = 704857856463510743L;
    @Id
    private String id;
    /**
     * 编码(密码生成策略 PASSWORD_STRATEGY_GENERATE、密码安全策略 PASSWORD_STRATEGY_SAFETY、密码修改策略 PASSWORD_STRATEGY_MODIFY)
     */
    private String code;
    /**
     * 值
     */
    private String value;

}
