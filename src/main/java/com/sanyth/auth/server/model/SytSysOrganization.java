package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 组织机构表实体类
 * Created by JIANGPING on 2020-05-14.
 */
@TableName("SYT_SYS_ORGANIZATION")
public class SytSysOrganization extends Model<SytSysOrganization> {

    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
	@TableId(value = "ID",type = IdType.ASSIGN_UUID)
	private String id;
    /**
     * 机构编码
     */
	@TableField("CODE")
	private String code;
    /**
     * 机构名称
     */
	@TableField("ORGNAME")
	private String orgname;

	/**
	 * 机构简称
	 */
	@TableField("ORGSHORTNAME")
	private String orgshortname;
    /**
     * 是否有效(0:无效，1:有效)
     */
	@TableField("VALID")
	private String valid;
    /**
     * 显示顺序
     */
	@TableField("DISPLAYORDER")
	private Double displayorder;
    /**
     * 机构描述
     */
	@TableField("ORGDESCRIPTION")
	private String orgdescription;
    /**
     * 机构类型
     */
	@TableField("CATEGORY_ID")
	private String categoryId;
    /**
     * 上级机构
     */
	@TableField("PARENT")
	private String parent;
    /**
     * 所有上级机构
     */
	@TableField("PARENTS")
	private String parents;
	@TableField("ORGCODE")
	private String orgcode;

	@TableField(exist = false)
	private List<SytSysOrganization> children;
	@TableField(exist = false)
	private String label;
	@TableField(exist = false)
	private boolean hasChildren;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getOrgname() {
		return orgname;
	}

	public void setOrgname(String orgname) {
		this.orgname = orgname;
	}

	public String getValid() {
		return valid;
	}

	public void setValid(String valid) {
		this.valid = valid;
	}

	public Double getDisplayorder() {
		return displayorder;
	}

	public void setDisplayorder(Double displayorder) {
		this.displayorder = displayorder;
	}

	public String getOrgdescription() {
		return orgdescription;
	}

	public void setOrgdescription(String orgdescription) {
		this.orgdescription = orgdescription;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getParent() {
		return parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}

	public String getParents() {
		return parents;
	}

	public void setParents(String parents) {
		this.parents = parents;
	}

	public String getOrgcode() {
		return orgcode;
	}

	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}

	public List<SytSysOrganization> getChildren() {
		return children;
	}

	public void setChildren(List<SytSysOrganization> children) {
		this.children = children;
	}

	public String getLabel() {
		return StringUtils.isNotEmpty(this.label) ? this.label : getOrgname();
	}

	public void setLabel(String label) {
		this.label = label;
	}

	@Override
	public Serializable pkVal() {
		return this.id;
	}

	public String getOrgshortname() {
		return orgshortname;
	}

	public void setOrgshortname(String orgshortname) {
		this.orgshortname = orgshortname;
	}

	public boolean isHasChildren() {
		return hasChildren;
	}

	public void setHasChildren(boolean hasChildren) {
		this.hasChildren = hasChildren;
	}
}
