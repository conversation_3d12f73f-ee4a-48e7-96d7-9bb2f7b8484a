package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * 第三方平台参数配置
 */
@TableName("syt_just_auth_config")
public class SytJustAuthConfig extends Model<SytJustAuthConfig> {

    @TableId(value = "ID",type = IdType.ASSIGN_UUID)
    private String id;
    @TableField("AUTHTYPE")
    private String authType;                // 授权类型
    @TableField("CLIENTID")
    private String clientId;                // 客户端id：对应各平台的appKey
    @TableField("CLIENTSECRET")
    private String clientSecret;            // 客户端Secret：对应各平台的appSecret
    /**
     * 登录成功后的回调地址
     */
    @TableField("REDIRECTURI")
    private String redirectUri;
    /**
     * 支付宝公钥：当选择支付宝登录时，该值可用
     * 对应“RSA2(SHA256)密钥”中的“支付宝公钥”
     */
    @TableField("ALIPAYPUBLICKEY")
    private String alipayPublicKey;
    /**
     * 企业微信，授权方的网页应用ID
     */
    @TableField("AGENTID")
    private String agentId;

    @TableField("BZ")
    private String bz;          // 说明

    @TableField("CREATEDATE")
    private Date createDate;
    @TableField("MODIFYDATE")
    private Date modifyDate;

    @Override
    public Serializable pkVal() {
        return null;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getAlipayPublicKey() {
        return alipayPublicKey;
    }

    public void setAlipayPublicKey(String alipayPublicKey) {
        this.alipayPublicKey = alipayPublicKey;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }
}
