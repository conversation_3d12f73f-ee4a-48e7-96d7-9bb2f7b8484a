package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * ${table.comment}实体类
 * Created by JIANGPING on 2020-05-14.
 */
@TableName("SYT_PERMISSION_ACCOUNT_ROLE")
public class SytPermissionAccountRole extends Model<SytPermissionAccountRole> {

    private static final long serialVersionUID = 1L;

	@TableId(value = "ID",type = IdType.ASSIGN_UUID)
	private String id;
	@TableField("ROLE_ID")
	private String roleId;
	@TableField("ACCOUNT_ID")
	private String accountId;


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	@Override
	public Serializable pkVal() {
		return this.id;
	}

}
