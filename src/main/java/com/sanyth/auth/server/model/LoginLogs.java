package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Date;

@TableName("sys_login_logs")
public class LoginLogs extends Model<LoginLogs> {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    @TableField("token_id")
    private String tokenId;
    @TableField("client_id")
    private String clientId;
    @TableField("human_code")
    private String humanCode;
    private String ip;
    @TableField("login_time")
    private Date loginTime;
    @TableField("logout_time")
    private Date logoutTime;
    @TableField("user_agent")
    private String userAgent;
    private String address;

    @TableField(exist = false)
    private String browser;
    @TableField(exist = false)
    private String operatingSystem;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTokenId() {
        return tokenId;
    }

    public void setTokenId(String tokenId) {
        this.tokenId = tokenId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getHumanCode() {
        return humanCode;
    }

    public void setHumanCode(String humanCode) {
        this.humanCode = humanCode;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    public Date getLogoutTime() {
        return logoutTime;
    }

    public void setLogoutTime(Date logoutTime) {
        this.logoutTime = logoutTime;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBrowser() {
        if (!StringUtils.isEmpty(userAgent)) {
            UserAgent userAgent = UserAgent.parseUserAgentString(getUserAgent());
            Browser browser = userAgent.getBrowser();
            return browser.getName();
        }
        return browser;
    }

    public String getOperatingSystem() {
        if (!StringUtils.isEmpty(userAgent)) {
            UserAgent userAgent = UserAgent.parseUserAgentString(getUserAgent());
            OperatingSystem operatingSystem = userAgent.getOperatingSystem();
            return operatingSystem.getName();
        }
        return operatingSystem;
    }

    public static void main(String[] args) {
        String ss = "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36";
        UserAgent userAgent = UserAgent.parseUserAgentString(ss);
        Browser browser = userAgent.getBrowser();
        System.out.println(browser.getName());
        System.out.println(browser.getBrowserType());
    }
    @Override
    public Serializable pkVal() {
        return id;
    }
}
