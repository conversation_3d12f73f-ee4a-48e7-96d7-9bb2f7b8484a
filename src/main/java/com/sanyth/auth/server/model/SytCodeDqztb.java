package com.sanyth.auth.server.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * 当前状态表
 */
@Data
@TableName("SYT_CODE_DQZTB")
public class SytCodeDqztb extends Model<SytCodeDqztb> {

    private static final long serialVersionUID = 1L;

	@TableId(value = "ID",type = IdType.ASSIGN_UUID)
	private String id;
	@TableField("BZ")
	private String bz;
	@TableField("CODE")
	private String code;
	@TableField("NAME")
	private String name;

}
