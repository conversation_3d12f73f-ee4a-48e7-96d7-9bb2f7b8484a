package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * ${table.comment}实体类
 * Created by JIANGPING on 2020-05-15.
 */
@Data
@JsonIgnoreProperties({"authority"})
@TableName("SYT_PERMISSION_ROLE")
public class SytPermissionRole extends Model<SytPermissionRole> implements GrantedAuthority {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId(value = "ID",type = IdType.ASSIGN_UUID)
	private String id;
    /**
     * 角色名称
     */
	@TableField("ROLENAME")
	private String rolename;
    /**
     * 角色唯一标识
     */
	@TableField("ROLEKEY")
	private String rolekey;
	/**
	 * 角色类型
	 */
	@TableField("ROLETYPE")
	private String roletype;
	/**
	 * 显示顺序
	 */
	@TableField("DISPLAYORDER")
	private Double displayorder;
    /**
     * 角色备注
     */
	@TableField("ROLEINFO")
	private String roleinfo;
    /**
     * 创建者ID
     */
	@TableField("MASTERID")
	private String masterid;
    /**
     * 权限串
     */
	@TableField("OPDATE")
	private Date opdate;
    /**
     * 更新时间
     */
	@TableField("AUTHSTRING")
	private String authstring;
    /**
     * 手机图标角色控制
     */
	@TableField("MOBILEAUTH")
	private String mobileauth;

	/**临时字段**/
	@TableField(exist = false)
	private Set<String> resourceIds;
	@TableField(exist = false)
	private Set<String> resourceLinkIds;

	@Override
	public Serializable pkVal() {
		return this.id;
	}

	@Override
	public String getAuthority() {
		return rolekey;
	}
}
