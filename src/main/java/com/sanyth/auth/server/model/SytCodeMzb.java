package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * ${table.comment}实体类
 * Created by JIANGPING on 2020-05-15.
 */
@TableName("SYT_CODE_MZB")
public class SytCodeMzb extends Model<SytCodeMzb> {

    private static final long serialVersionUID = 1L;

	@TableId(value = "ID",type = IdType.ASSIGN_UUID)
	private String id;
	@TableField("BZ")
	private String bz;
	@TableField("CODE")
	private String code;
	@TableField("NAME")
	private String name;


	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBz() {
		return bz;
	}

	public void setBz(String bz) {
		this.bz = bz;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public Serializable pkVal() {
		return this.id;
	}

}
