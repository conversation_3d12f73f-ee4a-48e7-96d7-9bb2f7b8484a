package com.sanyth.auth.server.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Date;

/**
 * 第三方平台账户、系统账号对照表
 */
@JsonIgnoreProperties({"token"})
@TableName("syt_just_auth_user")
public class SytJustAuthUser extends Model<SytJustAuthUser> {

    @TableId(value = "ID",type = IdType.ASSIGN_UUID)
    private String id;
    @TableField("USERID")
    private String userId;          // 第三方平台对应的用户id
    @TableField("USERNAME")
    private String userName;        // 第三方平台对应的用户姓名
    @TableField("TOKEN")
    private String token;
    @TableField("EMAIL")
    private String email;           // 邮箱
    @TableField("MOBILE")
    private String mobile;          // 手机
    @TableField("AUTHTYPE")
    private String authType;        // 授权类型
    @TableField("HUMANCODE")
    private String humancode;       // 对应的系统账号
    @TableField("SFBIND")
    private String sfBind;          // 是否绑定账号
    @TableField("AVATAR")
    private String avatar;          // 第三方账号头像

    @TableField("CREATEDATE")
    private Date createDate;
    @TableField("MODIFYDATE")
    private Date modifyDate;

    @Override
    public Serializable pkVal() {
        return null;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getHumancode() {
        return humancode;
    }

    public void setHumancode(String humancode) {
        this.humancode = humancode;
    }

    public String getSfBind() {
        return sfBind;
    }

    public void setSfBind(String sfBind) {
        this.sfBind = sfBind;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }
}
