package com.sanyth.auth.server.model;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.mzt.logapi.beans.CodeVariableType;
import com.mzt.logapi.beans.LogRecord;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Document(collection = "SytLogRecordAuth")
public class SytLogRecordPO implements java.io.Serializable{
    /**
     * id
     */
    @Id
    private String id;
    /**
     * 租户
     */
    private String tenant;

    /**
     * 保存的操作日志的类型，比如：订单类型、商品类型
     */
    private String type;
    /**
     * 日志的子类型，比如订单的C端日志，和订单的B端日志，type都是订单类型，但是子类型不一样
     */
    private String subType;

    /**
     * 日志绑定的业务标识
     */
    private String bizNo;
    /**
     * 操作人
     */
    private String operator;

    /**
     * 日志内容
     */
    private String action;
    /**
     * 记录是否是操作失败的日志
     */
    private boolean fail;
    /**
     * 日志的创建时间
     */
    private Date createTime;
    /**
     * 日志的额外信息
     */
    private String extra;

    private String codeVariable;

    public static SytLogRecordPO from(LogRecord logRecord) {
        SytLogRecordPO logRecordPO = new SytLogRecordPO();
        BeanUtils.copyProperties(logRecord, logRecordPO);
        logRecordPO.setCodeVariable(JSONUtil.toJsonStr(logRecord.getCodeVariable()));
        return logRecordPO;
    }

    public static List<LogRecord> from(List<SytLogRecordPO> logRecordPOS) {
        List<LogRecord> ret = Lists.newArrayListWithCapacity(logRecordPOS.size());
        for (SytLogRecordPO logRecordPO : logRecordPOS) {
            ret.add(toLogRecord(logRecordPO));
        }
        return ret;
    }

    private static LogRecord toLogRecord(SytLogRecordPO logRecordPO) {
        LogRecord logRecord = new LogRecord();
        BeanUtils.copyProperties(logRecordPO, logRecord);
        if (StringUtils.isNotBlank(logRecordPO.getCodeVariable())) {
            Map<CodeVariableType, Object> toBean = JSONUtil.toBean(logRecordPO.getCodeVariable(),
                    new TypeReference<Map<CodeVariableType, Object>>() {
                    }, true);
            logRecord.setCodeVariable(toBean);
        }
        return logRecord;
    }
}
