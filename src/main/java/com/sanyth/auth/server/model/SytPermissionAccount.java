package com.sanyth.auth.server.model;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 用户信息表实体类
 * Created by JIANGPING on 2020-05-14.
 */
@JsonIgnoreProperties({"roles","humanpassword"})
@TableName("SYT_PERMISSION_ACCOUNT")
public class SytPermissionAccount extends Model<SytPermissionAccount> implements UserDetails {

    private static final long serialVersionUID = 1L;

    /**
     * 人员标识（人员工号，可以输入，也可以是系统自动生成的UUID）
     */
    @TableField("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 人员代码，登录账号
     */
    @TableField("HUMANCODE")
    private String humancode;
    /**
     * 短信标识号
     */
    @TableField("HUMANSMSCODE")
    private String humansmscode;
    /**
     * 人员名称
     */
    @TableField("HUMANNAME")
    private String humanname;
    /**
     * 人员描述
     */
    @TableField("HUMANDESCRIPTION")
    private String humandescription;
    /**
     * 建立日期
     */
    @TableField("CREATEDATE")
    private Date createdate;
    /**
     * 有效起始时间
     */
    @TableField("VALIDFROMDATE")
    private Date validfromdate;
    /**
     * 有效终止时间
     */
    @TableField("VALIDTODATE")
    private Date validtodate;
    /**
     * 有效标志0.在校1.离校
     */
    @TableField("VALIDFLAG")
    private Double validflag;
    /**
     * 口令/登录密码
     */
    @TableField("HUMANPASSWORD")
    private String humanpassword;
    /**
     * 性别
     */
    @TableField("SEX")
    private String sex;
    /**
     * 生日
     */
    @TableField("BIRTHDAY")
    private Date birthday;
    /**
     * 办公室电话号码
     */
    @TableField("TELOFFICE")
    private String teloffice;
    /**
     * 家庭电话号码
     */
    @TableField("TELHOME")
    private String telhome;
    /**
     * 手机号码1
     */
    @TableField("TELMOBILE1")
    private String telmobile1;
    /**
     * 手机号码2
     */
    @TableField("TELMOBILE2")
    private String telmobile2;
    /**
     * 电子邮件
     */
    @TableField("EMAIL")
    private String email;
    /**
     * 地址
     */
    @TableField("ADDRESS")
    private String address;
    /**
     * 邮编
     */
    @TableField("POSTALCODE")
    private String postalcode;
    /**
     * 年龄-20140509-修改为排序标识
     */
    @TableField("AGE")
    private Integer age;
    /**
     * 单位标识，多个,号隔开
     */
    @TableField("ORGID")
    private String orgid;
    /**
     * 签名/个性签名
     */
    @TableField("SIGNATURE")
    private String signature;
    /**
     * 密码加密类型1:无加密 2:MD5 3:Base64
     */
    @TableField("ENCRYPTYPE")
    private String encryptype;
    /**
     * 证件值
     */
    @TableField("IDCODE")
    private String idcode;
    /**
     * 证件类型
     */
    @TableField("IDTYPE")
    private String idtype;
    /**
     * 最近登录时间
     */
    @TableField("LOGINTIME")
    private Date logintime;
    /**
     * 最近登录信息，如IP地址
     */
    @TableField("LOGININFO")
    private String logininfo;
    /**
     * 职务id
     */
    @TableField("DUTYID")
    private String dutyid;
    /**
     * 用户工号（多个工程共用一个数据库是做唯一标识）
     */
    @TableField("HUMANNUMBER")
    private String humannumber;
    /**
     * 显示顺序
     */
    @TableField("DISPLAYORDER")
    private Double displayorder;
    /**
     * 人员类别
     */
    @TableField("EMPLOYEETYPE")
    private String employeeType;

    /**
     * 用户所属机构名称
     */
    @TableField("ORGANIZATIONNAMES")
    private String organizationnames;
    /**
     * 用户所属机构简称
     */
    @TableField("ORGSHORTNAME")
    private String orgshortname;

    /**
     * 激活状态
     */
    @TableField("ACTIVEFLAG")
    private Long activeflag;


    @TableField(exist = false)
    private List<SytPermissionRole> roles;
    @TableField(exist = false)
    private JSONArray role;
    @TableField(exist = false)
    private JSONArray organization;
    @TableField(exist = false)
    private String roleNames;
    @TableField(exist = false)
    private boolean sfyx;					//是否有效
    /**
     * 最近一次密码修改时间
     */
    @TableField("MODIFYPASSTIME")
    private Date modifypasstime;

    /**
     * 用户当前状态(对应人员前状态码表，比如北信科：离职、退休、在职、返聘)
     */
    @TableField("CURRENTSTATE")
    private String currentState;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHumancode() {
        return humancode;
    }

    public void setHumancode(String humancode) {
        this.humancode = humancode;
    }

    public String getHumansmscode() {
        return humansmscode;
    }

    public void setHumansmscode(String humansmscode) {
        this.humansmscode = humansmscode;
    }

    public String getHumanname() {
        return humanname;
    }

    public void setHumanname(String humanname) {
        this.humanname = humanname;
    }

    public String getHumandescription() {
        return humandescription;
    }

    public void setHumandescription(String humandescription) {
        this.humandescription = humandescription;
    }

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    public Date getValidfromdate() {
        return validfromdate;
    }

    public void setValidfromdate(Date validfromdate) {
        this.validfromdate = validfromdate;
    }

    public Date getValidtodate() {
        return validtodate;
    }

    public void setValidtodate(Date validtodate) {
        this.validtodate = validtodate;
    }

    public Double getValidflag() {
        return validflag;
    }

    public void setValidflag(Double validflag) {
        this.validflag = validflag;
    }

    public String getHumanpassword() {
        return humanpassword;
    }

    public void setHumanpassword(String humanpassword) {
        this.humanpassword = humanpassword;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getTeloffice() {
        return teloffice;
    }

    public void setTeloffice(String teloffice) {
        this.teloffice = teloffice;
    }

    public String getTelhome() {
        return telhome;
    }

    public void setTelhome(String telhome) {
        this.telhome = telhome;
    }

    public String getTelmobile1() {
        return telmobile1;
    }

    public void setTelmobile1(String telmobile1) {
        this.telmobile1 = telmobile1;
    }

    public String getTelmobile2() {
        return telmobile2;
    }

    public void setTelmobile2(String telmobile2) {
        this.telmobile2 = telmobile2;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostalcode() {
        return postalcode;
    }

    public void setPostalcode(String postalcode) {
        this.postalcode = postalcode;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getEncryptype() {
        return encryptype;
    }

    public void setEncryptype(String encryptype) {
        this.encryptype = encryptype;
    }

    public String getIdcode() {
        return idcode;
    }

    public void setIdcode(String idcode) {
        this.idcode = idcode;
    }

    public String getIdtype() {
        return idtype;
    }

    public void setIdtype(String idtype) {
        this.idtype = idtype;
    }

    public Date getLogintime() {
        return logintime;
    }

    public void setLogintime(Date logintime) {
        this.logintime = logintime;
    }

    public String getLogininfo() {
        return logininfo;
    }

    public void setLogininfo(String logininfo) {
        this.logininfo = logininfo;
    }

    public String getDutyid() {
        return dutyid;
    }

    public void setDutyid(String dutyid) {
        this.dutyid = dutyid;
    }

    public String getHumannumber() {
        return humannumber;
    }

    public void setHumannumber(String humannumber) {
        this.humannumber = humannumber;
    }

    public Double getDisplayorder() {
        return displayorder;
    }

    public void setDisplayorder(Double displayorder) {
        this.displayorder = displayorder;
    }

    public String getOrganizationnames() {
        return organizationnames;
    }

    public void setOrganizationnames(String organizationnames) {
        this.organizationnames = organizationnames;
    }

    public Long getActiveflag() {
        return activeflag;
    }

    public void setActiveflag(Long activeflag) {
        this.activeflag = activeflag;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<SimpleGrantedAuthority> grantedAuthorities = null;
        if (!CollectionUtils.isEmpty(roles)) {
            grantedAuthorities = new ArrayList<>();
            for (SytPermissionRole role : roles) {
                grantedAuthorities.add(new SimpleGrantedAuthority(role.getRolekey()));
            }
        }
        return grantedAuthorities;
    }

    @Override
    public String getPassword() {
        return this.humanpassword;
    }

    @Override
    public String getUsername() {
        return this.humancode;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    public List<SytPermissionRole> getRoles() {
        return roles;
    }

    public void setRoles(List<SytPermissionRole> roles) {
        this.roles = roles;
    }

    public JSONArray getRole() {
        return role;
    }

    public void setRole(JSONArray role) {
        this.role = role;
    }

    public JSONArray getOrganization() {
        return organization;
    }

    public void setOrganization(JSONArray organization) {
        this.organization = organization;
    }

    public String getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(String roleNames) {
        this.roleNames = roleNames;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public boolean isSfyx() {
        Date date = new Date();
        if(this.getValidfromdate() == null || this.getValidtodate() == null)
            return true;
        //是否在当前时间段内
        return date.after(this.getValidfromdate()) && date.before(this.getValidtodate());
    }

    public void setSfyx(boolean sfyx) {
        this.sfyx = sfyx;
    }

    public String getOrgshortname() {
        return orgshortname;
    }

    public void setOrgshortname(String orgshortname) {
        this.orgshortname = orgshortname;
    }

    public Date getModifypasstime() {
        return modifypasstime;
    }

    public void setModifypasstime(Date modifypasstime) {
        this.modifypasstime = modifypasstime;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }
}
