package com.sanyth.auth.server.platform.weixin.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.mapper.SytPermissionAccountRoleMapper;
import com.sanyth.auth.server.mapper.SytPermissionRoleMapper;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.utils.PlatformRequestUtil;
import com.sanyth.auth.server.platform.weixin.dto.WxWebAuthCheckData;
import com.sanyth.auth.server.platform.weixin.enums.WxQrLoginStatus;
import com.sanyth.auth.server.platform.weixin.mapper.WxAppMapper;
import com.sanyth.auth.server.platform.weixin.mapper.WxUserMapper;
import com.sanyth.auth.server.platform.weixin.model.WxApp;
import com.sanyth.auth.server.platform.weixin.model.WxUser;
import com.sanyth.auth.server.platform.weixin.util.WxClient;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * 微信公众号 认证
 *
 * @since 2024/12/16 10:44
 */
@Controller
@RequestMapping("/noAuth/platform/wx/webAuth")
public class WxWebAuthController {

    protected final Log logger = LogFactory.getLog(getClass());
    public static final String ERROR_PAGE = "/noAuth/platform/wx/webAuth/errorPage";
    public static final String ControllerRoot = "/noAuth/platform/wx/webAuth";

    private final WxAppMapper appMapper;
    private final WxUserMapper userMapper;
    //    private SessionRepository<?> sessionRepository;
    private final SytPermissionAccountRoleMapper accountRoleMapper;
    private final SytPermissionAccountMapper accountMapper;
    private final SytPermissionRoleMapper roleMapper;
    private final RedisTemplate<Object, Object> redisTemplate;

    public WxWebAuthController(WxAppMapper appMapper, WxUserMapper userMapper, SytPermissionAccountRoleMapper accountRoleMapper, SytPermissionAccountMapper accountMapper, SytPermissionRoleMapper roleMapper, RedisTemplate<Object, Object> redisTemplate) {
        this.appMapper = appMapper;
        this.userMapper = userMapper;
        this.accountRoleMapper = accountRoleMapper;
        this.accountMapper = accountMapper;
        this.roleMapper = roleMapper;
        this.redisTemplate = redisTemplate;
    }

    @RequestMapping("errorPage")
    public String errorPage(ModelMap modelMap, String content) {
        String content1 = "未知异常";
        if (StringUtils.isNotEmpty(content)) {
            content1 = new String(Base64.decodeBase64(content));
        }
        modelMap.put("content", content1);
        return "/platform/wei_xin/web_auth/error";
    }

    @ExceptionHandler(RequestError.class)
    public String requestException(RequestError requestError) {
        return "redirect:" + ERROR_PAGE + "?content=" + Base64.encodeBase64URLSafeString(requestError.getContent().getBytes(StandardCharsets.UTF_8));
    }

    @ExceptionHandler(Exception.class)
    public String exception(Exception e) {
        logger.error("WxAuthController error", e);
        return "redirect:" + ERROR_PAGE + "?content=" + Base64.encodeBase64URLSafeString(e.getMessage().getBytes(StandardCharsets.UTF_8));
    }

    @RequestMapping("preIndex")
    public String preIndex(String id, HttpServletRequest request, String eck) throws UnsupportedEncodingException {
//        WxApp wxApp = appMapper.selectById(id);
//        RequestError.isTrue(StringUtils.isNotEmpty(wxApp.getH5Url()), "重定向URL为空");
        if (eck != null) {
            WxQrLoginStatus qrLoginStatus = (WxQrLoginStatus) redisTemplate.opsForValue().get(eck);
            RequestError.isTrue(qrLoginStatus != null, "二维码已过期");
            redisTemplate.opsForValue().set(eck, WxQrLoginStatus.scan, 1, TimeUnit.DAYS);
        }

        HttpSession session = request.getSession();
        String url_root = PlatformRequestUtil.getUrlRoot(request);
        String redirect_uri = url_root + ControllerRoot + "/oauth2";
        String redirect_uriEncode = URLEncoder.encode(redirect_uri, "UTF-8");

        String state = RandomStringUtils.randomAlphanumeric(16);
//        session.setAttribute(state, id);
        WxWebAuthCheckData.setToSession(session, id, null, eck);

        String redirect = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + id + "&redirect_uri=" + redirect_uriEncode + "&response_type=code&scope=snsapi_base&state=" + state + "#wechat_redirect";
        return "redirect:" + redirect;
    }

    @RequestMapping("oauth2")
    public String oauth2(String code, String state, HttpSession httpSession, ModelMap modelMap) {
        WxWebAuthCheckData authCheckData = WxWebAuthCheckData.getFromHttpSession(httpSession);
        String appId = authCheckData.getAppId();
        String openId = WxClient.getOpenId(code, appMapper.selectById(appId));
        String sytDevJump = System.getenv("SytDevJump");
        if (StringUtils.isNotEmpty(sytDevJump)) {
            System.out.printf("oauth2 appId-[%s], openId-[%s] \n", appId, openId);
        }

        WxWebAuthCheckData.setToSession(httpSession, appId, openId, authCheckData.getExChangeKey());
        return "redirect:/noAuth/platform/wx/webAuth/index";
    }

    @RequestMapping("index")
    public String index(HttpSession httpSession, ModelMap modelMap) {
        WxWebAuthCheckData authCheckData = WxWebAuthCheckData.getFromHttpSession(httpSession);

        QueryWrapper<WxUser> wrapper = new QueryWrapper<>();
        wrapper.eq("app_id", authCheckData.getAppId());
        wrapper.eq("open_id", authCheckData.getOpenId());
        WxUser wxUser = userMapper.selectOne(wrapper);
        if (wxUser != null) {
            modelMap.put("username", wxUser.getSystemUsername());
        }
        if (authCheckData.getExChangeKey() != null) {
            modelMap.put("confirmBtnOn", true);
        }
        modelMap.put("openId", authCheckData.getOpenId().substring(authCheckData.getOpenId().length() - 4));
        return "/platform/wei_xin/web_auth/index";
    }

//    @RequestMapping("bindPage")
//    public String bindPage(HttpSession httpSession, ModelMap modelMap) {
//        return "/platform/wei_xin/web_auth/bindPage";
//    }
//
//    @RequestMapping("bindResult")
//    public String bindResult(HttpSession httpSession, ModelMap modelMap) {
//        WxWebAuthCheckData authCheckData = WxWebAuthCheckData.getFromHttpSession(httpSession);
//        RequestError.isTrue(authCheckData != null, "会话已重置，请退出后重新进入");
//
//        SytPermissionAccount account = SecurityUtils.getUser();
//        WxUser user = new WxUser();
//        user.setAppId(authCheckData.getAppId());
//        user.setOpenId(authCheckData.getOpenId());
//        user.setSystemUsername(account.getUsername());
//        user.setCreated(new Date());
//        userMapper.insert(user);
//

    /// /        httpSession.setAttribute(WxSessionKey.JsTicketInfo.toString(), new WXJsApiTicketInfo(authCheckData.getAppId(), WXJsTicketType.GongZhongHao));
    /// /        modelMap.put("url", wxAppService.getById(authCheckData.getAppId()).getH5Url());
//        modelMap.put("url", ControllerRoot + "/index");
//        modelMap.put("msg_title", "绑定成功");
//        return "/platform/wei_xin/web_auth/authenticateResult";
//    }
    @RequestMapping("unbindResult")
    public String unbindResult(HttpSession httpSession, ModelMap modelMap) {
        WxWebAuthCheckData authCheckData = WxWebAuthCheckData.getFromHttpSession(httpSession);

//        WxUser userTransform = wxUserDao.findOneByAppAndOpenId(wxAppService.getById(authCheckData.getAppId()), authCheckData.getOpenId());
//        wxUserDao.delete(userTransform);
        QueryWrapper<WxUser> wrapper = new QueryWrapper<>();
        wrapper.eq("app_id", authCheckData.getAppId());
        wrapper.eq("open_id", authCheckData.getOpenId());
        userMapper.delete(wrapper);

        modelMap.put("url", ControllerRoot + "/index");
        return "/platform/wei_xin/web_auth/unbindResult";
    }

    @RequestMapping("test")
    public String test(HttpSession httpSession, ModelMap modelMap) {
        modelMap.put("username", "this is name");
        modelMap.put("openId", "this is openId");
        return "/platform/wei_xin/web_auth/index";
    }

    @RequestMapping("auth_confirmed")
    public String auth_confirmed(HttpSession httpSession, ModelMap modelMap) {
        WxWebAuthCheckData authCheckData = WxWebAuthCheckData.getFromHttpSession(httpSession);
        RequestError.isTrue(authCheckData != null, "会话已重置，请退出后重新进入");
        RequestError.isTrue(authCheckData.getExChangeKey() != null, "参数异常");

        WxQrLoginStatus qrLoginStatus = (WxQrLoginStatus) redisTemplate.opsForValue().get(authCheckData.getExChangeKey());
        RequestError.isTrue(qrLoginStatus == WxQrLoginStatus.scan, "操作异常");

        WxApp wxApp = appMapper.selectById(authCheckData.getAppId());
        String openId = authCheckData.getOpenId();
        QueryWrapper<WxUser> wrapper = new QueryWrapper<>();
        wrapper.eq("app_id", authCheckData.getAppId());
        wrapper.eq("open_id", openId);
        WxUser user = userMapper.selectOne(wrapper);
        RequestError.isTrue(user != null && user.getSystemUsername() != null, "未绑定系统账号");

        redisTemplate.opsForValue().set(authCheckData.getExChangeKey(), WxQrLoginStatus.confirm, 1, TimeUnit.DAYS);
        redisTemplate.opsForValue().set(authCheckData.getExChangeKey() + "-username", user.getSystemUsername(), 1, TimeUnit.DAYS);


//        WxApp wxApp = appMapper.selectById(authCheckData.getAppId());
//        String openId = authCheckData.getOpenId();
//        QueryWrapper<WxUser> wrapper = new QueryWrapper<>();
//        wrapper.eq("app_id", authCheckData.getAppId());
//        wrapper.eq("open_id", openId);
//        WxUser user = userMapper.selectOne(wrapper);
//        RequestError.isTrue(user != null, "未绑定系统账号");
//
//        QueryWrapper<SytPermissionAccount> accountWrapper = new QueryWrapper<>();
//        accountWrapper.eq("humancode", user.getSystemUsername());
//        SytPermissionAccount account = accountMapper.selectOne(accountWrapper);
//        RequestError.isTrue(account != null, "绑定的系统账号不存在");
//
//        QueryWrapper<SytPermissionAccountRole> accountRoleWrapper = new QueryWrapper<>();
//        accountRoleWrapper.eq("account_id", account.getId());
//        List<SytPermissionAccountRole> rList = accountRoleMapper.selectList(accountRoleWrapper);
//        RequestError.isTrue(!CollectionUtils.isEmpty(rList), "未绑定角色");
//
//        SytPermissionRole role = roleMapper.selectById(rList.get(0).getRoleId());
//
//        SecurityContext context = SecurityContextHolder.getContext();
//        Authentication authentication = context.getAuthentication();
//        account.setRoles(Collections.singletonList(role));
//        UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(
//                account,
//                authentication.getCredentials(),
//                Collections.singletonList(new SimpleGrantedAuthority(role.getRolekey())));
//        auth.setDetails(authentication.getDetails());
//        context.setAuthentication(auth);

//        RequestError.isTrue(StringUtils.isNotEmpty(wxApp.getH5Url()), "重定向URL为空");

//        httpSession.setAttribute(WxSessionKey.JsTicketInfo.toString(), new WXJsApiTicketInfo(authCheckData.getAppId(), WXJsTicketType.GongZhongHao));

//        modelMap.put("url", wxAppService.getById(authCheckData.getAppId()).getH5Url());
        modelMap.put("msg_title", "认证成功");
        return "/platform/wei_xin/web_auth/auth_confirmed";
    }
}
