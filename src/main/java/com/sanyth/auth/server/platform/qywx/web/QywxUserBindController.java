package com.sanyth.auth.server.platform.qywx.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.platform.commonDeal.ApiR;
import com.sanyth.auth.server.platform.commonDeal.ReBaseApiController;
import com.sanyth.auth.server.platform.qywx.dao.QywxAppDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxParamDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.enums.QywxParamKey;
import com.sanyth.auth.server.platform.qywx.model.QywxApp;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;
import com.sanyth.auth.server.platform.qywx.query.QywxUserQuery;
import com.sanyth.auth.server.platform.qywx.service.QywxUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @since 2022/3/17 16:20
 */
@Slf4j
@RestController
@RequestMapping( "/platform/qywx/user_bind")
public class QywxUserBindController extends ReBaseApiController {

    private final QywxUserDao xDao;
    private final QywxUserService userService;
    private final QywxParamDao paramDao;
    private final QywxAppDao appDao;

    public QywxUserBindController(QywxUserDao xDao, QywxUserService userService, QywxParamDao paramDao, QywxAppDao appDao) {
        this.xDao = xDao;
        this.userService = userService;
        this.paramDao = paramDao;
        this.appDao = appDao;
    }

    @GetMapping("list")
    public ApiR<Page<QywxUser>> list(QywxUserQuery query, Authentication authentication) {
        SytPermissionAccount account = (SytPermissionAccount) authentication.getPrincipal();
        QywxUser param = query.getParam();
        if (param == null) {
            param = new QywxUser();
            query.setParam(param);
        }
        param.setSysId(account.getHumancode());

        Page<QywxUser> page = xDao.queryPage(query);
        return ApiR.ok(page);
    }

    @PostMapping("save")
    public ApiR<String> save(@RequestBody QywxUser dto,Authentication authentication) {
        SytPermissionAccount account = (SytPermissionAccount) authentication.getPrincipal();
        dto.setSysId(account.getHumancode());
        userService.save(dto);
        return ApiR.ok();
    }

    @DeleteMapping("delete")
    public ApiR<String> delete(@RequestBody QywxUser dto) {
        xDao.removeById(dto.getId());
        return ApiR.ok();
    }

    @GetMapping("get")
    public ApiR<QywxUser> get(Long id) {
        QywxUser select = xDao.getById(id);
        return ApiR.ok(select);
    }

    @GetMapping("preBindInfo")
    public ApiR<String[]> preBindInfo(HttpServletRequest request) throws UnsupportedEncodingException {
        String cropId = paramDao.findValueByKey(QywxParamKey.CorpID);
        String appId = paramDao.findValueByKey(QywxParamKey.BindAppId);
        QywxApp app = appDao.getById(appId);

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        SytPermissionAccount account = (SytPermissionAccount) authentication.getPrincipal();

        // String urlRoot = PlatformRequestUtil.getUrlRoot(request);
        String urlRoot = app.getRedirectUrlStart();
        String encodeUrl = URLEncoder.encode(urlRoot+"/noAuth/platform/qywx/user_bind_page/bindCallback", StandardCharsets.UTF_8.toString());

        return ApiR.ok(new String[]{cropId, String.valueOf(app.getAgentId()), account.getHumancode(), encodeUrl});
    }
}
