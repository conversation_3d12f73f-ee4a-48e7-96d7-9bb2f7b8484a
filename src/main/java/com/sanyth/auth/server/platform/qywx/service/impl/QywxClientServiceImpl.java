package com.sanyth.auth.server.platform.qywx.service.impl;

import com.sanyth.auth.server.platform.qywx.dao.QywxAppDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxParamDao;
import com.sanyth.auth.server.platform.qywx.dto.*;
import com.sanyth.auth.server.platform.qywx.service.QywxClientService;
import com.sanyth.auth.server.platform.qywx.utils.QywxClientUrl;
import com.sanyth.auth.server.platform.utils.PlatformRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.concurrent.TimeUnit;

/**
 * @since 2022/3/7 16:41
 */
@Service
public class QywxClientServiceImpl implements QywxClientService {

    private final QywxParamDao paramDao;
    private final RedisTemplate<String, String> redisTemplate;
    private final QywxAppDao appDao;
    private final Logger logger = LoggerFactory.getLogger(QywxClientServiceImpl.class);

    public QywxClientServiceImpl(QywxParamDao paramDao, RedisTemplate<String, String> redisTemplate, QywxAppDao appDao) {
        this.paramDao = paramDao;
        this.redisTemplate = redisTemplate;
        this.appDao = appDao;
    }

    /**
     * 获取access_token
     *
     * @since 2022/3/8 9:50
     */
    @Override
    public String getAccessToken(String corpId, String secret) {
        String cacheKey = "platform:qywx:accessToken:" + corpId + ";" + secret;
        String accessToken = redisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.hasText(accessToken)) {
            return accessToken;
        }
        Assert.hasText(corpId, "corpId is empty");
        Assert.hasText(secret, "secret is empty");

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("corpid", corpId);
        map.put("corpsecret", secret);

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<QywxAccessTokenResponse> response = restTemplate.getForEntity(QywxClientUrl.URL_ROOT + QywxClientUrl.URL_GETTOKEN + PlatformRequestUtil.formatParameter(map), QywxAccessTokenResponse.class);
        QywxAccessTokenResponse tokenResponse = response.getBody();

        Assert.notNull(tokenResponse, "tokenResponse is null");
        Assert.isTrue(tokenResponse.isSuccess(), "qywx getAccessToken error: " + tokenResponse.getErrcode() + "; message: " + tokenResponse.getErrmsg());
        Integer expiresIn = tokenResponse.getExpiresIn();
        accessToken = tokenResponse.getAccessToken();
        redisTemplate.opsForValue().set(cacheKey, accessToken, expiresIn, TimeUnit.SECONDS);

        logger.debug("getAccessToken get from remote: " + accessToken);
        return accessToken;
    }

    /**
     * @since 2022/3/9 14:24
     */
    @Override
    public void sendMessageText(String accessToken, Integer agentId, String content, Collection<String> wxUserIds) {
        QywxMessageText text = new QywxMessageText();
        text.setContent(content);

        QywxMessageRequest request = new QywxMessageRequest();
        request.setMsgType("text");
        request.setText(text);
        request.setToUser(String.join("|", wxUserIds));
        request.setAgentId(agentId);

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("access_token", accessToken);

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<QywxMessageResponse> response = restTemplate.postForEntity(QywxClientUrl.URL_ROOT + QywxClientUrl.URL_MESSAGE_SEND + PlatformRequestUtil.formatParameter(map), request, QywxMessageResponse.class);
        QywxMessageResponse messageResponse = response.getBody();

        Assert.notNull(messageResponse, "messageResponse is null");
        Assert.isTrue(messageResponse.isSuccess(), "qywx sendMessageText error: " + messageResponse.getErrcode() + "; message: " + messageResponse.getErrmsg());
    }

    /**
     * @since 2022/3/16 10:26
     */
    @Override
    public QywxUserInfoResponse getUserInfo(String accessToken, String code) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("access_token", accessToken);
        map.put("code", code);

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<QywxUserInfoResponse> response = restTemplate.getForEntity(QywxClientUrl.URL_ROOT + QywxClientUrl.URL_USER_GETUSERINFO + PlatformRequestUtil.formatParameter(map), QywxUserInfoResponse.class);
        QywxUserInfoResponse userInfoResponse = response.getBody();

        Assert.notNull(userInfoResponse, "userInfoResponse is null");
        Assert.isTrue(userInfoResponse.isSuccess(), "qywx getUserInfo error: " + userInfoResponse.getErrcode() + "; message: " + userInfoResponse.getErrmsg());

        return userInfoResponse;
    }

    /**
     * 文本卡片消息
     *
     * @param title       标题，不超过128个字节，超过会自动截断（支持id转译）
     * @param description 描述，不超过512个字节，超过会自动截断（支持id转译）
     * @param url         点击后跳转的链接。最长2048字节，请确保包含了协议头(http/https)
     * @since 2022/9/9 11:31
     */
    @Override
    public void sendMessageTextCard(String accessToken, Integer agentId, Collection<String> wxUserIds, String title, String description, String url) {
        QywxMessageTextCard card = new QywxMessageTextCard();
        card.setTitle(title);
        card.setDescription(description);
        card.setUrl(url);

        QywxMessageRequest request = new QywxMessageRequest();
        request.setMsgType("textcard");
        request.setTextCard(card);
        request.setToUser(String.join("|", wxUserIds));
        request.setAgentId(agentId);

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("access_token", accessToken);
//        map.put("debug", "1");


        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<QywxMessageResponse> response = restTemplate.postForEntity(QywxClientUrl.URL_ROOT + QywxClientUrl.URL_MESSAGE_SEND + PlatformRequestUtil.formatParameter(map), request, QywxMessageResponse.class);
        QywxMessageResponse messageResponse = response.getBody();

        Assert.notNull(messageResponse, "messageResponse is null");
        if (!messageResponse.isSuccess() && messageResponse.getErrcode() == 81013) {
            logger.error("wxUserIds not found: "+ String.join(",", wxUserIds));
            return;
        }
        Assert.isTrue(messageResponse.isSuccess(), "qywx sendMessageText error: " + messageResponse.getErrcode() + "; message: " + messageResponse.getErrmsg());
    }
}
