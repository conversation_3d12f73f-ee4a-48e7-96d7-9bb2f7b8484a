package com.sanyth.auth.server.platform.commonDeal;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2022/2/25 10:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RequestError extends RuntimeException {
    private String content;

    public static RequestError of(String content) {
        RequestError requestError = new RequestError();
        requestError.setContent(content);
        return requestError;
    }

    public static void isTrue(boolean expression, String content) throws RequestError {
        if (!expression) {
            throw RequestError.of(content);
        }
    }

    public static void trigger(String content) throws RequestError {
        throw RequestError.of(content);
    }

}
