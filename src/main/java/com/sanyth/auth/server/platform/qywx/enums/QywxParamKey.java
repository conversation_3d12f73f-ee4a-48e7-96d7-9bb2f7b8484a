package com.sanyth.auth.server.platform.qywx.enums;


import com.sanyth.auth.server.platform.utils.EnumTextI;

/**
 * @since 2021/6/8 15:28
 */
public enum QywxParamKey implements EnumTextI {

    CorpID("企业微信CorpID"),
//    MemberVerificationDepartmentId("成员验证归属部门Id"),
    IsEnableMessage("是否启用消息推送"),
    SendMessageAppId("发送消息的AppId"),
    BindAppId("用户绑定AppId"),
    Oauth2AppId("OAuth2授权登录AppId"),
    IsEnableMessageOutput("是否启用消息推送输出"),
    AddressBookSecret("通讯录Secret");

    private final String text;

    QywxParamKey(String text) {
        this.text = text;
    }

    public String getText() {
        return text;
    }

}
