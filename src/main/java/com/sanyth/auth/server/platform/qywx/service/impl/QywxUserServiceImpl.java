package com.sanyth.auth.server.platform.qywx.service.impl;

import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.dto.QywxUserExcel;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;
import com.sanyth.auth.server.platform.qywx.service.QywxUserService;
import com.sanyth.auth.server.pojo.ImportErrorInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * @since 2022/3/14 10:14
 */
@Service
public class QywxUserServiceImpl implements QywxUserService {

    private final QywxUserDao userDao;

    public QywxUserServiceImpl(QywxUserDao qywxUserDao) {
        this.userDao = qywxUserDao;
    }

    @Override
    public void save(QywxUser dto) {
        QywxUser dto1 = new QywxUser();
        if ((StringUtils.isEmpty(dto.getId()))) {
            dto1.setCreated(new Date());
        } else {
            dto1 = userDao.getById(dto.getId());
            dto1.setUpdated(new Date());
        }

        dto1.setWxId(dto.getWxId());
        dto1.setSysId(dto.getSysId());
        dto1.setDescription(dto.getDescription());

        userDao.saveOrUpdate(dto1);
    }

    @Override
    public List<ImportErrorInfo> doImport(List<QywxUserExcel> userExcels) {
        List<ImportErrorInfo> errorInfo = new ArrayList<>();
        List<QywxUser> list = new LinkedList<>();
        Date now = new Date();
        for (QywxUserExcel userExcel : userExcels) {
            if (StringUtils.isEmpty(userExcel.getSysId())) {
                errorInfo.add(new ImportErrorInfo(userExcel.getRowIndex(), userExcel.getSysId(), "不能为空"));
                continue;
            }
            if (StringUtils.isEmpty(userExcel.getWxId())) {
                errorInfo.add(new ImportErrorInfo(userExcel.getRowIndex(), userExcel.getWxId(), "不能为空"));
                continue;
            }

            QywxUser qywxUser = new QywxUser();
            qywxUser.setSysId(userExcel.getSysId());
            qywxUser.setWxId(userExcel.getWxId());
            QywxUser first = userDao.queryFirst(qywxUser);
            if (first != null) {
                qywxUser = first;
                qywxUser.setUpdated(now);
            }else {
                qywxUser.setCreated(now);
            }
            qywxUser.setDescription(userExcel.getDescription());
            list.add(qywxUser);
        }
        userDao.saveOrUpdateBatch(list);
        return errorInfo;
    }

}
