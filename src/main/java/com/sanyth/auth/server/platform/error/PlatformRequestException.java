package com.sanyth.auth.server.platform.error;

public class PlatformRequestException extends Exception {
    private String content;

    public static PlatformRequestException of(String content) {
        PlatformRequestException error = new PlatformRequestException();
        error.setContent(content);
        return error;
    }

    public static void isTrue(boolean expression, String content) throws PlatformRequestException {
        if (!expression) {
            throw PlatformRequestException.of(content);
        }
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
