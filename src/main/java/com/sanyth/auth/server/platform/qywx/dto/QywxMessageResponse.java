package com.sanyth.auth.server.platform.qywx.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2022/3/9 11:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QywxMessageResponse extends QywxServerResponse{

    @JsonProperty("invaliduser")
    private String invalidUser;
    @JsonProperty("invalidparty")
    private String invalidParty;
    @JsonProperty("invalidtag")
    private String invalidTag;
    @JsonProperty("msgid")
    private String msgId;
    private String response_code;

}
