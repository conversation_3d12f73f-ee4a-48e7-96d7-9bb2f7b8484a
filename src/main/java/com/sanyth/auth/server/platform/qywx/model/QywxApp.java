package com.sanyth.auth.server.platform.qywx.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("platform_qywx_app")
public class QywxApp extends Model<QywxApp> {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String name;
    private Integer agentId;
    private String secret;
    private String redirectUrlStart;
    private Date updated;
    private Date created;
}
