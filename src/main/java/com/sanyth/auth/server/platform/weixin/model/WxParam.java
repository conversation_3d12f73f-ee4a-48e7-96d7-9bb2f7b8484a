package com.sanyth.auth.server.platform.weixin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.sanyth.auth.server.platform.weixin.enums.WxParamKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 微信公众号 参数
 *
 * @since 2024/12/18 11:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PLATFORM_WX_PARAM")
public class WxParam extends Model<WxParam> {

    @TableId(value = "KEY", type = IdType.INPUT)
    private WxParamKey key;
    private Boolean enabled;
    private String value;
    private String description;
    private Date updated;
    private Date created;
}
