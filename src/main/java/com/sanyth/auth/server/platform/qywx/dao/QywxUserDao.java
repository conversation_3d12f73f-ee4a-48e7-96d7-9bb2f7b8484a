package com.sanyth.auth.server.platform.qywx.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.platform.commonDeal.BaseQueryInterface;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;

import java.util.List;
import java.util.Set;

/**
 * @since 2022/2/23 14:22
 */
public interface QywxUserDao extends IService<QywxUser> {
    Page<QywxUser> queryPage(BaseQueryInterface<QywxUser> query);

    List<QywxUser> queryReceiveMessageUsersBySysId(Set<String> sysIds);

    long count(QywxUser query);

    QywxUser queryFirst(QywxUser query);
}
