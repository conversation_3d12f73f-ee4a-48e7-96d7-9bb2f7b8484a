package com.sanyth.auth.server.platform.qywx.web;

import com.sanyth.auth.server.platform.error.PlatformRequestException;
import com.sanyth.auth.server.platform.qywx.service.QywxUserBindService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @since 2022/8/25 11:44
 */
@Controller
@RequestMapping("/noAuth/platform/qywx/user_bind_page")
public class NoAuthQywxUserBindPageController {

    private final QywxUserBindService qywxUserBindService;

    public NoAuthQywxUserBindPageController(QywxUserBindService qywxUserBindService) {
        this.qywxUserBindService = qywxUserBindService;
    }

    @GetMapping("bindCallback")
    public String bindCallback(String code, String state, ModelMap modelMap) {
        String text = "绑定成功";
        try {
            qywxUserBindService.bindCallback(code, state);
        } catch (PlatformRequestException e) {
            e.printStackTrace();
            text = e.getContent();
        } catch (Exception e) {
            e.printStackTrace();
            text = "其他异常";
        }
        modelMap.put("text", text);
        return "platform/qywx/bindCallback";
    }

}
