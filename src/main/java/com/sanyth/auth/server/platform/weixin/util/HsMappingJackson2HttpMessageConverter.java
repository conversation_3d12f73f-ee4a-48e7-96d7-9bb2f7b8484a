package com.sanyth.auth.server.platform.weixin.util;

import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.ArrayList;
import java.util.List;

/**
 * 兼容处理 Content-Type 为 text/plain 类型的json返回值
 *
 * @since 2024/12/17 11:51
 */
public class HsMappingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {
    public HsMappingJackson2HttpMessageConverter() {
        List<MediaType> mediaTypes = new ArrayList<>();
        mediaTypes.add(MediaType.TEXT_PLAIN);
        setSupportedMediaTypes(mediaTypes);
    }
}
