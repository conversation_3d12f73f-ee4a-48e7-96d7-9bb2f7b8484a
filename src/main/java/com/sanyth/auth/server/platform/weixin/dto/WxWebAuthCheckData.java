package com.sanyth.auth.server.platform.weixin.dto;

import lombok.Data;

import javax.servlet.http.HttpSession;
import java.io.Serializable;

/**
 * @since 2024/12/16 14:45
 */
@Data
public class WxWebAuthCheckData implements Serializable {

    public static final String WX_SESSION_KEY_WEB_AUTH = "WxSessionKey-WebAuth";

    public static WxWebAuthCheckData getFromHttpSession(HttpSession httpSession) {
        return (WxWebAuthCheckData) httpSession.getAttribute(WX_SESSION_KEY_WEB_AUTH);
    }

//    public static void setToSession(HttpSession httpSession, String appId, String openId) {
//        WxWebAuthCheckData checkData = new WxWebAuthCheckData();
//        checkData.setAppId(appId);
//        checkData.setOpenId(openId);
//        httpSession.setAttribute(WX_SESSION_KEY_WEB_AUTH, checkData);
//    }

    public static void setToSession(HttpSession httpSession, String appId, String openId, String psi) {
        WxWebAuthCheckData checkData = new WxWebAuthCheckData();
        checkData.setAppId(appId);
        checkData.setOpenId(openId);
        checkData.setExChangeKey(psi);
        httpSession.setAttribute(WX_SESSION_KEY_WEB_AUTH, checkData);
    }

    private String appId;
    private String openId;
    private String exChangeKey;

}
