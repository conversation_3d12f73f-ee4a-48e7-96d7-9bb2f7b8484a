package com.sanyth.auth.server.platform.weixin.web;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.platform.commonDeal.ApiR;
import com.sanyth.auth.server.platform.commonDeal.PfBaseQuery;
import com.sanyth.auth.server.platform.commonDeal.ReBaseApiController;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.weixin.mapper.WxAppMapper;
import com.sanyth.auth.server.platform.weixin.model.WxApp;
import com.sanyth.auth.server.platform.weixin.query.WxAppQuery;
import com.sanyth.auth.server.platform.weixin.service.WxAppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 微信公众号 应用 接口
 *
 * @since 2024/12/13 13:50
 */
@Slf4j
@RestController
@RequestMapping("/platform/wx/app")
public class WxAppController extends ReBaseApiController {

    private final WxAppMapper appMapper;
    private final WxAppService appService;

    public WxAppController(WxAppMapper appMapper, WxAppService appService) {
        this.appMapper = appMapper;
        this.appService = appService;
    }

    private QueryWrapper<WxApp> buildWrapper(WxAppQuery query) {
        QueryWrapper<WxApp> wrapper = new QueryWrapper<>();
        if (query != null) {
            if (StringUtils.isNotBlank(query.getNameLike())) {
                wrapper.like("name", query.getNameLike());
            }
        }

        wrapper.orderByDesc("created");
        return wrapper;
    }

    @PostMapping("page")
    public ApiR<Page<WxApp>> page(@RequestBody PfBaseQuery<WxAppQuery> query) {
        Page<WxApp> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<WxApp> wrapper = buildWrapper(query.getParam());
        return ApiR.ok(appMapper.selectPage(page, wrapper));
    }

    private void fieldCopy(WxApp from, WxApp to) {
        RequestError.isTrue(StringUtils.isNotBlank(from.getName()), "名称不能为空");
        RequestError.isTrue(StringUtils.isNotBlank(from.getSecret()), "secret不能为空");
//        RequestError.isTrue(StringUtils.isNotBlank(from.getH5Url()), "h5Url不能为空");
        to.setName(from.getName());
        to.setSecret(from.getSecret());
//        to.setH5Url(from.getH5Url());
        to.setDebug(from.getDebug());
    }

    @PostMapping("save")
    public ApiR<String> save(@RequestBody WxApp dto) {
        RequestError.isTrue(StringUtils.isNotBlank(dto.getId()), "id 不能为空");
        WxApp dto1 = appMapper.selectById(dto.getId());
        if (dto1 == null) {
            dto1 = new WxApp();
            dto1.setCreated(new Date());
            dto1.setId(dto.getId());
            fieldCopy(dto, dto1);
//            pfCmCommonDao.insert(appMapper, dto1);
//            appDao.save(dto1);
            appMapper.insert(dto1);
        } else {
            dto1.setUpdated(new Date());
            fieldCopy(dto, dto1);
//            pfCmCommonDao.updateById(appMapper, dto1);
//            appDao.updateById(dto1);
            appMapper.updateById(dto1);
        }

        return ApiR.ok();
    }

    @PostMapping("delete")
    public ApiR<String> delete(@RequestBody WxApp dto) {
//        appMapper.deleteById(dto.getId());
        appService.deleteById(dto.getId());
        return ApiR.ok();
    }

    @GetMapping("get")
    public ApiR<WxApp> get(String id) {
        return ApiR.ok(appMapper.selectById(id));
    }

}
