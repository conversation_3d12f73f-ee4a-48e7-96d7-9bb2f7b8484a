package com.sanyth.auth.server.platform.qywx.query;

import com.sanyth.auth.server.platform.commonDeal.BaseQueryAbstract;
import com.sanyth.auth.server.platform.commonDeal.BaseQueryInterface;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2022/3/4 16:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QywxUserQuery extends BaseQueryAbstract implements BaseQueryInterface<QywxUser> {

    private QywxUser param;

}
