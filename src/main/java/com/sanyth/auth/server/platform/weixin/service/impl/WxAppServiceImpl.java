package com.sanyth.auth.server.platform.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.auth.server.platform.weixin.mapper.WxAppMapper;
import com.sanyth.auth.server.platform.weixin.mapper.WxUserMapper;
import com.sanyth.auth.server.platform.weixin.model.WxUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @since 2024/12/13 15:02
 */
@Service
public class WxAppServiceImpl implements com.sanyth.auth.server.platform.weixin.service.WxAppService {

    private final WxAppMapper appMapper;
    private final WxUserMapper userMapper;

    public WxAppServiceImpl(WxAppMapper appMapper, WxUserMapper userMapper) {
        this.appMapper = appMapper;
        this.userMapper = userMapper;
    }

    @Transactional
    @Override
    public void deleteById(String id) {
        QueryWrapper<WxUser> wrapper = new QueryWrapper<>();
        wrapper.eq("app_id", id);
        userMapper.delete(wrapper);
        appMapper.deleteById(id);
    }
}
