package com.sanyth.auth.server.platform.qywx.service.impl;

import com.sanyth.auth.server.platform.qywx.dao.QywxParamDao;
import com.sanyth.auth.server.platform.qywx.model.QywxParam;
import com.sanyth.auth.server.platform.qywx.service.QywxParamService;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class QywxParamServiceImpl implements QywxParamService {

    private final QywxParamDao paramDao;

    public QywxParamServiceImpl(QywxParamDao paramDao) {
        this.paramDao = paramDao;
    }

    @Override
    public void save(QywxParam dto) {
        dto.setUpdated(new Date());
        paramDao.saveOrUpdate(dto);
    }

}
