package com.sanyth.auth.server.platform.qywx.tasks;

import com.sanyth.auth.server.platform.qywx.dao.QywxAppDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxParamDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.dto.QywxMessageRedisPack;
import com.sanyth.auth.server.platform.qywx.enums.QywxParamKey;
import com.sanyth.auth.server.platform.qywx.model.QywxApp;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;
import com.sanyth.auth.server.platform.qywx.service.QywxClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.LinkedTransferQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @since 2022/3/11 15:46
 */
public class QywxDealMessageTask implements Runnable {

    private final Logger logger = LoggerFactory.getLogger(QywxDealMessageTask.class);
    private final AtomicBoolean isDeal;
    private final QywxUserDao userDao;
    private final QywxClientService clientService;
    private final QywxParamDao paramDao;
    private final QywxAppDao appDao;
    private final LinkedTransferQueue<QywxMessageRedisPack> messageQueue;
    private String accessToken;
    private Integer agentId;

    public QywxDealMessageTask(AtomicBoolean isDeal, QywxUserDao userDao, QywxClientService clientService, QywxParamDao paramDao, QywxAppDao appDao, LinkedTransferQueue<QywxMessageRedisPack> messageQueue) {
        this.isDeal = isDeal;
        this.clientService = clientService;
        this.userDao = userDao;
        this.paramDao = paramDao;
        this.appDao = appDao;
        this.messageQueue = messageQueue;
    }

    @Override
    public void run() {
        try {
            String corpId = paramDao.findValueByKey(QywxParamKey.CorpID);
            Assert.hasText(corpId, "企业微信CorpID未配置");

            String appId = paramDao.findValueByKey(QywxParamKey.SendMessageAppId);
            Assert.hasText(appId, "发送消息的AppId未配置");

            QywxApp app = appDao.getById(appId);
            Assert.notNull(app, "发送消息的APP不存在");
            Assert.notNull(app.getAgentId(), "发送消息的APP的agentId未配置");
            Assert.hasText(app.getSecret(), "发送消息的APP的secret未配置");
            agentId = app.getAgentId();

            boolean isEnableMessageOutput = paramDao.findEnabledByKey(QywxParamKey.IsEnableMessageOutput);

            accessToken = clientService.getAccessToken(corpId, app.getSecret());

            QywxMessageRedisPack messagePack1;
            do {
                messagePack1 = messageQueue.poll();
                if (messagePack1 != null) {
                    Set<String> wxIds = new HashSet<>(messagePack1.getUserIds());
                    List<QywxUser> users = userDao.queryReceiveMessageUsersBySysId(messagePack1.getUserIds());
                    for (QywxUser user : users) {
                        wxIds.add(user.getWxId());
                    }
                    if (isEnableMessageOutput) {
                        logger.info("wxUserIds: " + String.join(",", wxIds));
                    }
                    List<String> wxIds2 = new ArrayList<>(1000);
                    for (String wxId : wxIds) {
                        wxIds2.add(wxId);
                        if (wxIds2.size() == 1000) {
                            doSend(messagePack1, wxIds2);
                            wxIds2 = new ArrayList<>(1000);
                        }
                    }
                    if (wxIds2.size() > 0)
                        doSend(messagePack1, wxIds2);
                }
            } while (messagePack1 != null);

        } catch (IllegalArgumentException e) {
            logger.error(e.getMessage());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        isDeal.set(false);
    }

    private void doSend(QywxMessageRedisPack messagePack1, List<String> wxIds) {
        switch (messagePack1.getType()) {
            case Text:
                clientService.sendMessageText(accessToken, agentId, messagePack1.getContent(), wxIds);
                break;
            case TextCard:
                clientService.sendMessageTextCard(accessToken, agentId, wxIds, messagePack1.getTitle(), messagePack1.getDescription(), messagePack1.getUrl());
                break;
        }
    }

}
