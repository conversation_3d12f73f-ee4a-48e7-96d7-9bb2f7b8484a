package com.sanyth.auth.server.platform.weixin.web;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.platform.commonDeal.ApiR;
import com.sanyth.auth.server.platform.commonDeal.PfBaseQuery;
import com.sanyth.auth.server.platform.commonDeal.ReBaseApiController;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.weixin.enums.WxParamKey;
import com.sanyth.auth.server.platform.weixin.mapper.WxParamMapper;
import com.sanyth.auth.server.platform.weixin.model.WxParam;
import com.sanyth.auth.server.platform.weixin.query.WxParamQuery;
import com.sanyth.auth.server.platform.weixin.vo.WxOptionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static java.util.stream.Collectors.toSet;

/**
 * 微信公众号 参数 接口
 *
 * @since 2024/12/27 15:04
 */
@Slf4j
@RestController
@RequestMapping("/platform/wx/param")
public class WxParamController extends ReBaseApiController {

    private final WxParamMapper paramMapper;

    public WxParamController(WxParamMapper paramMapper) {
        this.paramMapper = paramMapper;
    }

    private QueryWrapper<WxParam> buildWrapper(WxParamQuery query) {
        QueryWrapper<WxParam> wrapper = new QueryWrapper<>();
//        if (query != null) {
//            if (StringUtils.isNotBlank(query.getNameLike())) {
//                wrapper.like("name", query.getNameLike());
//            }
//        }

        wrapper.orderByDesc("created");
        return wrapper;
    }

    @PostMapping("page")
    public ApiR<Page<WxParam>> page(@RequestBody PfBaseQuery<WxParamQuery> query) {
        Page<WxParam> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<WxParam> wrapper = buildWrapper(query.getParam());
        return ApiR.ok(paramMapper.selectPage(page, wrapper));
    }

    private void fieldCopy(WxParam from, WxParam to) {
        to.setValue(from.getValue());
        to.setDescription(from.getDescription());
        if (from.getEnabled() == null) {
            to.setEnabled(false);
        } else {
            to.setEnabled(from.getEnabled());
        }
    }

    @GetMapping("optionalKey")
    public ApiR<List<WxOptionVo>> optionalKey() {
        List<WxParam> params = paramMapper.selectList(new QueryWrapper<>());
        Set<WxParamKey> keys = params.stream().map(WxParam::getKey).collect(toSet());
        WxParamKey[] values = WxParamKey.values();
        List<WxOptionVo> vos = new ArrayList<>();
        for (WxParamKey value : values) {
            if (!keys.contains(value)) {
                WxOptionVo vo = new WxOptionVo();
                vo.setName(value.getText());
                vo.setValue(value.name());
                vos.add(vo);
            }
        }
        return ApiR.ok(vos);
    }

    @PostMapping("save")
    public ApiR<String> save(@RequestBody WxParam dto) {
        RequestError.isTrue(dto.getKey() != null, "key 不能为空");
        WxParam dto1 = paramMapper.selectById(dto.getKey());
        if (dto1 == null) {
            dto1 = new WxParam();
            dto1.setCreated(new Date());
            dto1.setKey(dto.getKey());
            fieldCopy(dto, dto1);
            paramMapper.insert(dto1);
        } else {
            dto1.setUpdated(new Date());
            fieldCopy(dto, dto1);
            paramMapper.updateById(dto1);
        }

        return ApiR.ok();
    }

    @PostMapping("delete")
    public ApiR<String> delete(@RequestBody WxParam dto) {
        paramMapper.deleteById(dto.getKey());
        return ApiR.ok();
    }

    @GetMapping("get")
    public ApiR<WxParam> get(String key) {
        return ApiR.ok(paramMapper.selectById(key));
    }

}
