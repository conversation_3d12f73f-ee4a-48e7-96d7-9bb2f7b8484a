package com.sanyth.auth.server.platform.weixin.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.platform.commonDeal.ApiR;
import com.sanyth.auth.server.platform.commonDeal.PfBaseQuery;
import com.sanyth.auth.server.platform.commonDeal.ReBaseApiController;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.weixin.mapper.WxAppMapper;
import com.sanyth.auth.server.platform.weixin.mapper.WxUserMapper;
import com.sanyth.auth.server.platform.weixin.model.WxApp;
import com.sanyth.auth.server.platform.weixin.model.WxUser;
import com.sanyth.auth.server.platform.weixin.query.WxUserQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 微信公众号 用户 接口
 *
 * @since 2024/12/13 13:50
 */
@Slf4j
@RestController
@RequestMapping("/platform/wx/user")
public class WxUserController extends ReBaseApiController {

    private final WxUserMapper userMapper;
    private final WxAppMapper appMapper;

    public WxUserController(WxUserMapper userMapper, WxAppMapper appMapper) {
        this.userMapper = userMapper;
        this.appMapper = appMapper;
    }

    private QueryWrapper<WxUser> buildWrapper(WxUserQuery query) {
        RequestError.isTrue(query != null && StringUtils.isNotBlank(query.getAppId()), "appId 不能为空");
        QueryWrapper<WxUser> wrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(query.getAppId())) {
            wrapper.eq("app_id", query.getAppId());
        }
        if (StringUtils.isNotBlank(query.getOpenIdLike())) {
            wrapper.like("open_id", query.getOpenIdLike());
        }
        if (StringUtils.isNotBlank(query.getSystemUsernameLike())) {
            wrapper.like("system_username", query.getSystemUsernameLike());
        }

        wrapper.orderByDesc("created");
        return wrapper;
    }

    @PostMapping("page")
    public ApiR<Page<WxUser>> page(@RequestBody PfBaseQuery<WxUserQuery> query) {
        Page<WxUser> page = new Page<>(query.getPage(), query.getPageSize());
        QueryWrapper<WxUser> wrapper = buildWrapper(query.getParam());
        return ApiR.ok(userMapper.selectPage(page, wrapper));
    }

    private void fieldCopy(WxUser from, WxUser to) {
        RequestError.isTrue(StringUtils.isNotBlank(from.getOpenId()), "openId不能为空");
        RequestError.isTrue(StringUtils.isNotBlank(from.getSystemUsername()), "systemUsername不能为空");
        to.setOpenId(from.getOpenId());
        to.setSystemUsername(from.getSystemUsername());
    }

    @PostMapping("save")
    public ApiR<String> save(@RequestBody WxUser dto) {
        if (StringUtils.isEmpty(dto.getId())) {
            RequestError.isTrue(StringUtils.isNotBlank(dto.getAppId()), "appId 不能为空");
            WxApp app = appMapper.selectById(dto.getAppId());
            RequestError.isTrue(app != null, "appId异常");

            WxUser dto1 = new WxUser();
            dto1.setAppId(dto.getAppId());
            dto1.setCreated(new Date());
            dto1.setId(dto.getId());
            fieldCopy(dto, dto1);
            userMapper.insert(dto1);
        } else {
            WxUser dto1 = userMapper.selectById(dto.getId());
            dto1.setUpdated(new Date());
            fieldCopy(dto, dto1);
            userMapper.updateById(dto1);
        }

        return ApiR.ok();
    }

    @PostMapping("delete")
    public ApiR<String> delete(@RequestBody WxUser dto) {
        userMapper.deleteById(dto.getId());
        return ApiR.ok();
    }

    @GetMapping("get")
    public ApiR<WxUser> get(String id) {
        return ApiR.ok(userMapper.selectById(id));
    }
}
