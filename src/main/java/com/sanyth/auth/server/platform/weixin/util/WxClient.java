package com.sanyth.auth.server.platform.weixin.util;

import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.weixin.dto.WxOauth2AccessTokenResponse;
import com.sanyth.auth.server.platform.weixin.model.WxApp;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * @since 2024/12/16 14:44
 */
public class WxClient {

    public static String getOpenId(String code, WxApp wxApp) {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString("https://api.weixin.qq.com/sns/oauth2/access_token");

        uriComponentsBuilder.queryParam("appid", wxApp.getId());
        uriComponentsBuilder.queryParam("secret", wxApp.getSecret());
        uriComponentsBuilder.queryParam("code", code);
        uriComponentsBuilder.queryParam("grant_type", "authorization_code");

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().add(new HsMappingJackson2HttpMessageConverter());
        WxOauth2AccessTokenResponse tokenRes = restTemplate.getForObject(uriComponentsBuilder.build().toUri(), WxOauth2AccessTokenResponse.class);
        RequestError.isTrue(tokenRes != null, "getOpenId error. tokenRes is null");
        RequestError.isTrue(tokenRes.getErrCode() == null, "getOpenId error: " + tokenRes.getErrCode() + "; message: " + tokenRes.getErrMsg());

        return tokenRes.getOpenid();
    }
}
