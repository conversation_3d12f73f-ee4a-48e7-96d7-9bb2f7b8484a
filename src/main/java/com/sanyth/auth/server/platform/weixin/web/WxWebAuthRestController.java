package com.sanyth.auth.server.platform.weixin.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.mapper.SytPermissionAccountRoleMapper;
import com.sanyth.auth.server.mapper.SytPermissionRoleMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytPermissionAccountRole;
import com.sanyth.auth.server.model.SytPermissionRole;
import com.sanyth.auth.server.platform.commonDeal.ReBaseRestController;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.utils.PlatformRequestUtil;
import com.sanyth.auth.server.platform.weixin.enums.WxParamKey;
import com.sanyth.auth.server.platform.weixin.enums.WxQrLoginStatus;
import com.sanyth.auth.server.platform.weixin.mapper.WxParamMapper;
import com.sanyth.auth.server.platform.weixin.model.WxParam;
import com.sanyth.auth.server.platform.weixin.vo.WxQrLoginCodeVo;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @since 2024/12/25 9:42
 */
@RestController
@RequestMapping("/noAuth/platform/wei_xin/web_auth/rest")
public class WxWebAuthRestController extends ReBaseRestController {

    private final WxParamMapper wxParamMapper;
    private final SytPermissionAccountRoleMapper accountRoleMapper;
    private final SytPermissionAccountMapper accountMapper;
    private final SytPermissionRoleMapper roleMapper;
    private final RedisTemplate<Object, Object> redisTemplate;

    public WxWebAuthRestController(WxParamMapper wxParamMapper, SytPermissionAccountRoleMapper accountRoleMapper, SytPermissionAccountMapper accountMapper, SytPermissionRoleMapper roleMapper, RedisTemplate<Object, Object> redisTemplate) {
        this.wxParamMapper = wxParamMapper;
        this.accountRoleMapper = accountRoleMapper;
        this.accountMapper = accountMapper;
        this.roleMapper = roleMapper;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 是否启用二维码
     *
     * @return boolean
     */
    @RequestMapping("qrCodeEnable")
    public boolean isEnableQrCode(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent.contains("Mobile")) {
            return false;
        }
        WxParam param = wxParamMapper.selectById(WxParamKey.QrCodeAppId);
        if (param == null) {
            return false;
        }
        return param.getEnabled();
    }

//    @RequestMapping("generate_qr_login_key")
//    public String generateQrLoginKey() {
//        UUID uuid = UUID.randomUUID();
//        String uuidString = uuid.toString();
//        redisTemplate.opsForValue().set(uuidString, WxQrLoginStatus.init, 30, TimeUnit.SECONDS);
//        return uuidString;
//    }

    @RequestMapping("generate_qr_code")
    public WxQrLoginCodeVo generateQrCode(HttpServletRequest request) {
        HttpSession httpSession = request.getSession();
        WxParam param = wxParamMapper.selectById(WxParamKey.QrCodeAppId);
        RequestError.isTrue(param != null && param.getEnabled(), "未配置二维码应用ID");
        String appId = param.getValue();

        UUID uuid = UUID.randomUUID();
        String uuidString = uuid.toString();
        redisTemplate.opsForValue().set(uuidString, WxQrLoginStatus.init, 60, TimeUnit.SECONDS);
        LocalDateTime ldt = LocalDateTime.now().plusMinutes(1);
        String datetimeText = ldt.format(DateTimeFormatter.ISO_DATE_TIME);
        httpSession.setAttribute("wx_qr_login_key", uuidString);

        String url_root = PlatformRequestUtil.getUrlRoot(request);
        WxParam urlRootParam = wxParamMapper.selectById(WxParamKey.UrlRoot);
        if (urlRootParam != null && urlRootParam.getEnabled()) {
            url_root = urlRootParam.getValue();
        }
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(url_root);
        uriComponentsBuilder.path(WxWebAuthController.ControllerRoot + "/preIndex");
        uriComponentsBuilder.queryParam("id", appId);
        uriComponentsBuilder.queryParam("eck", uuidString);
        String url = uriComponentsBuilder.build().toUri().toString();
//        System.out.println("url: " + url);
        return new WxQrLoginCodeVo(url, datetimeText);
    }

    @RequestMapping("check_status")
    public WxQrLoginStatus checkStatus(HttpSession httpSession) {
        String exChangeKey = (String) httpSession.getAttribute("wx_qr_login_key");
        if (exChangeKey == null) {
            return null;
        }
        WxQrLoginStatus qrLoginStatus = (WxQrLoginStatus) redisTemplate.opsForValue().get(exChangeKey);
        if (qrLoginStatus == WxQrLoginStatus.confirm) {
            String username = (String) redisTemplate.opsForValue().get(exChangeKey + "-username");
//            RequestError.isTrue(username != null, "程序异常");
            if (username != null) {
                doAuth(username);
//                redisTemplate.delete(exChangeKey);
                redisTemplate.delete(exChangeKey + "-username");
            }
        }
        return qrLoginStatus;
    }

//    @RequestMapping("do_auth")
//    public String do_auth(String username) {
//        doAuth(username);
//        return "success";
//    }

    private void doAuth(String username) {
        QueryWrapper<SytPermissionAccount> accountWrapper = new QueryWrapper<>();
        accountWrapper.eq("humancode", username);
        SytPermissionAccount account = accountMapper.selectOne(accountWrapper);
        RequestError.isTrue(account != null, String.format("【%s】系统账号不存在", username));

        QueryWrapper<SytPermissionAccountRole> accountRoleWrapper = new QueryWrapper<>();
        accountRoleWrapper.eq("account_id", account.getId());
        List<SytPermissionAccountRole> rList = accountRoleMapper.selectList(accountRoleWrapper);
        RequestError.isTrue(!CollectionUtils.isEmpty(rList), "未绑定角色");

        SytPermissionRole role = roleMapper.selectById(rList.get(0).getRoleId());

        SecurityContext context = SecurityContextHolder.getContext();
        Authentication authentication = context.getAuthentication();
        account.setRoles(Collections.singletonList(role));
        UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(
                account,
                authentication.getCredentials(),
                Collections.singletonList(new SimpleGrantedAuthority(role.getRolekey())));
        auth.setDetails(authentication.getDetails());
        context.setAuthentication(auth);
    }

    @RequestMapping("getRedirectUrl")
    public String getRedirectUrl(HttpSession httpSession) {
//        logger.info("Success hanlder");  // 这里加入需要的处理
        String redirectUrl = "/index";      // 缺省的登陆成功页面
        SavedRequest savedRequest = (SavedRequest) httpSession.getAttribute("SPRING_SECURITY_SAVED_REQUEST");
        if (savedRequest != null) {
            redirectUrl = savedRequest.getRedirectUrl();
            httpSession.removeAttribute("SPRING_SECURITY_SAVED_REQUEST");
            // 记录认证日志
//            saveAuthLogs(request, savedRequest, authentication);
        }
//        saveLoginLogs(request, authentication);
//        String uri = request.getRequestURI();
//        if(uri.indexOf("user/auth/callback/") > -1){
//            response.sendRedirect(redirectUrl);
//        }

        return redirectUrl;
    }
}
