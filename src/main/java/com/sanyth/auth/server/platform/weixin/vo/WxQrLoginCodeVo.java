package com.sanyth.auth.server.platform.weixin.vo;


import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @since 2024/12/26 15:06
 */
@Data
public class WxQrLoginCodeVo {

    public WxQrLoginCodeVo(String url, String endDatetimeText) {
        int width = 200;
        int height = 180;
        Map<EncodeHintType, Object> encodeHints = new HashMap<>();
        encodeHints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        encodeHints.put(EncodeHintType.MARGIN, 1); /* default = 4 */

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            BitMatrix bitMatrix = new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, width, height, encodeHints);
            MatrixToImageWriter.writeToStream(bitMatrix, "png", outputStream);
        } catch (WriterException | IOException e) {
            RequestError.trigger("生成二维码失败 :" + e.getMessage());
        }
        this.dataUrl = "data:image/png;base64," + Base64.encodeBase64String(outputStream.toByteArray());
        this.endDatetimeText = endDatetimeText;
    }

    private String dataUrl;
    private String endDatetimeText;

}
