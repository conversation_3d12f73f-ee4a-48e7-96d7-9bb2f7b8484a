package com.sanyth.auth.server.platform.qywx.web;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.platform.commonDeal.ApiR;
import com.sanyth.auth.server.platform.commonDeal.ReBaseApiController;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.qywx.dao.QywxAppDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxParamDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.dto.QywxUserExcel;
import com.sanyth.auth.server.platform.qywx.enums.QywxParamKey;
import com.sanyth.auth.server.platform.qywx.model.QywxApp;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;
import com.sanyth.auth.server.platform.qywx.query.QywxUserMessageQuery;
import com.sanyth.auth.server.platform.qywx.query.QywxUserQuery;
import com.sanyth.auth.server.platform.qywx.service.QywxClientService;
import com.sanyth.auth.server.platform.qywx.service.QywxUserService;
import com.sanyth.auth.server.pojo.ImportErrorInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * @since 2022/3/4 16:37
 */
@Slf4j
@RestController
@RequestMapping( "/platform/qywx/user")
public class QywxUserController  extends ReBaseApiController {

    private final QywxUserDao xDao;
    private final QywxParamDao paramDao;
    private final QywxAppDao appDao;
    private final QywxClientService clientService;
    private final QywxUserService userService;
    private final RedisTemplate<String, String> redisTemplate;

    public QywxUserController(QywxUserDao xDao, QywxParamDao paramDao, QywxAppDao appDao, QywxClientService clientService, QywxUserService userService, RedisTemplate<String, String> redisTemplate) {
        this.xDao = xDao;
        this.paramDao = paramDao;
        this.appDao = appDao;
        this.clientService = clientService;
        this.userService = userService;
        this.redisTemplate = redisTemplate;
    }

    @GetMapping("list")
    public ApiR<Page<QywxUser>> list(QywxUserQuery query) {
        Page<QywxUser> page = xDao.queryPage(query);
        return ApiR.ok(page);
    }

    @PostMapping("save")
    public ApiR<String> save(@RequestBody QywxUser dto) {
        userService.save(dto);
        return ApiR.ok();
    }

    @DeleteMapping("delete")
    public ApiR<String> delete(@RequestBody QywxUser dto) {
        xDao.removeById(dto.getId());
        return ApiR.ok();
    }

    @GetMapping("get")
    public ApiR<QywxUser> get(Long id) {
        QywxUser select = xDao.getById(id);
        return ApiR.ok(select);
    }

    @PutMapping("sendMessage")
    public ApiR<String> sendMessage(@RequestBody QywxUserMessageQuery query) throws RequestError {
//        Set<String> ids = query.getIds();
//        Set<String> wxUserIds = new HashSet<>();
//        for (String id : ids) {
//            QywxUser user = xDao.getById(id);
//            wxUserIds.add(user.getWxId());
//        }

        String corpId = paramDao.findValueByKey(QywxParamKey.CorpID);
        RequestError.isTrue(StringUtils.hasText(corpId), "企业微信CorpID未配置");
        String appId = paramDao.findValueByKey(QywxParamKey.SendMessageAppId);
        RequestError.isTrue(StringUtils.hasText(appId), "发送消息的AppId未配置");
        QywxApp app = appDao.getById(appId);
        RequestError.isTrue(app != null, "发送消息的APP不存在");
        RequestError.isTrue(app.getAgentId() != null, "发送消息的APP的agentId未配置");
        RequestError.isTrue(StringUtils.hasText(app.getSecret()), "发送消息的APP的secret未配置");
        String accessToken = clientService.getAccessToken(corpId, app.getSecret());
        clientService.sendMessageText(accessToken, app.getAgentId(), query.getContent(), Collections.singleton(query.getWxUserId()));
        return ApiR.ok();
    }

    @RequestMapping("downloadImportTemplate")
    public ResponseEntity<Resource> downloadImportTemplate() throws UnsupportedEncodingException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, QywxUserExcel.class).excelType(ExcelTypeEnum.XLSX).sheet("sheet1").doWrite(Collections.emptyList());
        ByteArrayResource byteArrayResource = new ByteArrayResource(outputStream.toByteArray());

        String fileName = "importTemplate.xlsx";
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDisposition(ContentDisposition.parse("attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())));
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        httpHeaders.setContentLength(outputStream.size());
        httpHeaders.setLastModified(1660785705109L);

        return new ResponseEntity<>(byteArrayResource, httpHeaders, HttpStatus.OK);
    }

    @RequestMapping("importData")
    public ApiR<String> importData(@RequestParam("file") MultipartFile file, HttpServletResponse response) throws IOException {
        InputStream inputStream = file.getInputStream();
        ExcelReaderBuilder excelRB = EasyExcel.read(inputStream, QywxUserExcel.class, new AnalysisEventListener<QywxUserExcel>() {
            final LinkedList<QywxUserExcel> userExcels = new LinkedList<>();

            @Override
            public void invoke(QywxUserExcel qywxUserExcel, AnalysisContext analysisContext) {
                qywxUserExcel.setRowIndex(analysisContext.readRowHolder().getRowIndex());
                userExcels.add(qywxUserExcel);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {

            }
        });

        List<QywxUserExcel> userExcels = excelRB.sheet().doReadSync();
        List<ImportErrorInfo> importErrorInfos = userService.doImport(userExcels);
        if (CollectionUtils.isEmpty(importErrorInfos))
            return ApiR.ok();
        redisTemplate.opsForValue().set("QywxUser import error "+ RequestContextHolder.currentRequestAttributes().getSessionId(), JSONUtils.toJSONString(importErrorInfos), 60);

        return ApiR.ok("error");
    }

    @RequestMapping("downloadImportedError")
    public ResponseEntity<Resource> downloadImportedError() throws UnsupportedEncodingException {
        String s = redisTemplate.opsForValue().get("QywxUser import error " + RequestContextHolder.currentRequestAttributes().getSessionId());
        List<ImportErrorInfo> importErrorInfos = (List<ImportErrorInfo>) JSONUtils.parse(s);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, ImportErrorInfo.class)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("sheet1")
                .doWrite(importErrorInfos);
        ByteArrayResource byteArrayResource = new ByteArrayResource(outputStream.toByteArray());

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDisposition(ContentDisposition.parse("attachment; filename=" + URLEncoder.encode("错误信息.xlsx", StandardCharsets.UTF_8.toString())));
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        httpHeaders.setContentLength(outputStream.size());
        httpHeaders.setLastModified(System.currentTimeMillis());

        return new ResponseEntity<>(byteArrayResource, httpHeaders, HttpStatus.OK);
    }
}
