package com.sanyth.auth.server.platform.weixin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 微信公众号 用户映射表
 *
 * @since 2024/12/12 16:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PLATFORM_WX_USER")
public class WxUser extends Model<WxUser> {

    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    @TableField("OPEN_ID")
    private String openId;
    @TableField("SYSTEM_USERNAME")
    private String systemUsername;
    private Date updated;
    private Date created;
    @TableField("APP_ID")
    private String appId;

}
