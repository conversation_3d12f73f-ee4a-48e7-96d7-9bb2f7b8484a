package com.sanyth.auth.server.platform.dto;

import java.io.Serializable;
import java.util.Objects;

public class NameValuePair implements Serializable {

    private String name;
    private String value;

    public NameValuePair() {
        this((String)null, (String)null);
    }

    public NameValuePair(String name, String value) {
        this.name = null;
        this.value = null;
        this.name = name;
        this.value = value;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public String toString() {
        return "name=" + this.name + ", " + "value=" + this.value;
    }

    public boolean equals(Object object) {
        if (object == null) {
            return false;
        } else if (this == object) {
            return true;
        } else if (!(object instanceof NameValuePair)) {
            return false;
        } else {
            NameValuePair that = (NameValuePair)object;
            return
                    Objects.equals(name, that.name) &&
                    Objects.equals(value, that.value);
        }
    }

    public int hashCode() {
        return Objects.hash(name, value);
    }
}
