package com.sanyth.auth.server.platform.qywx.service.impl;

import com.sanyth.auth.server.platform.qywx.dao.QywxAppDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxParamDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.dto.QywxMessageRedisPack;
import com.sanyth.auth.server.platform.qywx.enums.QywxMessageRedisPackType;
import com.sanyth.auth.server.platform.qywx.enums.QywxParamKey;
import com.sanyth.auth.server.platform.qywx.service.QywxClientService;
import com.sanyth.auth.server.platform.qywx.service.QywxPushMessageService;
import com.sanyth.auth.server.platform.qywx.tasks.QywxDealMessageTask;
import com.sanyth.auth.server.platform.service.PlatformActuatorService;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.LinkedTransferQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @since 2022/3/11 14:53
 */
@Service
public class QywxPushMessageServiceImpl implements QywxPushMessageService {

    private final AtomicBoolean isDeal = new AtomicBoolean();
//    private final Logger logger = LoggerFactory.getLogger(QywxMessageServiceImpl.class);
    private final QywxUserDao userDao;
    private final QywxClientService clientService;
    private final PlatformActuatorService platformActuatorService;
    private final QywxParamDao paramDao;
    private final QywxAppDao appDao;
    private final LinkedTransferQueue<QywxMessageRedisPack> messageQueue = new LinkedTransferQueue<>();

    public QywxPushMessageServiceImpl(QywxUserDao userDao, QywxClientService clientService, PlatformActuatorService platformActuatorService, QywxParamDao paramDao, QywxAppDao appDao) {
        this.userDao = userDao;
        this.clientService = clientService;
        this.platformActuatorService = platformActuatorService;
        this.paramDao = paramDao;
        this.appDao = appDao;
    }

    @Override
    public void pushMessageText(Set<String> userIds, String content) {
        if (!paramDao.findEnabledByKey(QywxParamKey.IsEnableMessage)) {
            return;
        }

        QywxMessageRedisPack pack = new QywxMessageRedisPack();
        pack.setType(QywxMessageRedisPackType.Text);
        pack.setUserIds(userIds);
        pack.setContent(content);
        messageQueue.put(pack);

        if (isDeal.compareAndSet(false, true)) {
            QywxDealMessageTask dealMessageTask = new QywxDealMessageTask(isDeal, userDao, clientService, paramDao, appDao, messageQueue);
            platformActuatorService.execute(dealMessageTask);
        }
    }

    @Override
    public void pushMessageTextCard(Set<String> userIds, String title, String description, String url) {
        if (!paramDao.findEnabledByKey(QywxParamKey.IsEnableMessage)) {
            return;
        }

        QywxMessageRedisPack pack = new QywxMessageRedisPack();
        pack.setType(QywxMessageRedisPackType.TextCard);
        pack.setUserIds(userIds);
        pack.setTitle(title);
        pack.setDescription(description);
        pack.setUrl(url);
        messageQueue.put(pack);

        if (isDeal.compareAndSet(false, true)) {
            QywxDealMessageTask dealMessageTask = new QywxDealMessageTask(isDeal, userDao, clientService, paramDao, appDao, messageQueue);
            platformActuatorService.execute(dealMessageTask);
        }
    }
}
