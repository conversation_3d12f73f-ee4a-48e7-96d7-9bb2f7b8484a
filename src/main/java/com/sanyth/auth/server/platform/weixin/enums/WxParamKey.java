package com.sanyth.auth.server.platform.weixin.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * @since 2024/12/18 11:21
 */
@Getter
public enum WxParamKey implements IEnum<String> {
    QrCodeAppId("二维码的应用ID"),
    UrlRoot("网址根"),
    ;
    private final String text;

    WxParamKey(String text) {
        this.text = text;
    }

    public String getValue() {
        return this.name();
    }
}
