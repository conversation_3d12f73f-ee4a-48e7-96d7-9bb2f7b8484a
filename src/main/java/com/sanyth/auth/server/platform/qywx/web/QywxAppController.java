package com.sanyth.auth.server.platform.qywx.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.platform.commonDeal.ApiR;
import com.sanyth.auth.server.platform.commonDeal.ReBaseApiController;
import com.sanyth.auth.server.platform.qywx.dao.QywxAppDao;
import com.sanyth.auth.server.platform.qywx.model.QywxApp;
import com.sanyth.auth.server.platform.qywx.query.QywxAppQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 企业微信应用
 *
 * @since 2022/3/2 10:06
 */
@Slf4j
@RestController
@RequestMapping("/platform/qywx/app")
public class QywxAppController extends ReBaseApiController {

    private final QywxAppDao dao;

    public QywxAppController(QywxAppDao dao) {
        this.dao = dao;
    }

    @GetMapping("list")
    public ApiR<Page<QywxApp>> list(QywxAppQuery query) {
        Page<QywxApp> page = dao.queryPage(query);
        return ApiR.ok(page);
    }

    @PostMapping("save")
    public ApiR<String> save(@RequestBody QywxApp dto) {
        QywxApp dto1 = new QywxApp();
        if ((StringUtils.isEmpty(dto.getId()))) {
            dto1.setCreated(new Date());
        } else {
            dto1 = dao.getById(dto.getId());
            dto1.setUpdated(new Date());
        }

        dto1.setName(dto.getName());
        dto1.setAgentId(dto.getAgentId());
        dto1.setSecret(dto.getSecret());
        dto1.setRedirectUrlStart(dto.getRedirectUrlStart());

        dao.saveOrUpdate(dto1);
        return ApiR.ok();
    }

    @DeleteMapping("delete")
    public ApiR<String> delete(@RequestBody QywxApp dto) {
        dao.removeById(dto.getId());
        return ApiR.ok();
    }

    @GetMapping("get")
    public ApiR<QywxApp> get(Long id) {
        QywxApp select = dao.getById(id);
        return ApiR.ok(select);
    }

}
