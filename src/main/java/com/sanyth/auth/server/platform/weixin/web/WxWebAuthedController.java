package com.sanyth.auth.server.platform.weixin.web;

import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.platform.commonDeal.RequestError;
import com.sanyth.auth.server.platform.weixin.dto.WxWebAuthCheckData;
import com.sanyth.auth.server.platform.weixin.mapper.WxUserMapper;
import com.sanyth.auth.server.platform.weixin.model.WxUser;
import com.sanyth.auth.server.util.SecurityUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * 已登录 微信公众号
 */
@Controller
@RequestMapping("/platform/wx/webAuthed")
public class WxWebAuthedController {

    private final WxUserMapper userMapper;

    public WxWebAuthedController(WxUserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @ExceptionHandler(RequestError.class)
    public String requestException(RequestError requestError) {
        return "redirect:" + WxWebAuthController.ERROR_PAGE + "?content=" + Base64.encodeBase64URLSafeString(requestError.getContent().getBytes(StandardCharsets.UTF_8));
    }

    @ExceptionHandler(Exception.class)
    public String exception(Exception e) {
//        logger.error("WxAuthController error", e);
        return "redirect:" + WxWebAuthController.ERROR_PAGE + "?content=" + Base64.encodeBase64URLSafeString(e.getMessage().getBytes(StandardCharsets.UTF_8));
    }

    @RequestMapping("bindResult")
    public String bindResult(HttpSession httpSession, ModelMap modelMap) {
        WxWebAuthCheckData authCheckData = WxWebAuthCheckData.getFromHttpSession(httpSession);
        RequestError.isTrue(authCheckData != null, "会话已重置，请退出后重新进入");

        SytPermissionAccount account = SecurityUtils.getUser();
        WxUser user = new WxUser();
        user.setAppId(authCheckData.getAppId());
        user.setOpenId(authCheckData.getOpenId());
        user.setSystemUsername(account.getUsername());
        user.setCreated(new Date());
        userMapper.insert(user);

//        httpSession.setAttribute(WxSessionKey.JsTicketInfo.toString(), new WXJsApiTicketInfo(authCheckData.getAppId(), WXJsTicketType.GongZhongHao));
//        modelMap.put("url", wxAppService.getById(authCheckData.getAppId()).getH5Url());
        modelMap.put("url", WxWebAuthController.ControllerRoot + "/index");
        modelMap.put("msg_title", "绑定成功");
        return "/platform/wei_xin/web_auth/bindResult";
    }

    @RequestMapping("test")
    @ResponseBody
    public String test() {
        return "success";
    }
}
