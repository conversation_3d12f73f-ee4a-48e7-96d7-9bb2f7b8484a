package com.sanyth.auth.server.platform.qywx.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class QywxUserExcel {

    @ExcelIgnore
    private Integer rowIndex;
    @ColumnWidth(25)
    @ExcelProperty(value = "系统用户账号" ,index = 0)
    private String sysId;
    @ColumnWidth(25)
    @ExcelProperty(value = "企业微信通讯录账号" ,index = 1)
    private String wxId;
    @ColumnWidth(20)
    @ExcelProperty(value = "说明" ,index = 2)
    private String description;
}
