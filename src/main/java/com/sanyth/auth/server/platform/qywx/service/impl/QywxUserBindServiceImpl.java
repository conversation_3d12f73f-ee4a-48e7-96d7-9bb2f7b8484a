package com.sanyth.auth.server.platform.qywx.service.impl;

import com.sanyth.auth.server.justauth.JustAuthConstants;
import com.sanyth.auth.server.model.SytJustAuthConfig;
import com.sanyth.auth.server.platform.error.PlatformRequestException;
import com.sanyth.auth.server.platform.qywx.dao.QywxAppDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxParamDao;
import com.sanyth.auth.server.platform.qywx.dao.QywxUserDao;
import com.sanyth.auth.server.platform.qywx.dto.QywxUserInfoResponse;
import com.sanyth.auth.server.platform.qywx.enums.QywxParamKey;
import com.sanyth.auth.server.platform.qywx.model.QywxApp;
import com.sanyth.auth.server.platform.qywx.model.QywxUser;
import com.sanyth.auth.server.platform.qywx.service.QywxClientService;
import com.sanyth.auth.server.platform.qywx.service.QywxUserBindService;
import com.sanyth.auth.server.platform.qywx.service.QywxUserService;
import com.sanyth.auth.server.service.SytJustAuthConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @since 2022/8/25 16:43
 */
@Service
public class QywxUserBindServiceImpl implements QywxUserBindService {

    private final QywxUserDao userDao;
    private final QywxUserService qywxUserService;
    private final QywxClientService clientService;
    private final QywxAppDao appDao;
    private final QywxParamDao paramDao;
    @Resource
    private SytJustAuthConfigService sytJustAuthConfigService;

    public QywxUserBindServiceImpl(QywxUserDao userDao, QywxUserService qywxUserService, QywxClientService clientService, QywxAppDao appDao, QywxParamDao paramDao) {
        this.userDao = userDao;
        this.qywxUserService = qywxUserService;
        this.clientService = clientService;
        this.appDao = appDao;
        this.paramDao = paramDao;
    }

    @Override
    public void bindCallback(String code, String state) throws PlatformRequestException {
        // 查询参数：由于门户系统也有QywxApp对应的绑定功能，所以当前系统用SytJustAuthConfig配置的参数
        String corpId = "";
        String secret = "";
        SytJustAuthConfig sytJustAuthConfig = sytJustAuthConfigService.getByAuthType(JustAuthConstants.AUTH_TYPE_WECHAT_ENTERPRISE);
        if(sytJustAuthConfig != null){
            corpId = sytJustAuthConfig.getClientId();
            secret = sytJustAuthConfig.getClientSecret();
        } else {
            corpId = paramDao.findValueByKey(QywxParamKey.CorpID);
            String appId = paramDao.findValueByKey(QywxParamKey.BindAppId);
            QywxApp app = appDao.getById(appId);
            secret = app.getSecret();
        }
        String accessToken = clientService.getAccessToken(corpId, secret);
        QywxUserInfoResponse userInfo = clientService.getUserInfo(accessToken, code);

        QywxUser qywxUser = new QywxUser();
        qywxUser.setWxId(userInfo.getUserId());
        qywxUser.setSysId(state);
        long count = userDao.count(qywxUser);
        PlatformRequestException.isTrue(count == 0, "已有重复绑定记录");
        qywxUserService.save(qywxUser);
    }
}
