package com.sanyth.auth.server.platform.qywx.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取access_token 返回结果
 * @since 2022/3/7 17:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QywxAccessTokenResponse extends QywxServerResponse{

    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("expires_in")
    private Integer expiresIn;
}
