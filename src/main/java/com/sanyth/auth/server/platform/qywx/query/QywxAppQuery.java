package com.sanyth.auth.server.platform.qywx.query;

import com.sanyth.auth.server.platform.commonDeal.BaseQueryAbstract;
import com.sanyth.auth.server.platform.commonDeal.BaseQueryInterface;
import com.sanyth.auth.server.platform.qywx.model.QywxApp;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2022/3/2 10:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QywxAppQuery extends BaseQueryAbstract implements BaseQueryInterface<QywxApp> {

    private QywxApp param;

}
