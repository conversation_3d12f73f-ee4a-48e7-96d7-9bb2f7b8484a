package com.sanyth.auth.server.platform.service;

public interface PlatformMessageService {
    void pushMessage(String content, String... userIds);

    /**
     * 文本卡片消息
     *
     * @param userIds     用户名集合
     * @param title       标题，不超过128个字节，超过会自动截断（支持id转译）
     * @param description 描述，不超过512个字节，超过会自动截断（支持id转译）
     * @param url         点击后跳转的链接。最长2048字节，请确保包含了协议头(http/https)
     * @since 2022/9/9 17:14
     */
    void pushMessageTextCard(String title, String description, String url, String... userIds);

    String encodeUrl(String url);
}
