package com.sanyth.auth.server.platform.commonDeal;

import lombok.Data;

/**
 * @since 2022/2/25 10:08
 * @param <T>
 */
@Data
public class ApiR<T> {

    private boolean status;
    private T data;
    private String msg;

    public void setDataAndTrue(T data) {
        this.data = data;
        this.status = true;
    }

    public ApiR() {}

    public static <T> ApiR<T> ok() {
        ApiR<T> response = new ApiR<>();
        response.setStatus(true);
        return response;
    }

    public static <T> ApiR<T> ok(T data) {
        ApiR<T> response = new ApiR<>();
        response.setDataAndTrue(data);
        return response;
    }

    public static <T> ApiR<T> failed() {
        return new ApiR<T>();
    }

    public static <T> ApiR<T> failed(String msg) {
        ApiR<T> response = new ApiR<>();
        response.setMsg(msg);
        return response;
    }

}
