package com.sanyth.auth.server.platform.commonDeal;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;

/**
 * @since 2022/2/25 10:11
 */
@RestController
public class ReBaseApiController {

    protected Log logger = LogFactory.getLog(this.getClass());

    @ExceptionHandler(RequestError.class)
    public ApiR<String> requestException(RequestError requestError) {
        return ApiR.failed(requestError.getContent());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ApiR<String> illegalArgumentException(Exception e) {
        logger.error(e.getMessage(), e);
        return ApiR.failed("缺少必需参数:"+e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public ApiR<String> exception(Exception e) {
        logger.error(e.getMessage(), e);
        return ApiR.failed(e.getMessage());
    }
}
