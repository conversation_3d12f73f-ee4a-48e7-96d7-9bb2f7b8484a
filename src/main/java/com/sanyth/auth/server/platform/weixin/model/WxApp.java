package com.sanyth.auth.server.platform.weixin.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 微信公众号 应用
 *
 * @since 2024/12/12 15:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PLATFORM_WX_APP")
public class WxApp extends Model<WxApp> {

    @TableId(type = IdType.INPUT)
    private String id;
    private String secret;
    private String name;
    private Date created;
    private Date updated;
//    private String h5Url;
    private Boolean debug;

}
