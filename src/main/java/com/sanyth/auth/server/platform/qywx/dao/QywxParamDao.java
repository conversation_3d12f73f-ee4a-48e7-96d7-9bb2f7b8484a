package com.sanyth.auth.server.platform.qywx.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.platform.commonDeal.BaseQueryInterface;
import com.sanyth.auth.server.platform.qywx.enums.QywxParamKey;
import com.sanyth.auth.server.platform.qywx.model.QywxParam;

/**
 * @since 2022/2/21 11:46
 */
public interface QywxParamDao extends IService<QywxParam> {
    Page<QywxParam> queryPage(BaseQueryInterface<QywxParam> query);

    String findValueByKey(QywxParamKey key);

    boolean findEnabledByKey(QywxParamKey key);
}
