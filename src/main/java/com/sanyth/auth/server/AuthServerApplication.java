package com.sanyth.auth.server;

import com.mzt.logapi.starter.annotation.EnableLogRecord;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Created by JIANGPING on 2020/2/4.
 */
@SpringBootApplication
@EnableLogRecord(tenant = "com.sanyth.auth", joinTransaction = true)
@EnableTransactionManagement(order = 0)
public class AuthServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(AuthServerApplication.class, args);
    }
}
