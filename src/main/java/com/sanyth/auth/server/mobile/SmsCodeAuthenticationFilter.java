package com.sanyth.auth.server.mobile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.auth.server.core.common.SpringTool;
import com.sanyth.auth.server.core.exception.ValidateException;
import com.sanyth.auth.server.core.rsa.SecJS;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description:
 * @Package: com.sanyth.auth.server.mobile
 * @ClassName: SmsCodeAuthenticationFilter
 * @Author: WDL
 * @CreateTime: 2021/4/22 15:39
 */
public class SmsCodeAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    @Resource
    SytPermissionAccountMapper sytPermissionAccountMapper;
    @Resource
    private RedisTemplate redisTemplate;

    public static final String IMOOC_FORM_MOBILE_KEY = "mobile";
    public static final String IMOOC_FORM_CODE_KEY = "vercode";

    private String mobileParameter = IMOOC_FORM_MOBILE_KEY;
    private String vercodeParameter = IMOOC_FORM_CODE_KEY;
    private boolean postOnly = true;

    public SmsCodeAuthenticationFilter() {
        super(new AntPathRequestMatcher("/phoneLogin", "POST"));
    }

    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (postOnly && !request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }

        String mobile = request.getParameter(mobileParameter);
        String vercode = request.getParameter(vercodeParameter);

        if (mobile == null) {
            mobile = "";
        }
        if (vercode == null) {
            vercode = "";
        }
        mobile = mobile.trim();
        vercode = vercode.trim();
        mobile = SecJS.decryptAndGet(mobile);
        vercode = SecJS.decryptAndGet(vercode);
        sytPermissionAccountMapper = (SytPermissionAccountMapper) SpringTool.getApplicationContext().getBean("sytPermissionAccountMapper");
        redisTemplate = (RedisTemplate) SpringTool.getApplicationContext().getBean("redisTemplate");
        if(!additionalAuthenticationChecks(mobile, vercode)){
            throw new ValidateException("验证码无效");
        }
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("TELMOBILE1", mobile);
        SytPermissionAccount account = sytPermissionAccountMapper.selectOne(wrapper);
        String humancode = "";
        if (null != account) {
            humancode = account.getHumancode();
        }else{
            throw new ValidateException("用户不存在");
        }

        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(humancode, vercode);

        // Allow subclasses to set the "details" property
        setDetails(request, authRequest);

        return this.getAuthenticationManager().authenticate(authRequest);
    }

    protected void setDetails(HttpServletRequest request, UsernamePasswordAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    public void setPostOnly(boolean postOnly) {
        this.postOnly = postOnly;
    }

    public boolean additionalAuthenticationChecks(String mobile, String vercode) {
        if(StringUtils.isEmpty(vercode)){
            return false;
        }
        //获取redis中手机键值对应的value验证码
        // 获取验证码
        String key = "PHONE_LOGIN_INFO" + "_" + mobile;
        Object str = redisTemplate.opsForHash().get(key, "code");
        String smsCode = "";
        if(str != null){
            smsCode = String.valueOf(str);
        }
        if (vercode.equalsIgnoreCase(smsCode)) {
            // 删除验证码
            redisTemplate.opsForHash().delete(key, "code");
            return true;
        }
        return false;
    }
}
