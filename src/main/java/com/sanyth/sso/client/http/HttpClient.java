package com.sanyth.sso.client.http;

import javax.net.ssl.*;
import javax.servlet.ServletException;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.SecureRandom;

public class HttpClient {

    public static String createRequest(String url, String httpMethod, String outputStr) throws Exception {
        String prefix = url.substring(0, url.indexOf(":"));
        if (prefix.toLowerCase().startsWith("https")) {
            return httpsRequest(url, httpMethod, outputStr);
        }
        return httpRequest(url, httpMethod, outputStr);
    }

    /**
     * 处理http请求
     *
     * @param requestUrl
     * @param httpMethod
     * @param outputStr
     * @return
     */
    private static String httpRequest(String requestUrl, String httpMethod, String outputStr) throws IOException {
        StringBuffer buffer = null;
        URL url = new URL(requestUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setRequestMethod(httpMethod);
        conn.connect();
        //往服务器端写内容 也就是发起http请求需要带的参数
        if (null != outputStr) {
            OutputStream os = conn.getOutputStream();
            os.write(outputStr.getBytes("utf-8"));
            os.close();
        }
        //读取服务器端返回的内容
        InputStream is = conn.getInputStream();
        InputStreamReader isr = new InputStreamReader(is, "utf-8");
        BufferedReader br = new BufferedReader(isr);
        buffer = new StringBuffer();
        String line = null;
        while ((line = br.readLine()) != null) {
            buffer.append(line);
        }
        return buffer.toString();
    }

    /**
     * 处理https请求
     *
     * @param requestUrl
     * @param httpMethod
     * @param outputStr
     * @return
     */
    private static String httpsRequest(String requestUrl, String httpMethod, String outputStr) throws Exception{
        // 通过主机认证
        HostnameVerifier hv = new HostnameVerifier() {
            @Override
            public boolean verify(String urlHostName, SSLSession session) {
                return true;
            }
        };

        StringBuffer buffer = null;
        //创建SSLContext
        SSLContext sslContext = SSLContext.getInstance("SSL");
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLSessionContext sslsc = sslContext.getServerSessionContext();
        sslsc.setSessionTimeout(0);
        //初始化
        sslContext.init(null, tm, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
        //激活主机认证
        HttpsURLConnection.setDefaultHostnameVerifier(hv);
        URL url = new URL(requestUrl);
        HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        conn.setRequestMethod(httpMethod);
        conn.connect();
        //往服务器端写内容
        if (null != outputStr) {
            OutputStream os = conn.getOutputStream();
            os.write(outputStr.getBytes("utf-8"));
            os.close();
        }
        //读取服务器端返回的内容
        InputStream is = conn.getInputStream();
        InputStreamReader isr = new InputStreamReader(is, "utf-8");
        BufferedReader br = new BufferedReader(isr);
        buffer = new StringBuffer();
        String line = null;
        while ((line = br.readLine()) != null) {
            buffer.append(line);
        }
        return buffer.toString();
    }
}
