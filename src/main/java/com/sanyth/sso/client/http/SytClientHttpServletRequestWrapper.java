package com.sanyth.sso.client.http;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

public class SytClientHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private String remoteUser;

    public SytClientHttpServletRequestWrapper(HttpServletRequest request, String remoteUser) {
        super(request);
        this.remoteUser = remoteUser;
    }

    @Override
    public String getRemoteUser() {
        return this.remoteUser;
    }
}
