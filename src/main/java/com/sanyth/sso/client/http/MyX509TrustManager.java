package com.sanyth.sso.client.http;

import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

public class MyX509TrustManager implements TrustManager, X509TrustManager {
    @Override
    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
        // TODO Auto-generated method stub
        return;
    }

    @Override
    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
        // TODO Auto-generated method stub
        return;
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return null;
    }
}
