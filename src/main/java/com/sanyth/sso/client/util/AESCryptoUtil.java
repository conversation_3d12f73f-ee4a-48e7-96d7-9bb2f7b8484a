package com.sanyth.sso.client.util;

import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

public class AESCryptoUtil {

    /*
     * 加密用的Key 可以用26个字母和数字组成 使用AES-128-CBC加密模式，key需要为16位。
     */
//    private static final String key = "hj7x89H$yuBI0456";
//    private static final String iv = "NIfb&95GUY86Gfgh";

    /**
     * @param data 明文
     * @param key  密钥，长度16
     * @param iv   偏移量，长度16
     * @return 密文
     * <AUTHOR>
     * @Description AES算法加密明文
     */
    public static String encrypt(String key, String iv, String data) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            int blockSize = cipher.getBlockSize();
            byte[] dataBytes = data.getBytes();
            int plaintextLength = dataBytes.length;

            if (plaintextLength % blockSize != 0) {
                plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
            }

            byte[] plaintext = new byte[plaintextLength];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);

            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());  // CBC模式，需要一个向量iv，可增加加密算法的强度

            cipher.init(Cipher.ENCRYPT_MODE, keyspec, ivspec);
            byte[] encrypted = cipher.doFinal(plaintext);

            return encode(encrypted).trim(); // BASE64做转码。

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @param data 密文
     * @param key  密钥，长度16
     * @param iv   偏移量，长度16
     * @return 明文
     * <AUTHOR>
     * @Description AES算法解密密文
     */
    public static String decrypt(String key, String iv, String data) throws Exception {
        try {
            byte[] encrypted1 = decode(data);//先用base64解密

            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());

            cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);

            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original);
            return originalString.trim();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 编码
     *
     * @param byteArray
     * @return
     */
    private static String encode(byte[] byteArray) {
        return new String(Base64Utils.encode(byteArray), StandardCharsets.UTF_8);
    }

    /**
     * 解码
     *
     * @param base64EncodedString
     * @return
     */
    private static byte[] decode(String base64EncodedString) throws UnsupportedEncodingException {
        return Base64Utils.decode(base64EncodedString.getBytes(StandardCharsets.UTF_8));
    }

    public static void main(String[] args) {
        try {
            String ss = encrypt("haBA4W87319iC7T0", "MzUO866VDOy9b321", "111111111111111111111111111111111111111111111111111");
            System.out.println(ss);
            String sss = decrypt("haBA4W87319iC7T0", "MzUO866VDOy9b321", ss);
            System.out.println(sss);
            ss = "hPtCjQW3BAB3PYY46qafBlvu3QUio1e2G9VVzvbhlt6%2BSitEatS28BHKP15jBc8L7nPcEDqQWf6WoGzVuPaVhw%3D%3D";
            ss = URLDecoder.decode(ss, "utf-8");
            System.out.println(decrypt("haBA4W87319iC7T0","MzUO866VDOy9b321", ss));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
