package com.sanyth.sso.client.bean;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class UserDetails {
    private String name;
    private String username;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public static void main(String[] args) {
        String s = "{\"code\":\"00000\",\"info\":{\"name\":\"李师师\",\"username\":\"10001\"}}";
        JsonObject jsonObject = (JsonObject) new JsonParser().parse(s);
        JsonObject info = jsonObject.getAsJsonObject("info");
        System.out.println(info.get("name"));
    }
}
