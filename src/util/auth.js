import Cookies from 'js-cookie'
const TokenKey = 'token'
var inFifteenMinutes = new Date(new Date().getTime() + 120 * 60 * 1000);
export function getToken() {
    return Cookies.get(TokenKey)
}

export function setToken(token) {    
    return Cookies.set(Token<PERSON>ey, token, { expires: inFifteenMinutes })
}

export function removeToken() {
    return Cookies.remove(TokenKey)
}

const Uid = 'uid'
// uid
export function getUid() {
    return Cookies.get(Uid)
}

export function setUid(uid) {    
    return Cookies.set(Uid, uid, { expires: inFifteenMinutes })
}

export function removeUid() {
    return Cookies.remove(Uid)
}