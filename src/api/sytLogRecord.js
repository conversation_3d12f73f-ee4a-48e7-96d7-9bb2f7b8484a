import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const Delete = (data) => request({
    url: baseUrl + '/sytLogRecord/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const QueryPage = (data) => request({
    url: baseUrl + '/sytLogRecord/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const Get = (data) => request({
    url: baseUrl + '/sytLogRecord/get',
    method: 'post',
    data: {
        ...data
    }
});
