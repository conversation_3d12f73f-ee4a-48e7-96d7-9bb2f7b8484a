import request from '@/router/axios';
import {baseUrl} from '@/config/env';

/**
 * 查询当前用户绑定记录
 * @param data
 */
export const getCurrentBind = (data) => request({
    url: baseUrl + '/sytJustAuthUser/getCurrentData',
    method: 'post',
    data: {
        ...data
    }
});
/**
 * 解除绑定
 * String id数据id（必填）
 * @param data
 */
export const bindUserDelete = (data) => request({
    url: baseUrl + '/sytJustAuthUser/delete',
    method: 'post',
    data: {
        ...data
    }
});



