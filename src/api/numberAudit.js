import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const getAccountAuditData = (data) => request({
    url: baseUrl + '/sytPermissionAccount/getAccountAuditData',
    method: 'post',
    data: {
        ...data
    }
});

export const queryAccountData = (data) => request({
    url: baseUrl + '/sytPermissionAccount/queryPage',
    method: 'post',
    data: {
        ...data
    }
});
