import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const getMenu = (type = 0) => request({
    url: baseUrl + '/resource/treeListByUser',
    method: 'post',
    data: {
        type
    }
});

// 菜单管理
export const getEMenu = (type = 0) => request({
    // url: baseUrl + '/user/getMenu',
    url: baseUrl + '/resource/treeList',
    method: 'post',
    data: {
        type
    }
});

// 退出登录
export const logout = () => request({
    url: baseUrl + '/logout',
    method: 'post'
})

// 切换角色
export const getRoleList = () => request({
    url: baseUrl + '/user',
    method: 'post'
})


export const switchRole = (data) => request({
    url: baseUrl + '/user/switchRole',
    method: 'post',
    data
})

// =======================


export const loginByUsername = (username, password) => request({
    url: baseUrl + '/user/login',
    method: 'post',
    meta: {
        isToken: false
    },
    data: {
        username,
        password,
        // code,
        // redomStr
    }

})

export const getUserInfo = () => request({
    url: baseUrl + '/user/getUserInfo',
    method: 'get'
});

export const refeshToken = () => request({
    url: baseUrl + '/user/refesh',
    method: 'post'
})


export const getTopMenu = () => request({
    url: baseUrl + '/user/getTopMenu',
    method: 'get'
});

export const sendLogs = (list) => request({
    url: baseUrl + '/user/logout',
    method: 'post',
    data: list
})

// 修改个人信息
export const setUserUpdateInfo = (data) => request({
    url: baseUrl + '/sytPermissionAccount/updateInfo',
    method: 'post',
    data: {
        ...data
    }
})
// 密码
export const setNewPassword = (data) => request({
    url: baseUrl + '/sytPermissionAccount/changePasswd',
    method: 'post',
    data: {
        ...data
    }
})

// 个人信息
export const sytPermissionAccountGet = () => request({
    url: baseUrl + '/sytPermissionAccount/get',
    method: 'post'
});
// oauth
export const oauthClient = (data) => request({
    url: baseUrl + '/oauthClient',
    method: 'post',
    data: data
})

// token
export const GetToken = (data) => request({
    url: baseUrl + '/oauthClient/userinfo',
    method: 'post',
    data: data
})
