import request from '@/router/axios';
import {baseUrl} from '@/config/env';

// 临时用 - 登录
export const toLogin = (data) => request({
  url: baseUrl + '/login',
  method: 'post',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
  },
  data
})

export const getInfo = () => request({
  url: baseUrl + '/sytCodeJslxb/list',
  method: 'post',
})

export const getIndexData = () => request({
  url: baseUrl + '/getIndexData',
  method: 'post',
})


