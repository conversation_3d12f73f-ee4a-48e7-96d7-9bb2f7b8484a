import request from '@/router/axios';
import {baseUrl} from '@/config/env';


// 查询
export const Query = (data, type) => request({
    url: baseUrl + '/' + type + '/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

//
export const Edit = (data, type) => request({
    url: baseUrl + '/' + type + '/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const Delete = (data, type) => request({
    url: baseUrl + '/' + type + '/delete',
    method: 'post',
    data: {
        id: data
    }
});
export const List = (data, type) => request({
    url: baseUrl + '/' + type + '/list',
    method: 'post',
    data: {
        ...data
    }
});

// 组织机构
export const GetOrg = (data) => request({
    url: baseUrl + '/sytSysOrganization/treeList',
    method: 'post',
    data: data
});
// 组织机构 返回JSONArray
export const GetOrgJsonArray = (data) => request({
    url: baseUrl + '/sytSysOrganization/treeListJsonArray',
    method: 'post',
    data: data
});

// 组织机构懒加载
export const GetLazyOrg = (data) => request({
    url: baseUrl + '/sytSysOrganization/lzayTreeList',
    method: 'post',
    data: data
});

// 组织机构懒加载 返回JSONArray
export const GetLazyOrgJsonArray = (data) => request({
    url: baseUrl + '/sytSysOrganization/lzayTreeJsonArray',
    method: 'post',
    data: data
});

// 编辑组织管理
export const EditOrg = (data) => request({
    url: baseUrl + '/sytSysOrganization/edit',
    method: 'post',
    data: {
        ...data
    }
});

// 删除
export const RemoveOrg = (data) => request({
    url: baseUrl + '/sytSysOrganization/delete',
    method: 'post',
    data: {
        id: data
    }
});
