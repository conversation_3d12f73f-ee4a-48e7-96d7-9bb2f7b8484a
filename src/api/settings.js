import request from '@/router/axios';
import {baseUrl} from '@/config/env';


// 菜单管理
export const editMenu = (data) => request({
    // url: baseUrl + '/user/getMenu',
    url: baseUrl + '/resource/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const removeMenu = (data) => request({
    // url: baseUrl + '/user/getMenu',
    url: baseUrl + '/resource/delete',
    method: 'post',
    data: {
        id: data
    }
});

// 角色管理
// left
export const GetRoleInfo = () => request({
    url: baseUrl + '/role/list',
    method: 'post',
});
export const GetEmployeeType = () => request({
    url: baseUrl + '/sytCodeJslxb/list',
    method: 'post',
});

export const GetERoleInfo = () => request({
    // url: baseUrl + '/sytPermissionRole/listManage',
    url: baseUrl + '/role/listManage',
    method: 'post'
});

export const EditRole = (data) => request({
    url: baseUrl + '/role/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const RemoveRole = (data) => request({
    url: baseUrl + '/role/delete',
    method: 'post',
    data: {
        id: data
    }
});

// 模块管理
export const GetModuleTree = () => request({
    url: baseUrl + '/resourceLink/treeList',
    method: 'post'
});

export const GetModuleParentList = () => request({
    url: baseUrl + '/resourceLink/getParentList',
    method: 'post'
});

export const GetModuleList = (data) => request({
    url: baseUrl + '/resourceLink/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const EditModuleList = (data) => request({
    url: baseUrl + '/resourceLink/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteModuleList = (data) => request({
    url: baseUrl + '/resourceLink/delete',
    method: 'post',
    data: {
        id: data
    }
});

// 认证日志
export const GetLoginLogs = (data) => request({
    url: baseUrl + '/loginLogs/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

// ================================================================

// right
export const GetRoleItemInfo = (data) => request({
    url: baseUrl + '/sytPermissionAccount/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const RemoveRoleItemInfo = (data) => request({
    url: baseUrl + '/sytPermissionAccount/delete',
    method: 'post',
    data: {
        id: data
    }
});

// 人员管理
export const GetUserList = (data) => request({
    url: baseUrl + '/sytPermissionAccount/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const EditUserList = (data) => request({
    url: baseUrl + '/sytPermissionAccount/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const DeleteUserList = (data) => request({
    url: baseUrl + '/sytPermissionAccount/delete',
    method: 'post',
    data: {
        id: data
    }
});

// 生成导出人员数据
export const GenExportData = (data) => request({
    url: baseUrl + '/sytPermissionAccount/generateExportData',
    method: 'post',
    data: {
        ...data
    }
});

// 轮播图设置
export const GetCarousel = (data) => request({
    url: baseUrl + '/sytRollBanner/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const EditCarousel = (data) => request({
    url: baseUrl + '/sytRollBanner/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const RemoveCarousel = (data) => request({
    url: baseUrl + '/sytRollBanner/delete',
    method: 'post',
    data: {
        id: data
    }
});

// 服务中心设置
export const GetSCList = () => request({
    url: baseUrl + '/sytServiceCenterCategory/list',
    method: 'post'
});

export const GetSC = (data) => request({
    url: baseUrl + '/sytServiceCenter/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

export const EditSC = (data) => request({
    url: baseUrl + '/sytServiceCenter/edit',
    method: 'post',
    data: {
        ...data
    }
});

export const RemoveSC = (data) => request({
    url: baseUrl + '/sytServiceCenter/delete',
    method: 'post',
    data: {
        id: data
    }
});

// 服务中心 appid
export const GetAppId = (data) => request({
    url: baseUrl + '/oauthClientDetails/list',
    method: 'post',
    data
});

// 授权
export const ToSQ = (data) => request({
    url: baseUrl + '/sytPermissionRole/authorize',
    method: 'post',
    data: {
        ...data
    }
});

//管理员重置密码
export const RestPassword = (data) => request({
    url: baseUrl + '/sytPermissionAccount/restPasswd',
    method: 'post',
    data: {
        humancode:data
    }
})

// 获取验证码
export const sendChangePhoneVerifyCode = (data) => request({
    url: baseUrl + '/sytPermissionAccount/sendChangePhoneVerifyCode',
    method: 'post',
    data: {
        ...data
    }
})
// 验证验证码
export const verifyChangePhoneCode = (data) => request({
    url: baseUrl + '/sytPermissionAccount/verifyChangePhoneCode',
    method: 'post',
    data: {
        ...data
    }
})

// 修改手机号
export const updatePhone = (data) => request({
    url: baseUrl + '/sytPermissionAccount/updatePhone',
    method: 'post',
    data: {
        ...data
    }
})

//访问
export const getAPPVisitNum = (data) => request({
    url: baseUrl + '/data/api/auth-data/getAPPVisitNum',
    method: 'post',
    data: {
        ...data
    }
})