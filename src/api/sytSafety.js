import request from '@/router/axios';
import {baseUrl} from '@/config/env';


// 分页查询
export const queryPage = (data) => request({
    url: baseUrl + '/SytSysSafety/queryPage',
    method: 'post',
    data: {
        ...data
    }
});

// 编辑
export const editSafety = (data) => request({
    url: baseUrl + '/SytSysSafety/edit',
    method: 'post',
    data: {
        ...data
    }
});

// 删除
export const deleteSafety = (data) => request({
    url: baseUrl + '/SytSysSafety/delete',
    method: 'post',
    data: {
        ...data
    }
});
// 查询列表
export const getList = (data) => request({
    url: baseUrl + '/SytSysSafety/list',
    method: 'post',
    data: {
        ...data
    }
});
// 根据code查询单条数据
export const getByCode = (data) => request({
    url: baseUrl + '/SytSysSafety/getByCode',
    method: 'post',
    data: {
        ...data
    }
});
// 根据多个CODE查询，返回指定数据
export const getListByCodes = (data) => request({
    url: baseUrl + '/SytSysSafety/getListByCodes',
    method: 'post',
    data: {
        ...data
    }
});