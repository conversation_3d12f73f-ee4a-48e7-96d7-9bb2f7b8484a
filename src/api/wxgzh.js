import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const appQueryList = (data) => request({
    url: baseUrl + '/platform/wx/app/page',
    method: 'post',
    data: {
        ...data
    }
});

export const editapp = (data) => request({
    url: baseUrl + '/platform/wx/app/save',
    method: 'post',
    data: {
        ...data
    }
});

export const deleteapp = (data) => request({
    url: baseUrl + '/platform/wx/app/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const userQueryList = (data) => request({
    url: baseUrl + '/platform/wx/user/page',
    method: 'post',
    data: {
        ...data
    }
});

export const edituser = (data) => request({
    url: baseUrl + '/platform/wx/user/save',
    method: 'post',
    data: {
        ...data
    }
});

export const deleteuser = (data) => request({
    url: baseUrl + '/platform/wx/user/delete',
    method: 'post',
    data: {
        ...data
    }
});

export const paramQueryList = (data) => request({
    url: baseUrl + '/platform/wx/param/page',
    method: 'post',
    data: {
        ...data
    }
});

export const editparam = (data) => request({
    url: baseUrl + '/platform/wx/param/save',
    method: 'post',
    data: {
        ...data
    }
});

export const deleteparam = (data) => request({
    url: baseUrl + '/platform/wx/param/delete',
    method: 'post',
    data: {
        ...data
    }
});
