import Vue from 'vue';
import axios from './router/axios';
import VueAxios from 'vue-axios';
import {formatDate, formatTime} from '@/util/index';
import {
    addBeginAndEndTime,
    addDateRange,
    handleTree,
    resetForm,
} from '@/util/ruoyi';
import App from './App';
import router from './router/router';
// 权限
import './permission';
// 日志
import './error';
//页面缓冲
import './cache';
import store from './store';
import {loadStyle} from './util/util';
import * as urls from '@/config/env';
import {iconfontUrl, iconfontVersion} from '@/config/env';
import Element from 'element-ui';
import i18n from './lang'; // Internationalization
import './styles/common.scss';
import basicBlock from './components/basic-block/main';
import basicContainer from './components/basic-container/main';
import './styles/assets/iconfont/iconfont.css';

Vue.prototype.formatDate = formatDate
Vue.prototype.formatTime = formatTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.addBeginAndEndTime = addBeginAndEndTime
Vue.prototype.handleTree = handleTree
Vue.use(router)
Vue.use(VueAxios, axios)
Vue.use(Element, {
    i18n: (key, value) => i18n.t(key, value)
})
Vue.use(window.AVUE, {
    i18n: (key, value) => i18n.t(key, value)
})

//注册全局容器
Vue.component('basicContainer', basicContainer)
Vue.component('basicBlock', basicBlock)
// 加载相关url地址
Object.keys(urls).forEach(key => {
    Vue.prototype[key] = urls[key];
})

// 动态加载阿里云字体库
iconfontVersion.forEach(ele => {
    loadStyle(iconfontUrl.replace('$key', ele));
})

Vue.config.productionTip = false;

//动态设置浏览器图标；
import {GetSysParamNoLogin} from "@/api/sysParam";
var schoolLogo = ''
GetSysParamNoLogin({idOrNameOrType: "titlelogo"}).then(res=>{
    const data = res.data.info;
    let img = JSON.parse(data.img);
    if (img.length > 0) {
        schoolLogo = img[0].url;
        document.querySelector('link[rel="icon"]').href = schoolLogo
    }
})

new Vue({
    router,
    store,
    i18n,
    render: h => h(App)
}).$mount('#app')
