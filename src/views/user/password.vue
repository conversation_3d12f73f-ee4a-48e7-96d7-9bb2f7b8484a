<template>
    <basic-container>
        <h3 class="pageTitle">修改密码
            <el-tag class="smallFont" type="danger" v-if="includeTip">
                <i class="el-icon-info"></i> {{ includeTip }}
            </el-tag
            >
        </h3>
        <avue-form ref="form" v-model="obj" :option="option">
            <template slot="menuForm">
                <el-row>
                    <el-col :span="24" style="text-align:left;padding-left:85px;">
                        <el-button icon="el-icon-check" type="primary" @click="handleSubmit">保 存</el-button>
                        <el-button icon="el-icon-delete" @click="handleEmpty">清 空</el-button>
                    </el-col>
                </el-row>
            </template>
        </avue-form>
    </basic-container>
</template>

<script>
import option from "@/const/user/password";
import {setNewPassword} from "@/api/user";
import Cookies from "js-cookie";
import {getByCode} from "@/api/sytSafety";

export default {
  inject: ["reload"],
  data() {
    return {
      type: "info",
      option: option,
      obj: {},
      passwordRule: {},
      includeTip: "",
    };
  },
  created() {
    this.getPasswordStrategyModify();
  },
  methods: {
    handleEmpty() {
      this.$refs.form.resetForm();
    },
    handleSubmit() {
      this.$refs.form.validate((vaild, done) => {
        if (vaild) {
          if (this.obj.newpassword !== this.obj.newpasswords) {
            this.$message.error("两次新密码不一致");
            done();
            return;
          }
          if (!!this.passwordRule) {
            // 校验正则
            if (!!this.passwordRule.passRegExp) {
              let RE_PASS = new RegExp(this.passwordRule.passRegExp);
              if (!RE_PASS.test(this.obj.newpassword)) {
                this.$message.error("密码强度不符：" + this.passwordRule.includeTip);
                done();
                return;
              }
            }
            // 密码长度限制
            if (!!this.passwordRule.passwordlength && this.obj.newpassword.length < this.passwordRule.passwordlength) {
              this.$message.error("密码长度不少于" + this.passwordRule.passwordlength);
              done();
              return;
            }
          }
          setNewPassword(this.obj).then(res => {
            console.log(res.data)
            if (res.data.code == "00000") {
              // this.$message({
              //     type: "success",
              //     message: "修改成功!"
              // });
              // window.location.href = "/logout";
              this.$confirm(
                "修改成功, 重新登录",
                "提示",
                {
                  confirmButtonText: "确定",
                  // cancelButtonText: "取消",
                  // type: "warning",
                  showCancelButton: false,
                }
              ).then(() => {
                let redirectLogout = Cookies.get('redirectLogout')
                console.log(`redirectLogout`, redirectLogout);
                if (redirectLogout) {
                  window.location.href = redirectLogout;
                } else {
                  window.location.href = "/logout";
                }
              }).catch(() => {
                window.location.href = "/logout";
              });
            } else {
              this.$message.error("修改失败!" + res.data.info);
              this.reload();
            }
          });
        }
      })
    },
    // 获取密码修改策略
    getPasswordStrategyModify() {
      let context = {
        code: "PASSWORD_STRATEGY_MODIFY"
      };
      getByCode(context).then(res => {
        console.log(`res`, res)
        if (res.data.code === "00000") {
          this.passwordRule = res.data.info;
          if (!!this.passwordRule.passRegExp) {
            this.includeTip = this.passwordRule.includeTip;
          }
        }
      });
    },
  }
};
</script>

<style lang="scss">
.smallFont {
  font-size: 12px;
  font-weight: normal;
  margin-left: 20px;
}
</style>
