<template>
    <basic-container>
        <div class="Box">
            <el-tabs  v-if="active == 0" v-model="activeName" style="width: 100%" @tab-click="handleClick">
                <el-tab-pane label="手机号" name="first">
                    <el-form ref="form" :model="form" label-width="80px">
                        <el-form-item label="原手机号:">
                            <el-input v-model="form.telmobile" size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="验证码:">
                            <el-input v-model="form.code" style="width: 50%" size="mini"></el-input>
                            <el-button :disabled="disabled" class="yanzhengma" @click="sendcode" size="mini">{{btntext}}</el-button>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="邮箱" name="second">
                    <el-form ref="form" :model="form" label-width="80px">
                        <el-form-item label="邮箱地址:">
                            <el-input v-model="form.emailadress" size="mini"></el-input>
                        </el-form-item>
                        <el-form-item label="验证码:">
                            <el-input v-model="form.code" style="width: 50%" size="mini"></el-input>
                            <el-button :disabled="disabled" class="yanzhengma" @click="sendcode" size="mini">{{btntext}}</el-button>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
            <el-form v-if="active == 1" ref="newform" :model="newform" label-width="80px">
                <el-form-item label="新手机号:">
                    <el-input v-model="newform.newtelmobile" size="mini"></el-input>
                </el-form-item>
                <el-form-item label="验证码:">
                    <el-input v-model="newform.code" style="width: 50%" size="mini"></el-input>
                    <el-button :disabled="disabled" class="yanzhengma" @click="newsendcode" size="mini">{{btntext}}</el-button>
                </el-form-item>
            </el-form>

            <el-steps :active="active" finish-status="success">
                <el-step title="验证原手机号"></el-step>
                <el-step title="设置新手机号"></el-step>
            </el-steps>
            <el-button class="btnstyle" v-if="active == 0" style="margin-top: 12px;" @click="next">下一步</el-button>
            <el-button class="btnstyle" v-if="active == 1" type="primary" style="margin-top: 12px;" @click="end">完成</el-button>
        </div>


    </basic-container>
</template>

<script>
import {sendChangePhoneVerifyCode, updatePhone, verifyChangePhoneCode} from "@/api/settings";

var wait = 60;
export default {
    inject: ["reload"],
    data() {
        return {
            active: 0,
            activeName: 'first',
            telform: {},
            btntext: '发送验证码',
            form:{
                code: null
            },
            newform:{
                code: null
            },
            disabled: false,
            isclick: true,
        };
    },
    created() {
    },
    methods: {
        time(){
            if( wait == 0){
                this.btntext = '发送验证码'
                this.disabled = false
                this.isclick = true
                wait = 60
                return
            }
            this.isclick = false
            this.disabled = true
            this.btntext = wait + '秒后重新获取'
            wait--
            setTimeout(()=>{
                this.time()
            },1000)
        },
        sendcode(){
            if(this.isclick){
                let verifyType = null
                let phoneOrEmail = null
                if(this.activeName == 'first'){
                    verifyType = "phone";
                    phoneOrEmail = this.form.telmobile
                    var re = /^1\d{10}$/;
                    let str = this.form.telmobile;
                    if(re.test(str)){
                        sendChangePhoneVerifyCode({
                            "verifyType":verifyType,
                            "phoneOrEmail":phoneOrEmail
                        }).then(res => {
                            if (res.data.code == "00000") {
                                this.$message({
                                    type: "success",
                                    message: "发送成功!"
                                });
                                wait = 60
                                this.time()
                            } else {
                                this.$message.error(res.info);
                            }
                        });
                    }else {
                        this.$message.error('手机号格式错误');
                    }
                }else{
                    verifyType = "email";
                    phoneOrEmail = this.form.emailadress
                    var re = /^([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
                    let str = this.form.emailadress;
                    if(re.test(str)){
                        sendChangePhoneVerifyCode({
                            "verifyType":verifyType,
                            "phoneOrEmail":phoneOrEmail
                        }).then(res => {
                            if (res.data.code == "00000") {
                                this.$message({
                                    type: "success",
                                    message: "发送成功!"
                                });
                                wait = 60
                                this.time()
                            } else {
                                this.$message.error(res.info);
                            }
                        });
                    }else {
                        this.$message.error('邮箱格式错误');
                    }
                }
            }

        },
        newsendcode(){
            var re = /^1\d{10}$/;
            let str = this.newform.newtelmobile;
            if(re.test(str)){
                sendChangePhoneVerifyCode({
                    "verifyType":"phone",
                    "phoneOrEmail":this.newform.newtelmobile
                }).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "发送成功!"
                        });
                        wait = 60
                        this.time()
                    } else {
                        this.$message.error(res.info);
                    }
                });
            }else {
                this.$message.error('手机号格式错误');
            }
        },
        handleClick(tab, event){
        },
        next() {
            let verifyType = null
            let phoneOrEmail = null
            if(this.activeName == 'first'){
                verifyType = "phone";
                phoneOrEmail = this.form.telmobile
                var re = /^1\d{10}$/;
                let str = this.form.telmobile;
                if(re.test(str)){
                    verifyChangePhoneCode({
                        "verifyType":verifyType,
                        "phoneOrEmail":phoneOrEmail,
                        "verifyCode":this.form.code
                    }).then(res=>{
                        if (res.data.code == "00000") {
                            wait = 0
                            this.active++
                        } else {
                            this.$message.error(res.info);
                        }
                    })
                }else {
                    this.$message.error('手机号格式错误');
                }
            }else{
                verifyType = "email";
                phoneOrEmail = this.form.emailadress
                var re = /^([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
                let str = this.form.emailadress;
                if(re.test(str)){
                    verifyChangePhoneCode({
                        "verifyType":verifyType,
                        "phoneOrEmail":phoneOrEmail,
                        "verifyCode":this.form.code
                    }).then(res=>{
                        if (res.data.code == "00000") {
                            wait = 0
                            this.active++
                        } else {
                            this.$message.error(res.info);
                        }
                    })
                }else {
                    this.$message.error('邮箱格式错误');
                }
            }
        },
        end() {
            var re = /^1\d{10}$/;
            let str = this.newform.newtelmobile;
            if(re.test(str)){
                updatePhone({
                    "verifyType":"phone",
                    "phoneOrEmail":this.newform.newtelmobile,
                    "verifyCode":this.newform.code
                }).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!"
                        });
                        this.$emit('success','success')
                    } else {
                        this.$message.error(res.info);
                    }
                });
            }else {
                this.$message.error('手机号格式错误');
            }
        }
    }
};
</script>

<style scoped lang="scss">
.smallFont {
    font-size: 12px;
    font-weight: normal;
    margin-left: 20px;
}
.yanzhengma{
    width: 40%;
    margin-left: 10%;
    vertical-align: middle;
    height: 28px;
    color: #909399;
    background-color: #F5F7FA;
}

.btnstyle{
    margin-left: 37%;
}
::v-deep .el-tabs__nav{
    width: 100%;
}
::v-deep .el-tabs__item{
    width: 50%;
    text-align: center;
}
</style>
