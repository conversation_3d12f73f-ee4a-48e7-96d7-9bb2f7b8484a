<template>
    <basic-container>
        <h3 class="pageTitle">用户管理</h3>
        <el-container>
            <el-aside width="200px" style="margin-right: 20px">
                <el-input style="margin-bottom: 16px"
                          size="mini"
                          placeholder="输入关键字进行过滤"
                          v-model="filterText"></el-input>
                <el-tree class="filter-tree"
                         :data="treeData"
                         :props="defaultProps"
                         :filter-node-method="filterNode"
                         @node-click="treeToOnLoad"
                         ref="tree"></el-tree>
            </el-aside>
            <el-main>
                <avue-crud :data="data"
                           :option="option"
                           :page.sync="page"
                           :table-loading="loading"
                           @size-change="sizeChange"
                           @current-change="currentChange"
                           @row-del="deleteItemHandle"
                           @row-save="addUserHandle"
                           @row-update="updateHandle"
                           @refresh-change="refresh"
                           @search-change="searchChange">
                    <template slot="rolename" slot-scope="scope">
                        <el-tag style="margin-right: 5px"
                                v-for="item in scope.row.rolename"
                                :key="item.value"
                                size="medium">{{ item.label }}
                        </el-tag>
                    </template>
                    <template slot="organizationnames" slot-scope="scope">
                        <template v-if="scope.row.organizationnames">
                          <span v-for="(cate, index) in scope.row.organizationnames.split(';')"
                                :key="cate + index">
                            <el-tag style="margin-right: 5px"
                                    type="danger"
                                    size="medium"
                                    v-if="index % 2 == 0">{{ cate }}</el-tag>
                            <template v-else>
                              <el-tag style="margin-right: 5px"
                                      v-for="item in cate.split(',')"
                                      :key="item"
                                      size="medium"
                                      type="success">{{ item }}</el-tag>
                            </template>
                          </span>
                        </template>
                    </template>
                    <template slot="menuLeft">
                        <el-button type="success"
                                   size="mini"
                                   icon="el-icon-upload2"
                                   @click="handleImport">导入
                        </el-button>
                    </template>
                    <template slot="menuLeft">
                        <el-button type="success"
                                   size="mini"
                                   icon="el-icon-download"
                                   @click="handleExport">导出
                        </el-button>
                    </template>
                    <template slot-scope="scope" slot="menu">
                        <el-button icon="el-icon-unlock" :size="scope.size" type="text" @click.stop="restPassword(scope.row)">重置密码</el-button>
                    </template>
                </avue-crud>
            </el-main>
        </el-container>

        <el-dialog title="用户数据导入"
                   append-to-body
                   :visible.sync="excelBox"
                   width="600px"
                   :close-on-click-modal="false">
            <avue-form :option="excelOption"
                       v-model="excelForm"
                       :upload-after="uploadAfter">
                <template slot="excelTemplate">
                    <el-button type="primary" @click="handleTemplate()">
                        点击下载<i class="el-icon-download el-icon--right"></i>
                    </el-button>
                </template>
            </avue-form>
        </el-dialog>
    </basic-container>
</template>

<script>
import {GetOrg, List} from '@/api/basetable';
import {
  DeleteUserList,
  EditUserList,
  GetEmployeeType,
  GetERoleInfo,
  GetRoleInfo,
  GetUserList,
  RestPassword,
} from '@/api/settings';
import {json2param} from '@/util/util';
import {cardid, isMobile, validateEmail} from '@/util/validate';

export default {
        data() {
            return {
                // tree
                filterText: "",
                treeData: [],
                defaultProps: {
                    label: "rolename",
                    value: "id",
                },
                curTreeId: "",
                param: {},
                // form
                data: [],
                page: {
                    currentPage: 1,
                    total: 0,
                    pageSize: 10,
                },
                roleList: [],
                employeeTypeList: [],
                orgList: [],
                loading: false,
                excelBox: false,
                excelForm: {},
                excelOption: {
                    submitBtn: false,
                    emptyBtn: false,
                    column: [
                        {
                            label: "模板上传",
                            prop: "file",
                            type: "upload",
                            drag: true,
                            loadText: "模板上传中，请稍等",
                            span: 24,
                            propsHttp: {
                                res: "info",
                            },
                            tip: "请上传 .xls,.xlsx 标准格式文件",
                            action: "/sytPermissionAccount/import",
                        },
                        {
                            label: "模板下载",
                            prop: "excelTemplate",
                            formslot: true,
                            span: 24,
                        },
                    ],
                },
				currentStateList: [],
            };
        },
        created() {
            this.getTreeList();
            this.onLoad();
            this.getRoleList();
            this.GetEmployeeType();
            // this.getOrgList();
			this.getCurrentStateList();
        },
        mounted() {
        },
        methods: {
            searchChange(param, done) {
                this.param = param;
                this.onLoad()
                done()
            },
            getTreeList() {
                GetERoleInfo().then((res) => {
                    this.treeData = res.data.info;
                    this.curTreeId = "";
                });
            },

            getRoleList() {
                GetRoleInfo().then((res) => {
                    this.roleList = res.data.info;
                });
            },
            GetEmployeeType() {
                GetEmployeeType().then((res) => {
                    this.employeeTypeList = res.data.info;
                });
            },
            getOrgList() {
                GetOrg().then((res) => {
                    this.orgList = res.data.info;
                });
            },
			getCurrentStateList() {
				List({},"sytCodeDqztb").then((res) => {
					if(res.data.code==="00000"){
						this.currentStateList = res.data.info;
					}
				});
			},
            onLoad() {
                this.loading = true;
                let queryParam = this.param;
                queryParam.roleId = this.curTreeId;
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam,
                };
                GetUserList(context).then((res) => {
                    this.loading = false;
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                    // console.log(this.data)
                    this.data.forEach((item) => {
                        item.rolename = item.role;
                        let arr = [];
                        item.role.forEach((item1) => {
                            arr.push(item1.value);
                        });
                        this.$set(item, "role", arr);
                    });
                });
            },
            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.onLoad();
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.onLoad();
            },
            refresh() {
                this.onLoad()
            },

            //tree filter
            filterNode(value, data) {
                if (!value) return true;
                return data.rolename.indexOf(value) !== -1;
            },

            treeToOnLoad(data) {
                this.curTreeId = data.id;
                this.onLoad();
            },

            // 添加
            addUserHandle(row, done, loading, type = "") {
              if (!this.validateData(row, done, loading)) {
                return false;
              }
                const context = {
                    account: row,
                    role: row.role,
                    organization: row.organization,
                };
                EditUserList(context).then((res) => {
                    if (res.data.code == "00000") {
                        done();
                        this.$message({
                            type: "success",
                            message: "操作成功!",
                        });
                        if (type == "edit") {
                            this.onLoad({roleId: this.curTreeId});
                        } else {
                            this.getTreeList();
                            this.onLoad({roleId: this.curTreeId});
                        }
                    } else {
                        loading();
                        this.$message.error(res.data.info);
                    }
                });
            },

            // 修改
            updateHandle(row, index, done, loading) {
                this.addUserHandle(row, done, loading, "edit");
            },
          validateData(row,done, loading){
            if (!isMobile(row.telmobile1)) {
              loading();
              this.$message.error("手机号不正确！");
              return false;
            }
            if (cardid(row.idcode)[0]) {
              loading();
              this.$message.error("证件号码不正确！");
              return false;
            }
            if (row.email&&!validateEmail(row.email)) {
              loading();
              this.$message.error("邮箱不正确！");
              return false;
            }
            return true;
          },

            // 删除
            deleteItemHandle(row) {
                this.$confirm(
                    "此操作将永久删除账号【" + row.humancode + "】, 是否继续?",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                ).then(() => {
                    DeleteUserList(row.id).then((res) => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!",
                            });
                            this.refresh();
                        } else {
                            this.$message.error("删除失败");
                        }
                    });
                }).catch(() => {
                });
            },

            // 导入
            handleImport() {
                this.excelBox = true;
            },

            uploadAfter(res, done, loading) {
                if (res === "success") {
                    this.excelBox = false;
                    this.refresh();
                    done();
                } else if (res === undefined) {
                    this.$message.error("上传内容格式有误！");
                    loading();
                } else {
                    this.$message.warning("请上传 .xls,.xlsx 标准格式文件");
                    loading();
                }
            },
            handleTemplate() {
                window.open(`/sytPermissionAccount/downImportTemplate`);
            },
            handleExport() {
              let queryParam = this.param;
              queryParam.roleId = this.curTreeId;
              /*const context = {
                page: this.page.currentPage,
                pageSize: this.page.pageSize,
                queryParam,
              };*/
              // GenExportData(context).then((res) => {
              //   if (res.data.code == "00000") {
              let paramStr = json2param(queryParam);
              window.open(`/sytPermissionAccount/export?` + paramStr);
                // }
              // });
            },
            restPassword(row){
                RestPassword(row.humancode).then((res) => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "操作成功!",
                        });
                        this.getTreeList();
                        this.onLoad();
                    } else {
                        this.$message.error("操作失败");
                    }
                });

            }
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            },
        },
        computed: {
            option() {

                return {
                    // dialogWidth: 700,
                    size: "mini",
                    index: true,
                    border: true,
                    // tabs: true,
                    stripe: true,
                    dialogClickModal: false,
                    menuWidth: 230,
                    align: "center",
                    // span: 24,
                    // menuType: "button",
                    editBtnText: "修改",
                    delBtnText: "删除",
                    refreshBtn: true,
                    columnBtn: true,
                    searchBtn: true,
                    searchShow: true,
                    column: [
                        {
                            label: "学号/工号",
                            width: 120,
                            prop: "humancode",
                            addDisplay: false,
                            editDisplay: false,
                            search: true,
                        },
                        {
                            label: "姓名",
                            prop: "humanname",
                            width: 120,
                            addDisplay: false,
                            editDisplay: false,
                            search: true,
                        },
                        {
                            label: "性别",
                            prop: "sex",
                            type: "select",
                            width: 60,
                            align: "center",
                            addDisplay: false,
                            editDisplay: false,
                            search: true,
                            dicData: [
                                {label: "男", value: "male"},
                                {label: "女", value: "female"},
                                {label: "请选择", value: ""},
                            ],
                        },
                        {
                            label: "角色",
                            prop: "rolename",
                            align: "center",
                            addDisplay: false,
                            editDisplay: false,
                            slot: true,
                        },
                        {
                            label: "组织机构",
                            prop: "organizationnames",
                            addDisplay: false,
                            editDisplay: false,
                            slot: true,
                            align: "center",
                            type: "tree",
                            dicMethod:"post",
                            dicUrl:"/sytSysOrganization/treeListJsonArray",
                            // dicData: this.orgList,
                            props: {
                                value: "id",
                            },
                            span: 12,
                            search: true,
                        },
                        {
                            label: "手机号码",
                            width: 120,
                            prop: "telmobile1",
                            addDisplay: false,
                            editDisplay: false,
                            search: true,
                        },
                        {
                            label: "证件号码",
                            prop: "idcode",
                            span: 12,
                            addDisplay: false,
                            editDisplay: false,
                            search: true,
                        },
                        {
                            label: "人员类别",
                            prop: "employeeType",
                            span: 12,
                            addDisplay: false,
                            editDisplay: false,
                        },
                    ],
                    group: [
                        {
                            // label: "基本信息",
                            prop: "group1",
                            column: [
                                {
                                    label: "学号/工号",
                                    prop: "humancode",
                                    rules: [{required: true, message: "学号或工号不能为空"}],
                                },
                                {
                                    label: "姓名",
                                    prop: "humanname",
                                    rules: [{required: true, message: "姓名不能为空"}],
                                },
                                {
                                    label: "性别",
                                    prop: "sex",
                                    type: "select",
                                    dicData: [
                                        {value: "male", label: "男"},
                                        {value: "female", label: "女"},
                                    ],
                                },
                                // {
                                //   label: "人员工号",
                                //   prop: "humannumber",
                                //   placeholder: "为空时由系统自动生成"
                                // },
                                {
                                    label: "手机号码",
                                    prop: "telmobile1",
                                    span: 12,
                                    rules: [{required: true, message: "手机号码不能为空"}],
                                },
                                /*{
                                    label: "密码",
                                    prop: "humanpassword",
                                    rules: [{validator: password.validatePass, trigger: 'blur' }],
                                },*/
                                {
                                    label: "角色设置",
                                    prop: "role",
                                    type: "select",
                                    span: 12,
                                    placeholder: "请选择 角色设置-可多选",
                                    drag: true,
                                    multiple: true,
                                    dicData: this.roleList,
                                    rules: [
                                        {
                                            required: true,
                                            message: "角色设置不能为空",
                                        },
                                    ],
                                    props: {
                                        label: "rolename",
                                        value: "id",
                                    },
                                },
                                {
                                    label: "组织机构",
                                    prop: "organization",
                                    type: "tree",
                                    multiple: true,
                                    checkStrictly:true,
                                    dicMethod:"post",
                                    dicUrl:"/sytSysOrganization/treeListJsonArray",
                                    // dicData: this.orgList,
                                    props: {
                                        value: "id",
                                    },
                                    span: 12,
                                    rules: [{required: true, message: "组织机构不能为空"}],
                                },
                                {
                                    label: "人员类别",
                                    prop: "employeeType",
                                    type: "select",
                                    span: 12,
                                    search: true,
                                    placeholder: "请选择人员类别",
                                    dicData: this.employeeTypeList,
                                    props: {
                                        label: "name",
                                        value: "name",
                                    },
                                    rules: [{required: true, message: "人员类别不能为空"}],
                                },
                                {
                                    label: "人员状态",
                                    prop: "validflag",
                                    type: "select",
                                    search:true,
                                    dicData: [
                                        {value: 0, label: "在校"},
                                        {value: 1, label: "离校"},
                                    ],
                                    rules: [{required: true, message: "人员状态不能为空"}],
                                },{
                                    label: "证件类型",
                                    prop: "idtype",
                                    type: "select",
                                    dicData: [
                                        {value: "居民身份证", label: "居民身份证"},
                                        {value: "士兵证", label: "士兵证"},
                                        {value: "军官证", label: "军官证"},
                                        {value: "警官证", label: "警官证"},
                                        {value: "护照", label: "护照"},
                                        {value: "其他", label: "其他"},
                                    ],
                                    span: 12,
                                    rules: [{required: true, message: "证件类型不能为空"}],
                                },
                                {
                                    label: "证件号码",
                                    prop: "idcode",
                                    span: 12,
                                    rules: [{required: true, message: "证件号码不能为空"}],
                                },
                                {
                                    label: "开始时间",
                                    prop: "validfromdate",
                                    type: "date",
                                    format: "yyyy-MM-dd",
                                    valueFormat: "yyyy-MM-dd",
                                    rules: [{required: true, message: "开始时间不能为空"}],
                                },
                                {
                                    label: "结束时间",
                                    prop: "validtodate",
                                    type: "date",
                                    format: "yyyy-MM-dd",
                                    valueFormat: "yyyy-MM-dd",
                                    rules: [{required: true, message: "结束时间不能为空"}],
                                },
                            ],
                        },
                        {
                            // label: "详细信息",
                            prop: "group2",
                            span: 12,
                            column: [

                                {
                                    label: "所属职务",
                                    prop: "dutyid",
                                },

                                {
                                    label: "出生日期",
                                    prop: "birthday",
                                    type: "date",
                                    format: "yyyy-MM-dd",
                                    valueFormat: "yyyy-MM-dd",
                                },
                                {
                                    label: "微信号",
                                    prop: "telmobile2",
                                },
                                {
                                    label: "电子邮箱",
                                    prop: "email",
                                    span: 12,
                                },
                                {
                                    label: "办公电话",
                                    prop: "teloffice",
                                },
                                {
                                    label: "家庭电话",
                                    prop: "telhome",
                                },
                                {
                                    label: "邮政编码",
                                    prop: "postalcode",
                                },

                                {
                                    label: "人员排序",
                                    prop: "displayorder",
                                    type: "number",
                                    // value: 1000
                                },
								{
									label: "当前状态",
									prop: "currentState",
									type: "select",
									span: 12,
									placeholder: "请选择当前状态",
									dicData: this.currentStateList,
									props: {
										label: "name",
										value: "code",
									},
								},
                                {
                                    label: "联系地址",
                                    prop: "address",
                                    // type: "textarea",
                                    span: 24,
                                },
                            ],
                        },
                    ],
                };
            },
        },
    };
</script>

<style scoped>
    .basic-container {
        height: 100%;
    }

    .basic-container :deep(.el-card) {
        overflow: auto;
        height: 101%;
    }
</style>
