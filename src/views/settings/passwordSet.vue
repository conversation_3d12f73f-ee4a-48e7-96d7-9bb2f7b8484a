<template>
  <basic-container>
    <h3 class="pageTitle">
      密码生成策略
      <el-tag class="smallFont">
        <i class="el-icon-info"></i> 此处统一设置新用户的初始密码</el-tag
      >
    </h3>
    <avue-form :option="option" v-model="formData" @submit="handleSubmit">
      <template slot="group1Header">
        <h4>
          密码安全策略
          <el-tag class="smallFont">
            <i class="el-icon-info"></i>
            此处设置用户密码安全策略</el-tag
          >
        </h4>
      </template>
      <template slot="group11Header" style="line-height: 20px;">
        <h6 style="padding-left: 30px;">
          账户锁定相关设置
          <el-tag class="smallFont" size="mini" >
            <i class="el-icon-info"></i>
            此处可进行账户锁定相关设置</el-tag
          >
        </h6>
      </template>
      <template slot="group2Header">
        <h4>
          密码修改策略
          <el-tag class="smallFont">
            <i class="el-icon-info"></i>
            此处设置用户在修改新密码时限制新密码的强度</el-tag
          >
        </h4>
      </template>
    </avue-form>
  </basic-container>
</template>

<script>
import {editSafety, getListByCodes} from "@/api/sytSafety";

var DIC = {
  YXQ: [
    {
      label: "30天",
      value: "30",
    },
    {
      label: "60天",
      value: "60",
    },
    {
      label: "90天",
      value: "90",
    },
    {
      label: "120天",
      value: "120",
    },
    {
      label: "长期有效",
      value: "longTime",
    },
  ],
  SCCL: [
    {
      label: "取身份证件号后6位",
      value: "idcard",
    },
    {
      label: "取账号名",
      value: "loginnumber",
    },
    {
      label: "指定密码",
      value: "manageset",
    },
  ],
  INCLUDE: [
    {
      label: "是",
      value: "是",
    },
    {
      label: "否",
      value: "否",
    },
  ],
};
var PASSWORD_STRATEGY_ARR=["PASSWORD_STRATEGY_GENERATE", "PASSWORD_STRATEGY_SAFETY", "PASSWORD_STRATEGY_MODIFY"];
export default {
  data() {
    return {
      formData: {},
      code: PASSWORD_STRATEGY_ARR.join(","),
      option: {
        submitText: "提交",
        emptyBtn:false, // 隐藏清空按钮
        labelWidth: 180,
        column: [
          {
            label: "密码生成策略",
            prop: "sccl",
            span: 12,
            row: true,
            type: "radio",
            dicData: DIC.SCCL,
            value: "idcard", // 默认身份证后6位
            // rules: [{
            //   required: true,
            //   message: "请指定密码生成策略",
            //   trigger: "blur"
            // }],
          },
          {
            label: "指定密码",
            span: 12,
            row: true,
            prop: "setnumber",
            display: true,
            rules: [{
              required: true,
              message: "请输入指定密码",
              trigger: "blur"
            }],
          },
        ],
        group: [
          {
            label: "密码安全策略",
            collapse: true,
            prop: "group1",
            column: [
              {
                label: "登录超时限制(分钟)",
                prop: "overtime",
                type: "number",
                span: 12,
                row: true,
                value: 20,
                minRows: 10,
              },
              {
                label: "用户密码有效期",
                prop: "mmyxq",
                type: "select",
                dicData: DIC.YXQ,
                span: 12,
                row: true,
                value: 120, // 默认值
              },
              /*{
                label: "登录失败锁定账户限制",
                prop: "lockaccount",
                type: "select",
                span: 12,
                row: true,
                value: "关闭", // 默认值
                dicData: [
                  {
                    label: "开启",
                    value: "开启",
                  },
                  {
                    label: "关闭",
                    value: "关闭",
                  },
                ],
              },
              {
                label: "连续登录失败时长(分钟)",
                prop: "loginfailtime",
                type: "number",
                span: 12,
                row: true,
                value: 20,
                minRows: 1,
                display: true,
              },
              {
                label: "密码输错次数限制",
                prop: "number",
                type: "number",
                span: 12,
                row: true,
                value: 3,
                minRows: 1,
                display: true,
              },
              {
                label: "锁定时长(分钟)",
                prop: "locktime",
                type: "number",
                span: 12,
                row: true,
                value: 20,
                minRows: 1,
                display: true,
              },
              {
                label: "锁定类型",
                prop: "locktype",
                type: "select",
                span: 12,
                row: true,
                display: true,
                value: "usercode", // 默认值
                dicData: [
                  {
                    label: "账号",
                    value: "usercode",
                  },
                  {
                    label: "IP",
                    value: "ip",
                  },
                ],
              },*/
            ],
          },
          {
            label: "锁定账户相关设置",
            collapse: false,
            prop: "group11",
            column: [
              {
                label: "登录失败锁定账户限制",
                prop: "lockaccount",
                type: "select",
                span: 12,
                row: true,
                value: "关闭", // 默认值
                dicData: [
                  {
                    label: "开启",
                    value: "开启",
                  },
                  {
                    label: "关闭",
                    value: "关闭",
                  },
                ],
              },
              {
                label: "连续登录失败时长(分钟)",
                prop: "loginfailtime",
                type: "number",
                span: 12,
                row: true,
                value: 20,
                minRows: 1,
                display: true,
              },
              {
                label: "密码输错次数限制",
                prop: "number",
                type: "number",
                span: 12,
                row: true,
                value: 3,
                minRows: 1,
                display: true,
              },
              {
                label: "锁定时长(分钟)",
                prop: "locktime",
                type: "number",
                span: 12,
                row: true,
                value: 20,
                minRows: 1,
                display: true,
              },
              {
                label: "锁定类型",
                prop: "locktype",
                type: "select",
                span: 12,
                row: true,
                display: true,
                value: "usercode", // 默认值
                dicData: [
                  {
                    label: "账号",
                    value: "usercode",
                  },
                  {
                    label: "IP",
                    value: "ip",
                  },
                ],
              },
            ],
          },
          {
            label: "密码修改策略",
            arrow: false,
            prop: "group2",
            labelWidth: 160,
            column: [
              {
                label: "密码最小长度",
                prop: "passwordlength",
                type: "number",
                span: 12,
                row: true,
                value: 8,
                minRows: 8,
              },
              {
                label: "是否必须包含数字",
                prop: "sfsz",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
                value: "是", // 默认值
              },
              {
                label: "是否必须包含小写字母",
                prop: "sfxxzm",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
                value: "是", // 默认值
              },
              {
                label: "是否必须包含大写字母",
                prop: "sfdxzm",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
                value: "是", // 默认值
              },
              {
                label: "是否必须包含符号",
                prop: "sffh",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
                value: "是", // 默认值
              },
              {
                label: "是否允许包含用户名",
                prop: "sfyhm",
                span: 12,
                row: true,
                type: "radio",
                dicData: DIC.INCLUDE,
                value: "是", // 默认值
              },
            ],
          },
        ],
      },
    };
  },
  watch: {
    "formData.sccl": {
      handler(val) {
        var setnumber = this.findObject(this.option.column, "setnumber");
        if (val === "manageset") {
          setnumber.display = true;
        } else {
          setnumber.display = false;
          this.formData.setnumber = "";
        }
      },
      immediate: true,
    },
    "formData.lockaccount": {
      handler(val) {
        let groupColumn = this.option.group[1].column;
        let loginfailtime = this.findObject(groupColumn, "loginfailtime");
        let number = this.findObject(groupColumn, "number");
        let locktime = this.findObject(groupColumn, "locktime");
        let locktype = this.findObject(groupColumn, "locktype");
        if (val === "开启") {
          loginfailtime.display = true;
          number.display = true;
          locktime.display = true;
          locktype.display = true;
        } else {
          loginfailtime.display = false;
          number.display = false;
          locktime.display = false;
          locktype.display = false;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.option1 = Object.assign(this.deepClone(this.option), {
      card: true,
    });
    this.getBaseData();
  },
  methods: {
    handleSubmit(form, done, loading) {
      // this.$message.success(JSON.stringify(this.formData));
      let safetyObj = {
        overtime: this.formData.overtime,
        mmyxq: this.formData.mmyxq,
        lockaccount: this.formData.lockaccount,
      };
      if("开启" === this.formData.lockaccount){
        safetyObj.loginfailtime = this.formData.loginfailtime;
        safetyObj.number = this.formData.number;
        safetyObj.locktime = this.formData.locktime;
        safetyObj.locktype = this.formData.locktype;
      }
      const context = {
        "PASSWORD_STRATEGY_GENERATE": {
          sccl: this.formData.sccl,
          setnumber: this.formData.sccl!=="manageset" ? "" : this.formData.setnumber,
        },
        "PASSWORD_STRATEGY_SAFETY": safetyObj,
        "PASSWORD_STRATEGY_MODIFY": {
          passwordlength: this.formData.passwordlength,
          sfsz: this.formData.sfsz,
          sfxxzm: this.formData.sfxxzm,
          sfdxzm: this.formData.sfdxzm,
          sffh: this.formData.sffh,
          sfyhm: this.formData.sfyhm,
        },
      };
      this.$confirm("确定要修改相关密码策略?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        editSafety(context).then(res => {
          // console.log(`res`,res)
          if(res.data.code === "00000"){
            done();
            this.getBaseData();
          } else {
            this.$message.error("操作失败");
            loading();
          }
        });
      }).catch(() => {
      });
    },
    getBaseData() {
      const context = {
        code: this.code,
      };
      getListByCodes(context).then(res => {
        // console.log(`res`,res)
        if(res.data.code === "00000"){
          const data = res.data.info;
          this.formData = data;
        }
      });
    },
  },
};
</script>

<style scoped>
.basic-container >>> .avue-group__header {
  margin-bottom: 10px;
  border-bottom: none;
}
.basic-container >>> .avue-group__title {
  color: #303133;
  font-weight: bold;
}
.smallFont {
  font-size: 12px;
  font-weight: normal;
  margin-left: 20px;
}
</style>