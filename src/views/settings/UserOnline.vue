<template>
  <div>
    <basic-container>
      <avue-crud :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 @row-del="deleteHandle"
                 @refresh-change="refresh"
                 @search-change="searchChange">
        <template slot-scope="scope" slot="menu">
          <el-button v-if="sessionId != scope.row.tokenId" icon="el-icon-unlock" :size="scope.size" type="text" @click.stop="kickOut(scope.row)">踢出</el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import Cookies from 'js-cookie';
import {mapGetters} from 'vuex';
import {KickOut, QueryPage} from '@/api/userOnline';
import {formatDate} from '@/util';

export default {
  inject: ['reload'],
  data() {
    return {
      sessionId:'',
      uploadObj: {},
      tableLoading: false,
      pageList: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10,
      },
      data: [],
      optionDetail: {
        index: true,
        size: 'mini',
        dialogWidth: 580,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        // menu: false,
        menuWidth: 150,
        align: 'center',
        delBtnText: '踢出',
        addBtn:false,
        editBtn:false,
        delBtn:false,
        border: true,
        column: [
          {
            label: '会话编号',
            prop: 'tokenId',
            span: 24,
          },
          {
            label: '用户账号',
            prop: 'userName',
            span: 24,
            search: true,
          },
          {
            label: '登录IP地址',
            prop: 'ipaddr',
            span: 24,
          },
          /*{
            label: '登录地址',
            prop: 'loginLocation',
            span: 24,
          },*/
          {
            label: '浏览器类型',
            prop: 'browser',
            span: 24,
          },
          {
            label: '操作系统',
            prop: 'os',
            span: 24,
          },
          {
            label: '登录时间',
            prop: 'loginTime',
            span: 24,
            format: "yyyy-MM-dd HH:mm:ss"
            ,formatter:(val,value,label)=>{
              return formatDate(value);
            }
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  created() {
    this.onLoad(this.pageParam());
    this.sessionId = Cookies.get('sessionId');
  },
  methods: {
    // 删除
    kickOut(row) {
      this.$confirm('是否踢出当前终端?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        KickOut({'token': row.tokenId}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: 'success', message: '踢出成功'});
            this.onLoad(this.pageParam());
          }
        });
      }).catch(() => {
        console.log('已取消删除操作');
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true;
      QueryPage(param).then(res => {
        this.tableLoading = false;
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
        this.data = data.records;
      });
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam());
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam());
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {},
      };
    },
    searchChange(param, done) {
      var pageParam = this.pageParam();
      pageParam.queryParam = param;
      this.onLoad(pageParam);
      done();
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
  },
};
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

.avue-view {
  height: 100%;
}
</style>
