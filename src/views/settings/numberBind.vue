<template>
    <basic-container>
        <h3>账号绑定</h3>
        <el-alert style="margin:10px 0;" title="系统已集成多家第三方平台登录认证，在使用第三方帐号登录时，可在这里进行帐号的绑定/解绑操作。" type="info"
                  show-icon></el-alert>
        <div class="user-setting__main" v-if="initElRows">
            <el-row v-for="row in initElRows" :key="row">
                <el-col :span="8">
                    <div class="user-setting__meta">
                        <p class="title">
                            <img :src="row.icon" alt="">
                            {{row.title}}
                        </p>
                    </div>
                </el-col>
<!--                <el-col :span="8">
                    <div class="user-setting__meta">
                        <p class="subtitle">{{row.sfBind==='是'?'已绑定':'未绑定'}}</p>
                    </div>
                </el-col>-->
                <el-col :span="8">
                    <div class="user-setting__meta">
                        <el-button type="text" @click.native="detailClick(row.authType)">已绑定用户列表</el-button>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="user-setting__menu">
<!--                        <el-button v-if="row.sfBind==='是'" plain type="warning" size="mini"
                                   @click="bindUserDelete(row)">解绑
                        </el-button>-->
                        <!-- <el-button v-else plain type="primary" size="mini" @click="bindUserType(row)">绑定</el-button> -->
                        <el-button plain type="primary" size="mini" @click="bindUserType(row)">绑定</el-button>
                    </div>
                </el-col>
            </el-row>
        </div>
        <el-dialog modal-append-to-body
                   append-to-body
                   :title="showTitle+'绑定'"
                   top="50px"
                   :visible.sync="showDialog"
                   width="60%">
            <iframe :src="thirdUrl" frameborder="0" width="100%" height="600px"></iframe>

            <div slot="footer"
                 class="screenshot__menu">
                <el-button type="primary"
                           icon="el-icon-check"
                           @click="showDialog=false">取消
                </el-button>
            </div>
        </el-dialog>

        <el-dialog title="开始绑定"
                   :visible.sync="dialog"
                   :modal-append-to-body="false"
                   :modal="false"
                   :destroy-on-close="true"
                   @close="dialog=false"
                   width="20%">
            <!--            <el-alert-->
            <!--                    :title="messageAlert"-->
            <!--                    type="info">-->
            <!--            </el-alert>-->
            <div id="wx_reg" style="text-align: center;"></div>
        </el-dialog>
        <el-dialog title="用户列表"
                   append-to-body
                   :visible.sync="dialogStatus"
                   :close-on-click-modal="false">
            <avue-crud :data="data2"
                       :option="option2"
                       :page.sync="page2"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       @row-del="deleteHandle"
            >

            </avue-crud>
        </el-dialog>
    </basic-container>
</template>

<script>
import {bindUserDelete} from "@/api/numberBind";
import {DeleteUserBind, ListUserBind} from "@/views/platform/qywx/qywxApi";
import {getBindInfo} from "@/api/justAuthConfig";

export default {
        data() {
            return {
                setInitElRows: [
                    {
                        title: '企业微信',
                        authType: 'WECHAT_ENTERPRISE',
                        icon: require("../../styles/image/qywx.png"),
                        sfBind: '否',
                    },
                    // {
                    //     title: '微信',
                    //     authType: 'WECHAT',
                    //     icon: require("../../styles/image/weixin.png"),
                    //     sfBind: '否',
                    // },
                    // {
                    //     title: '钉钉',
                    //     authType: 'DINGTALK',
                    //     icon: require("../../styles/image/ding.png"),
                    //     sfBind: '否',
                    // },
                    // {
                    //     title: '腾讯QQ',
                    //     authType:'QQ',
                    //     icon:require("../../styles/image/qq.png"),
                    // sfBind:'否',
                    // },
                    // {
                    //     title: '易班',
                    //     authType:'',
                    //     icon:require("../../styles/image/yiban.png"),
                    // sfBind:'否',
                    // },
                    // {
                    //     title: '新浪微博',
                    //     authType:'WEIBO',
                    //     icon:require("../../styles/image/weibo.png"),
                    // sfBind:'否',
                    // },
                ],
                initElRows: [],
                showDialog: false,
                showTitle: '',
                thirdUrl: '',
                dialog: false,
                dialogStatus: false,
                page2: {
                    currentPage: 1,
                    total: 0,
                    pageSize: 10,
                    // "pageSizes": [ 5, 10, 20, 30, 40, 50, 100 ],
                },
                data2: [
                ],
                option2:{
                    index: true,
                    size: "mini",
                    dialogWidth: 580,
                    dialogClickModal: false,
                    searchShow: false,
                    align: "center",
                    delBtnText: '解绑',
                    addBtn:false,      //是否显示添加按钮
                    editBtn:false,      //是否显示添加按钮
                    refreshBtn: false, //表格上面小的 刷新按钮
                    columnBtn: false,  //表格上面小的 列表按钮
                    searchBtn: false,  //表格上面小的 搜索按钮
                    border: true,
                    menu: true,        //是否显示操作栏
                    column: [
                        {
                            label: 'wxId',
                            prop: "wxId",
                            search: true,
                            rules: [{required: true, trigger: blur}]
                        },
                        {
                            label: '说明',
                            prop: "description",
                            type: "textarea",
                            span: 24,
                        },
                        {
                            label: '创建时间',
                            prop: "created",
                            readonly: true,
                            addDisplay: false,
                            editDisplay: false,
                            editDisabled: true,
                            addDisabled: true,
                            type: "date",
                            format: "yyyy-MM-dd HH:mm:ss",
                        },
                        {
                            label: '更新时间',
                            prop: "updated",
                            readonly: true,
                            addDisplay: false,
                            editDisplay: false,
                            editDisabled: true,
                            addDisabled: true,
                            type: "date",
                            format: "yyyy-MM-dd HH:mm:ss",
                        },
                    ]
                }
            }
        },
        created() {
            this.getCurrentBind()
        },
        methods: {
            getCurrentBind() {
                this.initElRows = this.setInitElRows;
                /*getCurrentBind().then(res => {
                    this.initElRows = [];
                    if (res.data.code === '00000') {
                        this.setInitElRows.forEach(e => {
                            res.data.info.forEach(row => {
                                if (e.authType === row.authType) {
                                    e.sfBind = row.sfBind
                                    e.id = row.id
                                    e.userId = row.userId
                                }
                            })
                        })
                    }
                    this.initElRows = this.setInitElRows;
                })*/
            },
            // bindUserType(){
            //         this.$confirm('请返回登录页，在其他登录方式中选择对应的第三方应用图标链接进行绑定操作。', '绑定提示', {
            //         confirmButtonText: '确定',
            //         cancelButtonText: '取消',
            //         type: 'warning'
            //         }).then(() => {
            //             window.location.href = "/logout"
            //         }).catch(() => {
            //
            //         });
            // },
            bindUserDelete(row) {
                bindUserDelete({"id": row.id}).then(res => {
                    if (res.data.code === '00000') {
                        this.$message({type: "success", message: "操作成功"});
                        this.getCurrentBind()
                    } else {
                        this.$message.error(res.data.info);
                    }
                })
            },
            bindUserType(row) {
                this.dialog = true;
                getBindInfo({"oauthType": row.authType}).then(res => {
                    let resData = res.data;
                    if(resData.code === '00000'){
                        let data1 = resData.info;
                        new window.WwLogin({
                            "id": "wx_reg",
                            "appid": data1.appId,
                            "agentid": data1.agentId,
                            "redirect_uri": data1.redirectUri,
                            "state": data1.humancode,
                            "self_redirect": true
                        });
                    } else {
                        this.$message.error(resData.info);
                        this.dialog = false;
                    }
                })
            },
            detailClick () {
                this.dialogStatus = true;
                this.page2.currentPage=1;
                this.page2.pageSize=10;
                this.onLoadUserData();
            },
            sizeChange(val) {
                this.page2.currentPage = 1;
                this.page2.pageSize = val;
                this.onLoadUserData();
            },
            currentChange(val) {
                this.page2.currentPage = val;
                this.onLoadUserData();
            },
            onLoadUserData(){
                const param={
                    page: this.page2.currentPage,
                    pageSize: this.page2.pageSize,
                    queryParam: {

                    }
                }
                ListUserBind(param).then((res)=>{
                    // console.log(res);
                    this.loading = false;
                    const data = res.data.data;
                    this.page2.currentPage = data.current;
                    this.page2.total = data.total;
                    this.page2.pageSize = data.size;
                    this.data2 = data.records;
                })
            },
            deleteHandle(row) {
                this.$confirm("此操作将解绑此账号, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    DeleteUserBind({id: row.id}).then(() => {
                        this.onLoadUserData();
                    })
                }).catch(() => {
                    // console.log('已取消操作')
                });
            },
        }

    };
</script>

<style lang="scss" scoped>
    h3 {
        margin: 0;
        font-size: 16px;
        margin-bottom: 0px;
    }

    .el-alert--info.is-light{
        background: #ECF5FF;
        color: #7FBEFF;
    }

    .title img {
        width: 30px;
        vertical-align: middle;
        margin-right: 5px;
    }

    .user-setting__menu {
        text-align: right;
    }

    .el-row {
        border-bottom: 1px solid #f1f1f1;
        margin-bottom: 10px;
    }

    .user-setting {
        &__main {
            // padding: 8px 40px;
            padding: 8px 0px;
            padding-left: 0;
        }

        &__item {
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            // display: -webkit-box;
            // display: -ms-flexbox;
            // display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        &__title {
            font-size: 20px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 28px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        &__meta {
            // flex: 1;
            line-height: 40px;

            .title {
                // margin: 6px 0;
                margin: 0;
                font-weight: 700;
                font-size: 14px;
            }

            .subtitle {
                // margin: 8px 0;
                margin: 0;
                font-size: 14px;
                color: #888;
                text-align: center;
            }
        }
    }
    :deep(.avue-crud__menu) {
        display: none;
    }
    :deep(.el-dialog__header) {
        border-bottom: 1px solid #f0f0f0;
    }
    :deep(.el-dialog__body) {
        padding: 20px 20px 5px 10px;
    }
</style>
