<template>
    <basic-container>
        <h3 class="pageTitle">账号审计</h3>
        <avue-crud :data="data" :option="option" :cell-style="cellStyle" >

          <template slot-scope="scope" slot="menu">
            <el-button :size="scope.size" type="text" @click.native="detailClick(scope.row)">查看列表</el-button>
          </template>

        </avue-crud>

      <el-dialog title="用户列表"
                 append-to-body
                 :visible.sync="dialogStatus"
                 :close-on-click-modal="false">
        <avue-crud :data="data2"
                   :option="option2"
                   :page.sync="page2"
                   @size-change="sizeChange"
                   @current-change="currentChange"
        >
          <template slot="rolename" slot-scope="scope">
            <el-tag style="margin-right: 5px"
                    v-for="item in scope.row.rolename"
                    :key="item.value"
                    size="medium">{{ item.label }}
            </el-tag>
          </template>
          <template slot="organizationnames" slot-scope="scope">
            <template v-if="scope.row.organizationnames">
                          <span v-for="(cate, index) in scope.row.organizationnames.split(';')"
                                :key="cate + index">
                            <el-tag style="margin-right: 5px"
                                    type="danger"
                                    size="medium"
                                    v-if="index % 2 == 0">{{ cate }}</el-tag>
                            <template v-else>
                              <el-tag style="margin-right: 5px"
                                      v-for="item in cate.split(',')"
                                      :key="item"
                                      size="medium"
                                      type="success">{{ item }}</el-tag>
                            </template>
                          </span>
            </template>
          </template>

        </avue-crud>
      </el-dialog>
    </basic-container>
</template>

<script>
import {mapGetters} from "vuex";
import {getAccountAuditData, queryAccountData} from "@/api/numberAudit";

export default {
        data() {
            return {
                dialogStatus: false,
                data: [
                    {
                        number: '0 个休眠账号',
                        tip: '200天内没有登录的账号'
                    },
                    {
                        number: '0 个孤儿账号',
                        tip: '创建来源未知的账号'
                    }, {
                        number: '0 个密码强度不符合要求',
                        tip: '账号的密码强度小于密码策略对它的要求'
                    }, {
                        number: '0 个账号不符合规范',
                        tip: '账号不符合系统定义的规范'
                    },
                    // {
                    //   number: '0 个账号密码未修改',
                    //   tip: '30天内未修改密码'
                    // },
                    // {
                    //   number: '0 个账号过期',
                    //   tip: '账号过期'
                    // },
                    // {
                    //   number: '0 个账号未绑定组织',
                    //   tip: '账号未绑定组织'
                    // },
                    // {
                    //   number: '0 个账号未分配角色',
                    //   tip: '账号未分配角色'
                    // },
                ],
                option: {
                    title: '表格的标题',
                    addBtn: false,
                    border: true,
                    page: false,
                    align: 'left',
                    menuAlign: 'center',
                    editBtn:false,
                    editBtnText: '数据列表',
                    delBtn: false,
                    editTitle: false,
                    header: false,
                    column: [
                        {
                            label: "审计项目",
                            prop: "number",
                        },
                        {
                            label: "提示",
                            prop: "tip",
                        }
                    ]
                },
                zhsjlx: "", // 账号审计类型
                page2: {
                  currentPage: 1,
                  total: 0,
                  pageSize: 10,
                  // "pageSizes": [ 5, 10, 20, 30, 40, 50, 100 ],
                },
                data2: [
                ],
                option2:{
                  index: true,
                  size: "mini",
                  dialogWidth: 580,
                  dialogClickModal: false,
                  searchShow: false,
                  align: "center",
                  editBtnText: "修改",
                  delBtnText: '删除',
                  addBtn:false,      //是否显示添加按钮
                  refreshBtn: false, //表格上面小的 刷新按钮
                  columnBtn: false,  //表格上面小的 列表按钮
                  searchBtn: false,  //表格上面小的 搜索按钮
                  border: true,
                  menu: false,        //是否显示操作栏
                  column: [
                    {
                      label: "学号/工号",
                      width: 120,
                      prop: "humancode",
                      addDisplay: false,
                      editDisplay: false,
                      search: true,
                    },
                    {
                      label: "姓名",
                      prop: "humanname",
                      width: 120,
                      addDisplay: false,
                      editDisplay: false,
                      search: true,
                    },
                    {
                      label: "性别",
                      prop: "sex",
                      type: "select",
                      width: 60,
                      align: "center",
                      addDisplay: false,
                      editDisplay: false,
                      search: true,
                      dicData: [
                        {label: "男", value: "male"},
                        {label: "女", value: "female"},
                        {label: "请选择", value: ""},
                      ],
                    },
                    {
                      label: "用户类型",
                      prop: "rolename",
                      addDisplay: false,
                      editDisplay: false,
                      slot: true,
                      align: "left",
                    },
                    {
                      label: "组织机构",
                      prop: "organizationnames",
                      addDisplay: false,
                      editDisplay: false,
                      slot: true,
                      align: "left",
                      type: "tree",
                      dicMethod:"post",
                      dicUrl:"/sytSysOrganization/treeListJsonArray",
                      // dicData: this.orgList,
                      props: {
                        value: "id",
                      },
                      span: 12,
                      search: true,
                    },
                    {
                      label: "手机号码",
                      width: 120,
                      prop: "telmobile1",
                      addDisplay: false,
                      editDisplay: false,
                      // search: true,
                    },
                  ]
                }
            }
        },
        computed: {
          ...mapGetters(["userInfo"])
        },
        created() {
          this.onLoad();
        },
        methods: {
            cellStyle({row, columnIndex}) {
              // console.log(row);
                if (columnIndex == 0) {
                    if (row.money <= 3000) {
                        return {
                            color: 'green',
                            fontWeight: 'bold',
                            fontSize: '20'
                        }
                    } else {
                        return {
                            color: 'red',
                            fontWeight: 'bold',
                            fontSize: '20'
                        }
                    }
                }
            },
            onLoad(){
              getAccountAuditData().then((res)=>{
                // console.log(res);
                if (res.data.code === "00000") {
                  const dataMap = res.data.info.dataMap;
                  this.data=[
                      {
                        number: (dataMap.WDLRS==null?0:dataMap.WDLRS)+' 个休眠账号',
                        tip: '90天以上未登录的账号',
                        zhsjlx: 'wdlrs',
                      },
                      {
                        number: (dataMap.WXGMMRS==null?0:dataMap.WXGMMRS)+' 个账号密码未修改',
                        tip: '90天以上未修改密码的账号',
                        zhsjlx: 'wxgmmrs',
                      },
                      {
                        number: (dataMap.ZHGQRS==null?0:dataMap.ZHGQRS)+' 个账号过期',
                        tip: '账号过期',
                        zhsjlx: 'zhgqrs',
                      },
                      {
                        number: (dataMap.WBDZZRS==null?0:dataMap.WBDZZRS)+' 个账号未绑定组织机构',
                        tip: '账号未绑定组织机构',
                        zhsjlx: 'wbdzzrs',
                      },
                      {
                        number: (dataMap.WFPJSRS==null?0:dataMap.WFPJSRS)+' 个账号未分配角色',
                        tip: '账号未分配角色',
                        zhsjlx: 'wfpjsrs',
                      },
                  ];
                }
              })
            },
            refresh() {
              this.onLoad();
            },
            detailClick (row) {
              // console.log(row);
              this.dialogStatus = true;
              this.page2.currentPage=1;
              this.page2.pageSize=10;
              this.zhsjlx = row.zhsjlx;
              this.onLoadUserData();
            },
            sizeChange(val) {
              this.page2.currentPage = 1;
              this.page2.pageSize = val;
              this.onLoadUserData();
            },
            currentChange(val) {
              this.page2.currentPage = val;
              this.onLoadUserData();
            },
            onLoadUserData(){
              const param={
                page: this.page2.currentPage,
                pageSize: this.page2.pageSize,
                queryParam: {"zhsjlx": this.zhsjlx}
              }
              queryAccountData(param).then((res)=>{
                // console.log(res);
                this.loading = false;
                const data = res.data.info;
                this.page2.currentPage = data.current;
                this.page2.total = data.total;
                this.page2.pageSize = data.size;
                this.data2 = data.records;
                // console.log(this.data)
                this.data2.forEach((item) => {
                  item.rolename = item.role;
                  let arr = [];
                  item.role.forEach((item1) => {
                    arr.push(item1.value);
                  });
                  this.$set(item, "role", arr);
                });
              })
            }

        },
    }
</script>
<style lang="scss" scoped>
  :deep(.avue-crud__menu) {
    display: none;
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #f0f0f0;
  }
  :deep(.el-dialog__body) {
    padding: 20px 20px 5px 10px;
  }
</style>
