<template>
    <basic-container>
        <h3 class="pageTitle">菜单管理</h3>
        <avue-crud ref="crud"
                   :table-loading="loading"
                   v-model="form"
                   :option="option"
                   :data="data"
                   :before-close="beforeClose"
                   @row-save="rowSave"
                   @row-update="rowUpdate"
                   @row-del="rowDel"
                   @refresh-change="refresh">
            <template slot="icon" slot-scope="scope">
                <i :class="scope.row.icon" style="font-size:24px"></i>
                <span v-if="!scope.row.icon">-</span>
            </template>
            <template slot-scope="scope" slot="menu">
                <el-button icon="el-icon-circle-plus-outline"
                           type="text"
                           :size="scope.size"
                           @click.stop="addHandle(scope.row)">添加子菜单
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
    import {getEMenu} from "@/api/user";
    import {editMenu, removeMenu} from "@/api/settings";

    export default {
        data() {
            return {
                form: {},
                data: [],
                loading: false
            };
        },
        created() {
            this.onLoad();
        },
        methods: {
            onLoad() {
                this.loading = true;
                getEMenu().then(res => {
                    res.data.info.forEach(item => {
                        item.parentId == null ? (item.parentId = "") : "";
                    });
                    this.data = res.data.info;
                    this.loading = false;
                });
            },
            addHandle(row) {
                // this.$refs.crud.value.parentId = row.id;
                this.$refs.crud.option.column.filter(item => {
                    if (item.prop === "parentId") {
                        item.value = row.id;
                        item.addDisabled = true;
                    }
                });
                this.$refs.crud.rowAdd();
            },
            beforeClose(done) {
                this.$refs.crud.option.column.filter(item => {
                    if (item.prop === "parentId") {
                        item.value = "";
                        item.addDisabled = false;
                    }
                });
                done();
            },
            // 添加
            rowSave(row, done, loading) {
                editMenu(row).then(res => {
                        if (res.data.code == "00000") {
                            this.$message.success("操作成功！");
                            this.onLoad();
                            done();
                        } else {
                            this.$message.error(res.data.info);
                            loading();
                        }
                    }, () => {
                        this.$message.error("服务异常");
                        loading();
                    }
                );
            },
            // 修改
            rowUpdate(row, index, done, loading) {
                this.rowSave(row, done, loading);
            },
            // 删除
            rowDel(row) {
                console.log(row);
                if (row.children.length != 0) {
                    this.$message.warning("请先删除【" + row.label + "】的子级菜单");
                    return;
                }

                this.$confirm(
                    "此操作将永久删除菜单【" + row.label + "】, 是否继续?",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    }
                ).then(() => {
                    removeMenu(row.id).then(res => {
                            if (res.data.code == "00000") {
                                this.$message.success("删除成功！");
                                this.onLoad();
                            } else {
                                this.$message.error("操作失败");
                            }
                        }, () => {
                            this.$message.error("服务异常");
                        }
                    );
                }).catch(() => {
                });
            },
            refresh() {
                this.onLoad();
            }
        },
        computed: {
            option() {
                return {
                    dialogWidth: 580,
                    // headerAlign: "center",
                    // border: true,
                    // stripe: true,
                    index: true,
                    border: true,
                    defaultExpandAll: false,
                    dialogClickModal: false,
                    menuWidth: 230,
                    // menuType: "button",
                    editBtnText: "修改",
                    delBtnText: '删除',
                    size: "mini",
                    refreshBtn: false,
                    columnBtn: false,
                    column: [
                        {
                            label: "菜单名称",
                            prop: "label",
                            span: 24,
                            width: 200,
                            rules: [
                                {required: true, message: "菜单名称不能为空", trigger: "blur"}
                            ]
                        },
                        {
                            label: "菜单图标",
                            prop: "icon",
                            slot: true,
                            align: "center",
                            width: 200,
                            span: 24
                        },
                        {
                            label: "路由地址",
                            prop: "path",
                            span: 24,
                            rules: [
                                {required: true, message: "路由地址不能为空", trigger: "blur"}
                            ]
                        },

                        {
                            label: "组件路径",
                            prop: "component",
                            span: 24
                        },
                        {
                            label: "菜单类型",
                            prop: "isBtn",
                            type: "radio",
                            align: "center",
                            width: 100,
                            span: 12,
                            dicData: [
                                {label: "菜单", value: "否"},
                                {label: "按钮", value: "是"}
                            ],
                            value: "否",
                            rules: [
                                {required: true, message: "菜单类型不能为空", trigger: "blur"}
                            ]
                        },
                        {
                            label: "菜单可见",
                            prop: "status",
                            type: "radio",
                            align: "center",
                            width: 100,
                            span: 12,
                            dicData: [
                                {label: "是", value: "是"},
                                {label: "否", value: "否"}
                            ],
                            value: "是",
                            rules: [
                                {
                                    required: true,
                                    message: "菜单是否可见不能为空",
                                    trigger: "blur"
                                }
                            ]
                        },
                        {
                            label: "显示顺序",
                            prop: "sort",
                            type: "number",
                            span: 24,
                            hide: true,
                        },
                        {
                            label: "上级菜单",
                            prop: "parentId",
                            type: "tree",
                            dicData: this.data,
                            span: 24,
                            props: {
                                value: "id"
                            },
                            hide: true
                        },
                        {
                            label: "接口地址",
                            prop: "url",
                            type: "textarea",
                            span: 24,
                            hide: true
                        }
                    ]
                };
            }
        }
    };
</script>

<style scoped>
    .basic-container {
        height: 100%;
    }

    .basic-container :deep(.el-card) {
        overflow: auto;
        height: 101%;
    }
</style>
