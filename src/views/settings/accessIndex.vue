<template>
  <basic-container>
    <div class="wel__body">
<!--      <el-row class="rowFirst">-->
<!--        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">-->
<!--          <h3 class="data-title">数据概览</h3>-->
<!--          <div class="dataContent">-->
<!--            <div class="data">-->
<!--              <p class="dataName">帐号总数</p>-->
<!--              <p class="dataNumber">9150</p>-->
<!--            </div>-->
<!--            <div class="data">-->
<!--              <p class="dataName">今日新增</p>-->
<!--              <p class="dataNumber">123</p>-->
<!--            </div>-->
<!--            <div class="data">-->
<!--              <p class="dataName">今日删除</p>-->
<!--              <p class="dataNumber">123</p>-->
<!--            </div>-->
<!--            <div class="data">-->
<!--              <p class="dataName">今日修改密码</p>-->
<!--              <p class="dataNumber">1233</p>-->
<!--            </div>-->
<!--            <div class="data">-->
<!--              <p class="dataName">应用接入</p>-->
<!--              <p class="dataNumber">12</p>-->
<!--            </div>-->
<!--            <div class="data">-->
<!--              <p class="dataName">认证成功</p>-->
<!--              <p class="dataNumber">345</p>-->
<!--            </div>-->
<!--            <div class="data">-->
<!--              <p class="dataName">认证失败</p>-->
<!--              <p class="dataNumber">134</p>-->
<!--            </div>-->
<!--            <div class="data">-->
<!--              <p class="dataName">认证接口数</p>-->
<!--              <p class="dataNumber">2</p>-->
<!--            </div>-->
<!--          </div>-->
<!--        </el-col>-->
<!--        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">-->
<!--          <h3 class="data-title">快捷导航</h3>-->
<!--          <div class="dataApp">-->
<!--            <div class="app" @click="routerLink('/client/platform_client/index')">-->
<!--              <i class="el-icon-coordinate"></i>应用认证-->
<!--            </div>-->
<!--            <div class="app" @click="routerLink('/tongyi/user-settings/index')">-->
<!--              <i class="el-icon-tickets"></i>帐号管理-->
<!--            </div>-->
<!--            <div class="app" @click="routerLink('/settings/org-settings/index')">-->
<!--              <i class="el-icon-office-building"></i>组织机构-->
<!--            </div>-->
<!--            <div class="app" @click="routerLink('/settings/password-set/index')">-->
<!--              <i class="el-icon-setting"></i>模块管理-->
<!--            </div>-->
<!--            <div class="app" @click="routerLink('/settings/param-setting/index')">-->
<!--              <i class="el-icon-cpu"></i>系统参数-->
<!--            </div>-->
<!--            <div class="app" @click="routerLink('/settings/menu-settings/index')">-->
<!--              <i class="el-icon-guide"></i>菜单管理-->
<!--            </div>-->
<!--          </div>-->
<!--        </el-col>-->
<!--      </el-row>-->
      <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <h3 class="data-title chartTitle">访问人数统计</h3>
<!--          <div class="pickMonth">-->
<!--            <el-date-picker-->
<!--                v-model="monthvalue"-->
<!--                type="month"-->
<!--                size="small"-->
<!--                placeholder="请选择月份">-->
<!--            </el-date-picker>-->
<!--          </div>-->
          <el-tabs v-model="activeName" >
            <el-tab-pane label="" name="first">
              <el-row :gutter="10">
                <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
                  <div class="longChart" id="numberData">
                  </div>
                </el-col>
                <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                  <div class="shortChart" id="numberPieData">
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
      <avue-crud :data="data"
                 :option="option"
                 ></avue-crud>
    </div>

  </basic-container>
</template>

<script>
import {mapGetters} from "vuex";
import echarts from 'echarts'
import {getAPPVisitNum} from "@/api/settings"

export default {
  name: "wel",
  data() {
    return {
      activeName: 'first',
      monthvalue:"",
      option:{
        header: false,
        menu: false,
        column: [{
          label: '系统名称',
          prop: 'name'
        },{
          label: '访问人数',
          prop: 'num'
        },]
      },
      data: [
        //   {
        //     name: "1",
        //     num: 1
        //    },
        // {
        //   name: "1",
        //   num: 1
        // },
        // {
        //   name: "1",
        //   num: 1
        // },
        // {
        //   name: "1",
        //   num: 1
        // },
      ],
      barXdata:[],
      barYdata:[],
      pieData: [],
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.getData()
    // this.drawBar();
    // this.drawPie();
    // this.proveLine();
    // this.provePie();
    // this.visitLine();
    // this.visitPie();
  },
  created() {
  },
  methods: {
    getData(){
      // this.data.forEach(item=>{
      //   this.barXdata.push(item.name)
      //   this.barYdata.push(item.num)
      //   this.pieData.push({
      //     name: item.name,
      //     value: item.num
      //   })
      // })
      // this.drawBar();
      // this.drawPie();
      getAPPVisitNum().then(res=>{
        // console.log(`getAPPVisitNum`,res.data.data)
				if (res.data.code!=200) {
					return;
				}
        let data = res.data.data;
        this.data = data;
        data.forEach(item=>{
          this.barXdata.push(item.name)
          this.barYdata.push(item.num)
          this.pieData.push({
            name: item.name,
            value: item.num
          })
        })
        this.drawBar();
        this.drawPie();
      })
    },
    drawBar() {
      this.charts = echarts.init(document.getElementById("numberData"))
      this.charts.setOption({
        title: {
          text: '各系统访问人数',
          left: "25px",
          textStyle: {
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '2%',
          top: '15%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#999'
            }
          },
          data: this.barXdata
          // data: ['1号', '2号', '3号', '4号', '5号', '6号', '7号', '8号', '9号', '10号', '11号', '12号', '13号', '14号', '15号', '16号', '17号', '18号', '19号', '20号',, '21号', '22号', '23号', '24号', '25号', '26号', '27号', '28号', '29号', '30号']
        },
        color: ["#6395F9","#62DAAB"],
        yAxis: {
          type: 'value',
          splitNumber: 3,
          axisTick: { //y轴刻度线
            show: false
          },
          splitLine: {
            show: true, //网格线不显示
            lineStyle: {
              color: '#eee'
            }
          },
          axisLine: { //y轴
            show: false,
            lineStyle: {
              color: '#999'
            }
          },
        },
        series: [{
          name: '人数',
          // markPoint: {
          //   data: [{
          //     type: 'max',
          //     name: '最大值'
          //   },
          //     {
          //       type: 'min',
          //       name: '最小值'
          //     }
          //   ]
          // },
          data: this.barYdata,

          // data: [23, 32, 32, 56, 91, 45, 25, 32, 56,
          //   91, 45, 40, 32, 56, 91, 45, 25, 32, 56,
          //   91, 45, 40, 23, 32, 32, 56, 91, 45, 25, 32, 56,56
          // ],
          type: 'line',
          barWidth: 20,
          symbol: "none",
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: 'rgb(99,149,249)'
            }, {
              offset: 1,
              color: 'rgb(255,255,255)'
            }])
          },
        },
        ]
      })
    },
    drawPie() {
      this.charts = echarts.init(document.getElementById("numberPieData"))
      this.charts.setOption({
        title: {
          text: '访问人数',
          left: "25px",
          textStyle: {
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        color: ['#6395F9', '#62DAAB', '#F6C022', '#E96C5B', '#3D9BE3', '#B98B7F', '#BEC467','#53B4BA','#D673AE','#B3DFDE'],
        legend: {
					type: 'scroll', //可滚动翻页的图例。当图例数量较多时可以使用。
          bottom: '10px',
          orient: 'horizontal',
          icon: 'pin',
          // data: ['学生帐号', '管理帐号', '教工帐号', '其他']
          data: this.barXdata,
        },
        series: [{
          name: '访问人数',
          type: 'pie',
          // label: {
          //   formatter: '{b}' + '\n' + '{c} ({d}%)'
          // },
          center: ['50%', '50%'],
          radius: ['35%', '50%'],
					/*avoidLabelOverlap: false,
					label: {
						normal: {
							show: false,
							position: 'center'
						},
						emphasis: {
							show: true,
							textStyle: {
								fontSize: '20',
								fontWeight: 'bold'
							}
						}
					},
					labelLine: {
						normal: {
							show: false
						}
					},*/
          data: this.pieData,
          // data: [{
          //   value: 235,
          //   name: '学生帐号'
          // },
          //   {
          //     value: 128,
          //     name: '管理帐号'
          //   },
          //   {
          //     value: 56,
          //     name: '教工帐号'
          //   },
          //   {
          //     value: 56,
          //     name: '其他'
          //   }
          // ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
    },
  },
};
</script>
<style scoped="scoped">
.basic-container >>> .el-card{
  background: #F0F2F5;
  /*padding: 10px 10px;*/
  box-sizing: border-box;
}
/*.basic-container >>> .el-card__body{*/
/*  padding: 0;*/
/*}*/
.el-row .el-col{
  background: #fff;
  border-radius: 3px;

}
.rowFirst .el-col{
  height: 240px;
  width: calc(50% - 5px);
  margin-bottom: 10px;
}
.rowFirst .el-col:nth-of-type(1){
  margin-right: 10px;
}
.el-tabs >>> .el-tabs__nav{
  margin-left: 200px;
}
.el-tabs >>> .el-tabs__item{
  height: 50px;
  line-height: 50px;
}
.el-tabs >>> .el-tabs__nav-wrap::after{
  height: 1px;
}
.chartTitle{
  border: none !important;
  position: absolute;
  left: 0;
  top: 3px;
}
.pickMonth{
  position: absolute;
  right: 10px;
  top:10px;
  z-index: 10;
}
.longChart,.shortChart{
  width: 100%;
  height: 350px;
}
.zanwuContent{
  width: 100%;
  height: 100%;
  padding-top: 80px;
  text-align: center;
  font-size: 12px;
  color: #ccc;
}
.zanwuContent img{
  width: 128px;
}
</style>
<style scoped="scoped" lang="scss">
.data-box {
  padding-top: 20px;
}

.avue-view {
  height: 100%;
}

.data-title{
  font-size: 16px;
  border-bottom: 1px solid #E4E7ED;
  height: 45px;
  line-height: 45px;
  margin: 0;
  padding: 0 15px;
}
.dataContent{
  padding:0 20px;
  padding-top: 20px;
}
.data{
  width: 25%;
  display: inline-block;
  margin: 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  .dataName{
    margin: 0;
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
  }
  .dataNumber{
    font-size: 25px;
    margin: 0;
  }
}
.dataApp{
  padding:10px;
}
.app{
  display: inline-block;
  background: #F0F2F3;
  height: 65px;
  line-height: 65px;
  width: 30.33%;
  margin: 10px 1.5%;
  text-align: center;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  i{
    color: #1890ff;
    margin-right: 10px;
    font-size: 25px;
    vertical-align: sub;
  }
}

@media screen and (min-width: 320px) and (max-width: 750px) {
  .wel__info {
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 10px;
  }
  .wel__info-title {
    font-size: 18px;
  }
  .wel__info-content {
    margin-left: 20px;
  }
  .wel__info-img {
    width: 60px;
    height: 60px;
    border-radius: 30px;
  }
}
</style>
