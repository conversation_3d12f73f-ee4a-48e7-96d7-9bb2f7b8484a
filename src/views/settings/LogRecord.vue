<template>
  <div>
    <basic-container>
      <avue-crud :table-loading="tableLoading"
                 :option="optionDetail"
                 :data="pageList"
                 :page.sync="page"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 @row-del="deleteHandle"
                 @refresh-change="refresh"
                 @search-change="searchChange">
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import {mapGetters} from 'vuex';
import {Delete, QueryPage} from '@/api/sytLogRecord';
import {formatDate} from '@/util';

export default {
  inject: ['reload'],
  data() {
    return {
      uploadObj: {},
      tableLoading: false,
      pageList: [],
      page: {
        //pageSizes: [10, 20, 30, 40],默认
        currentPage: 1,
        total: 0,
        pageSize: 10,
      },
      data: [],
      optionDetail: {
        index: true,
        size: 'mini',
        dialogWidth: 580,
        dialogClickModal: false,
        searchBtn: true,
        searchShow: false,
        menu: false,
        menuWidth: 150,
        align: 'center',
        delBtnText: '删除',
        addBtn:false,
        editBtn:false,
        delBtn:false,
        border: true,
        column: [
          {
            label: '操作人',
            prop: 'operator',
            span: 24,
            search: true,
          },
          {
            label: '操作模块',
            prop: 'type',
            span: 24,
          },
          {
            label: '操作时间',
            prop: 'createTime',
            span: 24,
            format: "yyyy-MM-dd HH:mm:ss"
            ,formatter:(val,value,label)=>{
              return formatDate(value);
            }
          },
          {
            label: '操作描述',
            prop: 'action',
            span: 24,
          },
          {
            label: '操作内容',
            prop: 'bizNo',
            span: 24,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  created() {
    this.onLoad(this.pageParam());
  },
  methods: {
    // 删除
    deleteHandle(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        Delete({'id': row.id}).then(res => {
          if (res.data.code === '00000') {
            this.$message({type: 'success', message: '删除成功'});
            this.onLoad(this.pageParam());
          }
        });
      }).catch(() => {
        console.log('已取消删除操作');
      });
    },
    // 列表查询
    onLoad(param) {
      this.tableLoading = true;
      QueryPage(param).then(res => {
        this.tableLoading = false;
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.pageList = data.records;
        this.data = data.records;
      });
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.onLoad(this.pageParam());
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.onLoad(this.pageParam());
    },
    pageParam() {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: {},
      };
    },
    searchChange(param, done) {
      var pageParam = this.pageParam();
      pageParam.queryParam = param;
      this.onLoad(pageParam);
      done();
    },
    refresh() {
      this.onLoad(this.pageParam());
    },
  },
};
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
  width: 25%;
  text-align: left;
  padding-top: 0;
}

.avue-view {
  height: 100%;
}
</style>
