<template>
    <basic-container>
        <h3 class="pageTitle">模块管理</h3>
        <el-container>
            <el-aside width="280px" style="margin-right: 20px">
                <el-input style="margin-bottom: 16px"
                          size="mini"
                          placeholder="输入关键字进行过滤"
                          v-model="filterText"></el-input>
                <el-tree class="filter-tree"
                         :data="treeData"
                         :props="defaultProps"
                         :filter-node-method="filterNode"
                         @node-click="treeToOnLoad"
                         ref="tree"></el-tree>
            </el-aside>
            <el-main>
                <avue-crud :data="data"
                           :option="option"
                           :page.sync="page"
                           :table-loading="loading"
                           @size-change="sizeChange"
                           @current-change="currentChange"
                           @row-del="deleteItemHandle"
                           @row-save="addUserHandle"
                           @row-update="updateHandle"
                           @refresh-change="refresh">
                    <template slot="rolename" slot-scope="scope">
                        <el-tag style="margin-right: 5px"
                                v-for="item in scope.row.rolename"
                                :key="item.value"
                                size="medium">{{ item.label }}
                        </el-tag>
                    </template>
                    <template slot="organizationnames" slot-scope="scope">
                        <template v-if="scope.row.organizationnames">
                          <span v-for="(cate, index) in scope.row.organizationnames.split(';')"
                                :key="cate + index">
                            <el-tag style="margin-right: 5px"
                                    type="danger"
                                    size="medium"
                                    v-if="index % 2 == 0">{{ cate }}</el-tag>
                            <template v-else>
                              <el-tag style="margin-right: 5px"
                                      v-for="item in cate.split(',')"
                                      :key="item"
                                      size="medium"
                                      type="success">{{ item }}</el-tag>
                            </template>
                          </span>
                        </template>
                    </template>
                </avue-crud>
            </el-main>
        </el-container>
    </basic-container>
</template>

<script>
    import {
        GetRoleInfo,
        GetModuleParentList,
        GetModuleList,
        EditModuleList,
        DeleteModuleList,
    } from "@/api/settings";
    import {GetOrg} from "@/api/basetable";

    export default {
        data() {
            return {
                // tree
                filterText: "",
                treeData: [],
                defaultProps: {
                    label: "name",
                    value: "id",
                },
                curTreeId: "",
                // form
                data: [],
                page: {
                    currentPage: 1,
                    total: 0,
                    pageSize: 10,
                },
                roleList: [],
                orgList: [],
                loading: false,
            };
        },
        created() {
            this.getTreeList();
            // this.onLoad();
            // this.getRoleList();
            // this.getOrgList();
        },
        mounted() {
        },
        methods: {
            // ===
            getTreeList() {
                GetModuleParentList().then((res) => {
                    this.treeData = res.data.info;
                    this.treeToOnLoad(this.treeData[0]);
                    // this.curTreeId = this.treeData[0].id;
                    // this.onLoad({ parentId: this.curTreeId });
                });
            },

            getRoleList() {
                GetRoleInfo().then((res) => {
                    this.roleList = res.data.info;
                });
            },
            getOrgList() {
                GetOrg().then((res) => {
                    this.orgList = res.data.info;
                });
            },
            onLoad(queryParam = {}) {
                this.loading = true;
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam,
                };
                GetModuleList(context).then((res) => {
                    this.loading = false;
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                });
            },
            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.onLoad();
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.onLoad();
            },
            refresh() {
                this.curTreeId == ""
                    ? this.onLoad()
                    : this.onLoad({parentId: this.curTreeId});
            },

            //tree filter
            filterNode(value, data) {
                if (!value) return true;
                return data.name.indexOf(value) !== -1;
            },

            treeToOnLoad(data) {
                this.curTreeId = data.id;
                this.onLoad({parentId: data.id});
            },

            // 添加
            addUserHandle(row, done, loading) {
            // , type = ""
                const context = {
                    ...row,
                    parentId: this.curTreeId,
                };

                EditModuleList(context).then((res) => {
                    if (res.data.code == "00000") {
                        done();
                        this.$message({
                            type: "success",
                            message: "操作成功!",
                        });
                        if (curTreeId) {
                            this.onLoad({parentId: this.curTreeId});
                        }
                    } else {
                        loading();
                        this.$message.error("操作失败");
                    }
                });
            },

            // 修改
            updateHandle(row, index, done, loading) {
                this.addUserHandle(row, done, loading, "edit");
            },

            // 删除
            deleteItemHandle(row) {
                this.$confirm(
                    "此操作将永久删除链接名称【" + row.name + "】, 是否继续?",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                ).then(() => {
                    DeleteModuleList(row.id).then((res) => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!",
                            });
                            this.refresh();
                        } else {
                            this.$message.error("删除失败");
                        }
                    });
                }).catch(() => {
                });
            },
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            },
        },
        computed: {
            option() {
                return {
                    // dialogWidth: 700,
                    dialogWidth: 580,
                    size: "mini",
                    index: true,
                    border: true,
                    // tabs: true,
                    stripe: true,
                    dialogClickModal: false,
                    menuWidth: 150,
                    align: "center",
                    span: 24,
                    // menuType: "button",
                    editBtnText: "修改",
                    delBtnText: "删除",
                    refreshBtn: false,
                    columnBtn: false,
                    column: [
                        {
                            label: "链接名称",
                            prop: "name",
                            addDisplay: false,
                            editDisplay: false,
                        },
                        {
                            label: "链接地址",
                            prop: "pattern",
                            addDisplay: false,
                            editDisplay: false,
                        },
                        {
                            label: "链接类型",
                            prop: "type",
                            align: "center",
                            addDisplay: false,
                            editDisplay: false,
                        },
                    ],
                    group: [
                        {
                            // label: "基本信息",
                            prop: "group1",
                            column: [
                                {
                                    label: "链接名称",
                                    prop: "name",
                                    rules: [{required: true, message: "链接名称不能为空"}],
                                },
                                {
                                    label: "链接地址",
                                    prop: "pattern",
                                    // rules: [{required: true, message: "链接地址不能为空"}],
                                },
                                {
                                    label: "链接类型",
                                    prop: "type",
                                    type: "select",
                                    placeholder: "请选择链接类型",
                                    dicData: [
                                        {lable: "接口", value: "接口"},
                                        {label: "页面", value: "页面"},
                                    ],
                                    /*rules: [
                                        {
                                            required: true,
                                            message: "链接类型不能为空",
                                        },
                                    ],*/
                                },
                            ],
                        },
                    ],
                };
            },
        },
    };
</script>

<style scoped>
    .basic-container {
        height: 100%;
    }

    .basic-container :deep(.el-card) {
        overflow: auto;
        height: 101%;
    }
</style>
