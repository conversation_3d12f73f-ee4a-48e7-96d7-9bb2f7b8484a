<template>
    <basic-container>
        <h3 class="pageTitle">角色管理</h3>
        <avue-crud :data="data"
                   :option="option"
                   :table-loading="loading"
                   @refresh-change="refresh"
                   @row-save="rowSave"
                   @row-update="rowUpdate"
                   @row-del="rowDel"></avue-crud>
    </basic-container>
</template>


<script>
    const dicData = [
      { value: "SuperAdmin", label: "超级管理员" },
      { value: "system", label: "系统管理员" },
      { value: "leader", label: "领导" },
      { value: "teacher", label: "教师" },
      { value: "student", label: "学生" },
      { value: "user", label: "用户" }
    ];
    const typeData = [
      {label: "超级管理员", value: "0"},
      {label: "所有部门", value: "1"},
      {label: "本部门及子部门", value: "2"},
      {label: "仅本部门", value: "3"},
      {label: "仅用户自己", value: "4"},
      {label: "动态配置", value: "5"},
    ];
    import {
        EditRole,
        GetERoleInfo,
        RemoveRole,
        GetModuleTree,
    } from "@/api/settings";
    import {getEMenu} from "@/api/user";

    export default {
        data() {
            return {
                data: [],
                menuList: [],
                moduleList: [],
                loading: false,
            };
        },
        created() {
            this.onLoad();
            this.getMenuList();
            this.getModuleList();
        },
        methods: {
            onLoad() {
                this.loading = true;
                GetERoleInfo().then((res) => {
                    this.loading = false;
                    this.data = res.data.info;
                });
            },
            getMenuList() {
                getEMenu().then((res) => {
                    this.menuList = res.data.info;
                });
            },

            getModuleList() {
                GetModuleTree().then((res) => {
                    this.moduleList = res.data.info;
                });
            },

            rowSave(row, done, loading) {
                EditRole(row).then(
                    (res) => {
                        if (res.data.code == "00000") {
                            done();
                            this.$message.success("操作成功！");
                            this.onLoad();
                        } else {
                            loading();
                            this.$message.error(res.data.info);
                        }
                    },
                    () => {
                        loading();
                        this.$message.error("服务异常");
                    }
                );
            },
            rowUpdate(row, index, done, loading) {
                this.rowSave(row, done, loading);
            },

            rowDel(row) {
                this.$confirm(
                    "此操作将永久删除角色【" + row.rolename + "】, 是否继续?",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                ).then(() => {
                    RemoveRole(row.id).then((res) => {
                        if (res.data.code == "00000") {
                            this.$message.success("删除成功！");
                            this.onLoad();
                        } else {
                            this.$message.error(res.data.info);
                        }
                    });
                }).catch(() => {
                });
            },
            refresh() {
                this.onLoad();
            },
        },
        computed: {
            option() {
                return {
                    dialogWidth: 580,
                    dialogClickModal: false,
                    labelWidth: 100,
                    index: true,
                    span: 24,
                    menuWidth: 150,
                    border: true,
                    size: "mini",
                    align: "center",
                    // menuType: "button",
                    editBtnText: "修改",
                    delBtnText: "删除",
                    refreshBtn: false,
                    columnBtn: false,
                    column: [
                        {
                            label: "角色名称",
                            prop: "rolename",
                            rules: [{required: true, message: "角色名称不能为空"}],
                        },
                        {
                            label: "角色标识",
                            prop: "rolekey",
                            type: "select",
                            dicData: dicData,
                            rules: [{required: true, message: "角色标识不能为空"}],
                        },
                      {
                        label: "角色类型",
                        prop: "roletype",
                        type: "select",
                        dicData: typeData,
                        rules: [{required: true, message: "角色类型不能为空"}],
                      },
                        {
                            label: "菜单授权",
                            prop: "resourceIds",
                            type: "tree",
                            hide: true,
                            multiple: true,
                            dicData: this.menuList,
                            props: {
                                value: "id",
                            },
                            rules: [{required: true, message: "菜单授权不能为空"}],
                        },
                        {
                            label: "模块授权",
                            prop: "resourceLinkIds",
                            type: "tree",
                            hide: true,
                            multiple: true,
                            dicData: this.moduleList,
                            props: {
                                value: "id",
                                label: "name",
                            },
                            rules: [{required: true, message: "模块授权不能为空"}],
                        },
                      {
                        label: "显示顺序",
                        prop: "displayorder",
                        type: "number",
                      },
                        {
                            label: "角色描述",
                            prop: "roleinfo",
                            type: "textarea",
                            maxlength: 150,
                            showWordLimit: true,
                        },
                    ],
                };
            },
        },
    };
</script>

<style scoped>
    .basic-container {
        height: 100%;
    }

    .basic-container :deep(.el-card) {
        overflow: auto;
        height: 101%;
    }
</style>
