<template>
    <basic-container>
        <h3 class="pageTitle">人员当前状态表</h3>
        <avue-crud :data="data"
                   :option="option"
                   :page.sync="page"
                   @row-save="saveHandle"
                   @row-update="updateHandle"
                   @row-del="deleteHandle"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
import {Delete, Edit, Query} from "@/api/basetable";

export default {
        inject: ["reload"],
        data() {
            return {
                type: "sytCodeDqztb",
                page: {
                    currentPage: 1,
                    total: 1,
                    pageSize: 10,
                },
                obj: {},
                data: [],
                option: {},
            };
        },
        created() {
            this.getType();
            this.getInfo();
        },
        methods: {
            getType() {
                if (this.type == "sytCodeDqztb") {
                    this.option = {
                        index: true,
                        size: "mini",
                        editBtnText: "修改",
                        delBtnText: "删除",
                        border: true,
                        align: "center",
                        menuAlign: "center",
                        dialogWidth: 580,
                        menuWidth: 150,
                        dialogClickModal: false,
                        refreshBtn: false,
                        columnBtn: false,
                        column: [
                            {
                                label: "id",
                                prop: "id",
                                hide: true,
                                addDisplay: false,
                                editDisplay: false,
                            },
                            {
                                label: "当前状态码",
								labelWidth: 100,
                                prop: "code",
                                span: 24,
                                rules: [{required: true, message: "当前状态码不能为空"}],
                            },
                            {
                                label: "当前状态",
								labelWidth: 100,
                                prop: "name",
                                span: 24,
                                rules: [{required: true, message: "当前状态不能为空"}],
                            },
                            {
                                label: "备注",
								labelWidth: 100,
                                type: "textarea",
                                span: 24,
                                prop: "bz",
                                hide: true,
                                maxlength: 150,
                                showWordLimit: true,
                            },
                        ],
                    };
                }
            },

            getInfo() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {},
                };
                Query(context, this.type).then((res) => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                });
            },

            saveHandle(row, done, loading) {
                Edit(row, this.type).then((res) => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "新增成功!",
                        });
                        done();
                        this.reload();
                    } else {
                        this.$message.error("新增失败");
                        loading();
                    }
                });
            },

            updateHandle(row, index, done, loading) {
                Edit(row, this.type).then((res) => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!",
                        });
                        done();
                        this.reload();
                    } else {
                        this.$message.error("修改失败");
                        loading();
                    }
                });
            },

            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    Delete(row.id, this.type).then((res) => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!",
                            });
                            this.reload();
                        } else {
                            this.$message.error("删除失败");
                        }
                    });
                }).catch(() => {
                });
            },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getInfo();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getInfo();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.reload();
            },
        },
    };
</script>

<style scoped>
    .w {
        padding: 10px;
        padding-top: 0;
    }
</style>
