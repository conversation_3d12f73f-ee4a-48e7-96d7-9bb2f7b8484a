<template>
    <basic-container>
        <avue-crud :data="data"
                   :option="option"
                   :page.sync="page"
                   @row-save="saveHandle"
                   @row-update="updateHandle"
                   @row-del="deleteHandle"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    import {Query, Edit, Delete} from "@/api/basetable";
    import {GetRoleInfo} from "@/api/settings";
    import {GetOrg} from "@/api/basetable";

    export default {
        inject: ["reload"],
        data() {
            return {
                type: this.$route.path.split("/")[2],
                page: {
                    currentPage: 1,
                    total: 1,
                    pageSize: 10
                },
                obj: {},
                data: [],
                option: {}
            };
        },
        created() {
            this.getType();
            this.getInfo();
        },
        methods: {
            getType() {
                if (this.type == "sytDesktop") {
                    this.option = {
                        index: true,
                        editBtnText: "修改",
                        delBtnText: '删除',
                        border: true,
                        align: "center",
                        menuAlign: "center",
                        labelWidth: 120,
                        menuWidth: 150,
                        dialogClickModal: false,
                        size: "mini",
                        column: [
                            {
                                label: "id",
                                prop: "id",
                                hide: true,
                                addDisplay: false,
                                editDisplay: false
                            },
                            {
                                label: "桌面名称",
                                prop: "name",
                                rules: [{required: true, message: "桌面名称不能为空"}]
                            },
                            {
                                label: "图标",
                                prop: "icon"
                                // hide: true
                            },
                            {
                                label: "排序",
                                prop: "sort",
                                type: "number"
                            },
                            {
                                label: "备注",
                                type: "textarea",
                                span: 24,
                                prop: "remark",
                                hide: true,
                                maxlength: 150,
                                showWordLimit: true
                            }
                        ]
                    };
                }

                this.getSelectList();
            },

            getSelectList() {
                GetRoleInfo().then(res => {
                    if (res.data.code == "00000") {
                        this.option.column.splice(4, 0, {
                            label: "可见角色",
                            placeholder: "请选择可见角色 - 可多选",
                            // rules: [{ required: true, message: "请选择可见角色" }],
                            prop: "roleIds",
                            type: "select",
                            drag: true,
                            span: 12,
                            multiple: true,
                            dicData: res.data.info,
                            props: {
                                label: "rolename",
                                value: "id"
                            }
                        });

                        GetOrg().then(res => {
                            if (res.data.code == "00000") {
                                this.option.column.splice(5, 0, {
                                    span: 24,
                                    label: "可见组织机构",
                                    placeholder: "请选择可见角色 - 可多选",
                                    rules: [
                                        {
                                            required: true,
                                            message: "请选择可见组织机构"
                                        }
                                    ],
                                    prop: "orgIds",
                                    type: "tree",
                                    multiple: true,
                                    dicData: res.data.info,
                                    props: {
                                        value: "id"
                                    }
                                });
                            } else {
                                this.$message.error("服务异常");
                            }
                        });
                    } else {
                        this.$message.error("服务异常");
                    }
                });
            },

            getInfo() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                };
                Query(context, this.type).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                });
            },

            saveHandle(row, done, loading) {
                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "新增成功!"
                        });
                        done();
                        this.reload();
                    } else {
                        this.$message.error("新增失败");
                        loading();
                    }
                });
            },

            updateHandle(row, index, done, loading) {
                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!"
                        });
                        done();
                        this.reload();
                    } else {
                        this.$message.error("修改失败");
                        loading();
                    }
                });
            },

            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        Delete(row.id, this.type).then(res => {
                            if (res.data.code == "00000") {
                                this.$message({
                                    type: "success",
                                    message: "删除成功!"
                                });
                                this.reload();
                            } else {
                                this.$message.error("删除失败");
                            }
                        });
                    })
                    .catch(() => {
                    });
            },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getInfo();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getInfo();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.reload();
            }
        }
    };
</script>

<style scoped>
    .w {
        padding: 10px;
        padding-top: 0;
    }
</style>
