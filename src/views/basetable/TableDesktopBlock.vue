<template>
    <basic-container>
        <avue-crud v-model="obj"
                   :data="data"
                   :option="option"
                   :page.sync="page"
                   @row-save="saveHandle"
                   @row-update="updateHandle"
                   @row-del="deleteHandle"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    import {Query, Edit, Delete} from "@/api/basetable";
    import {GetRoleInfo} from "@/api/settings";
    import {GetOrg} from "@/api/basetable";
    import {GetDesktop, GetDataCategory} from "@/api/platform";

    export default {
        data() {
            return {
                type: this.$route.path.split("/")[2],
                page: {
                    currentPage: 1,
                    total: 1,
                    pageSize: 10
                },
                obj: {},
                data: [],
                selectData: [],
                categoryIdsData: [],
                desktopIdData: [],
                roleIdsData: [],
                orgIdsData: []
            };
        },
        created() {
            this.getSelectList();
            this.getInfo();
        },
        methods: {
            getSelectList() {
                GetDesktop().then(res => {
                    if (res.data.code == "00000") {
                        this.desktopIdData = res.data.info;
                    }
                });

                GetRoleInfo().then(res => {
                    if (res.data.code == "00000") {
                        this.roleIdsData = res.data.info;
                    }
                });

                GetOrg().then(res => {
                    if (res.data.code == "00000") {
                        this.orgIdsData = res.data.info;
                    }
                });
            },

            getInfo() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                };
                Query(context, this.type).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                });
            },

            saveHandle(row, done, loading) {
                // console.log(row);

                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        done();
                        this.$message.success("新增成功！");
                        this.reload();
                    } else {
                        this.$message.error("新增失败");
                        loading();
                    }
                });
            },

            updateHandle(row, index, done, loading) {
                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!"
                        });
                        done();
                        this.reload();
                    } else {
                        this.$message.error("修改失败");
                        loading();
                    }
                });
            },

            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    Delete(row.id, this.type).then(res => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!"
                            });
                            this.reload();
                        } else {
                            this.$message.error("删除失败");
                        }
                    });
                }).catch(() => {
                });
            },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getInfo();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getInfo();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.reload();
            }
        },
        watch: {
            "obj.desktopId"(val) {
                // console.log(val);
                if (val != "") {
                    this.selectData = [
                        {label: "轮播图", value: "lunbotu"},
                        {label: "新闻公告", value: "xinwen"},
                        {label: "消息任务", value: "xiaoxirenwu"},
                        {label: "我的数据", value: "wodeshuju"},
                        {label: "集成系统", value: "jichengxitong"},
                        {label: "我的服务", value: "wodefuwu"}
                    ];
                } else {
                    this.selectData = [];
                }
            },
            "obj.model"(val) {
                if (val != "") {
                    GetDataCategory({desktopId: this.obj.desktopId, model: val}).then(
                        res => {
                            if (res.data.code == "00000") {
                                this.categoryIdsData = res.data.info;
                            }
                        }
                    );
                }
            }
        },
        computed: {
            option() {
                return {
                    // dialogWidth: 580,
                    size: "mini",
                    labelWidth: 120,
                    index: true,
                    delBtnText: '删除',
                    border: true,
                    align: "center",
                    menuAlign: "center",
                    menuWidth: 150,
                    dialogClickModal: false,
                    // menuType: "button",
                    editBtnText: "修改",
                    column: [
                        {
                            label: "板块名称",
                            prop: "name",
                            span: 12,
                            rules: [{required: true, message: "板块名称不能为空"}]
                        },
                        {
                            label: "所属桌面",
                            prop: "desktopName",
                            addDisplay: false,
                            editDisplay: false
                        },
                        {
                            label: "启用状态",
                            prop: "status",
                            type: "radio",
                            dicData: [
                                {value: "禁用", label: "禁用"},
                                {value: "启用", label: "启用"}
                            ],
                            span: 12,
                            // rules: [{ required: true, message: "启用状态不能为空" }],
                            value: "启用"
                        },
                        {
                            label: "排序",
                            prop: "sort",
                            type: "number",
                            span: 12
                            // tip: "数字越小越靠前"
                            // rules: [{ required: true, message: "排序不能为空" }]
                        },

                        {
                            label: "展示个数",
                            prop: "limit",
                            type: "number",
                            hide: true,
                            span: 12
                        },
                        {
                            label: "布局",
                            prop: "style",
                            type: "select",
                            span: 12,
                            dicData: [
                                {label: "通栏", value: "通栏"},
                                {label: "半栏", value: "半栏"}
                            ],
                            rules: [{required: true, message: "布局不能为空"}]
                        },
                        {
                            label: "所属桌面",
                            rules: [{required: true, message: "请选择所属桌面"}],
                            prop: "desktopId",
                            span: 12,
                            type: "select",
                            dicData: this.desktopIdData,
                            props: {
                                label: "name",
                                value: "id"
                            },
                            change: () => {
                                this.obj.model = "";
                                this.obj.categoryIds = "";
                            }
                        },
                        {
                            label: "展示类型",
                            prop: "model",
                            type: "select",
                            hide: true,
                            span: 12,
                            dicData: this.selectData,
                            click: () => {
                                if (this.obj.desktopId == "") {
                                    this.$message.warning("请先选择所属桌面");
                                }
                            },
                            change: () => {
                                this.obj.categoryIds = "";
                            }
                        },
                        {
                            label: "标签页选项",
                            prop: "categoryIds",
                            type: "select",
                            drag: true,
                            span: 12,
                            hide: true,
                            multiple: true,
                            dicData: this.categoryIdsData,
                            props: {
                                label: "name",
                                value: "id"
                            },
                            click: () => {
                                if (this.obj.model == "") {
                                    this.$message.warning("请先选择展示类型");
                                }
                            }
                        },
                        {
                            label: "可见角色",
                            // rules: [
                            //   { required: true, message: "请选择可见角色 - 可多选" }
                            // ],
                            prop: "roleIds",
                            type: "select",
                            drag: true,
                            span: 12,
                            multiple: true,
                            dicData: this.roleIdsData,
                            props: {
                                label: "rolename",
                                value: "id"
                            }
                        },
                        {
                            span: 12,
                            label: "可见组织机构",
                            // rules: [
                            //   {
                            //     required: true,
                            //     message: "请选择可见组织机构 - 可多选"
                            //   }
                            // ],
                            prop: "orgIds",
                            type: "tree",
                            multiple: true,
                            dicData: this.orgIdsData,
                            props: {
                                value: "id"
                            }
                        },

                        {
                            label: "备注",
                            type: "textarea",
                            span: 24,
                            prop: "remark",
                            hide: true,
                            maxlength: 150,
                            maxRows: 3,
                            showWordLimit: true
                        }
                    ]
                };
            }
        }
    };
</script>

<style scoped>
    .basic-container {
        height: 100%;
    }

    .basic-container :deep(.el-card) {
        overflow: auto;
        height: 101%;
    }
</style>
