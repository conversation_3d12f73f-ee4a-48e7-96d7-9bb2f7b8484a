<template>
    <div>
        <basic-container>
            <avue-crud ref="crud"
                       v-model="form"
                       :table-loading="tableLoading"
                       :option="optionDetail"
                       :data="pageList"
                       :page.sync="page"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       @row-save="saveHandle"
                       @row-update="updateHandle"
                       @row-del="deleteHandle"
                       @search-change="searchChange"
                       @refresh-change="refresh"
            >
                <!--                <template v-slot:menuForm="{row,index,type}">-->
                <!--                    <el-button type="primary" icon="el-icon-check" size="small" plain @click="testConnect">测试-->
                <!--                    </el-button>-->
                <!--                </template>-->
            </avue-crud>
        </basic-container>
    </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteParam, ListParam, SaveParam} from "./qywxApi";

export default {
    inject: ["reload"],
    data() {
        return {
            form: {},
            tableLoading: false,
            pageList: [],
            page: {
                //pageSizes: [10, 20, 30, 40],默认
                currentPage: 1,
                total: 0,
                pageSize: 10
            },
            optionDetail: {
                index: true,
                size: "mini",
                dialogWidth: 580,
                dialogClickModal: false,
                searchBtn: true,
                searchShow: false,
                // menuWidth: 150,
                align: "center",
                // editBtnText: "修改",
                // delBtnText: '删除',
                border: true,
                column: [
                    {
                        label: 'key',
                        prop: "key",
                        type: "select",
                        dicMethod: "get",
                        dicUrl: 'platform/qywx/param/paramKeys',
                        props: {
                            label: "name",
                            value: "value",
                        },
                        search: true,
                        rules: [{required: true, message: "required key", trigger: blur}]
                    },
                    {
                        label: 'enabled',
                        prop: "enabled",
                        type: "switch",
                        value: false,
                        rules: [{type: 'boolean', required: true, trigger: blur}]
                    },
                    {
                        label: 'value',
                        prop: "value",
                        type: "textarea",
                        span: 24,
                    },
                    // {
                    //     label: '创建人',
                    //     prop: "createUser",
                    //     addDisplay: false,
                    //     editDisplay: false,
                    // },
                    {
                        label: '更新时间',
                        prop: "updated",
                        readonly: true,
                        addDisplay: false,
                        editDisplay: false,
                        editDisabled: true,
                        addDisabled: true,
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                    },
                ]
            }
        }
    },
    computed: {
        ...mapGetters(["userInfo"])
    },
    created() {
        this.onLoad(this.pageParam());
    },
    methods: {
        saveHandle(row, done) {
            SaveParam(row).then(() => {
                done();
                this.refresh();
            })
        },
        updateHandle(row, index, done) {
            SaveParam(row).then(() => {
                done();
                this.refresh();
            })
        },
        deleteHandle(row) {
            this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                DeleteParam({key: row.key}).then(() => {
                    this.refresh();
                })
            }).catch(() => {
                console.log('已取消删除操作')
            });
        },
        // 列表查询
        onLoad(param) {
            this.tableLoading = true
            ListParam(param).then(res => {
                this.tableLoading = false
                if (res.data.status) {
                    const data = res.data.data;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.pageList = data.records;
                }
            })
        },
        sizeChange(val) {
            this.page.currentPage = 1;
            this.page.pageSize = val;
            this.onLoad(this.pageParam())
        },
        currentChange(val) {
            this.page.currentPage = val;
            this.onLoad(this.pageParam())
        },
        pageParam() {
            return {
                page: this.page.currentPage,
                pageSize: this.page.pageSize
            }
        },
        searchChange(param, done) {
            let pageParam = this.pageParam()
            pageParam.param = param;
            this.onLoad(pageParam)
            done()
        },
        refresh() {
            this.onLoad(this.pageParam());
        }
    }
}
</script>
