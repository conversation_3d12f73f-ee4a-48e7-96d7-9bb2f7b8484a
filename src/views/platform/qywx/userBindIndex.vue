<template>
    <div>
        <basic-container>
            <avue-crud ref="crud"
                       v-model="form"
                       :table-loading="tableLoading"
                       :option="optionDetail"
                       :data="pageList"
                       :page.sync="page"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       @row-update="updateHandle"
                       @row-del="deleteHandle"
                       @search-change="searchChange"
                       @refresh-change="refresh"
            >
                <template v-slot:menuLeft="scope">
                    <el-button type="primary" icon="el-icon-message" size="small" plain @click="openDialog">开始绑定</el-button>
                </template>
            </avue-crud>
        </basic-container>

        <el-dialog title="开始绑定"
                   :visible.sync="dialog"
                   :modal-append-to-body="false"
                   :modal="false"
                   :destroy-on-close="true"
                   @close="dialogClosed"
                   width="20%">
<!--            <el-alert-->
<!--                    :title="messageAlert"-->
<!--                    type="info">-->
<!--            </el-alert>-->
            <div id="wx_reg" style="text-align: center;"></div>
        </el-dialog>
    </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteUserBind, ListUserBind, SaveUserBind} from "./qywxApi";
import {getBindInfo} from "@/api/justAuthConfig";

export default {
    data() {
        return {
            form: {},
            tableLoading: false,
            pageList: [],
            page: {
                //pageSizes: [10, 20, 30, 40],默认
                currentPage: 1,
                total: 0,
                pageSize: 10
            },
            optionDetail: {
                index: true,
                size: "mini",
                dialogWidth: 580,
                dialogClickModal: false,
                // searchShowBtn: false,
                searchShow: false,
                // menuWidth: 150,
                align: "center",
                // editBtnText: "修改",
                // delBtnText: '删除',
                border: true,
                column: [
                    {
                        label: 'wxId',
                        prop: "wxId",
                        search: true,
                        rules: [{required: true, trigger: blur}]
                    },
                    {
                        label: '说明',
                        prop: "description",
                        type: "textarea",
                        span: 24,
                    },
                    {
                        label: '创建时间',
                        prop: "created",
                        readonly: true,
                        addDisplay: false,
                        editDisplay: false,
                        editDisabled: true,
                        addDisabled: true,
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                    },
                    {
                        label: '更新时间',
                        prop: "updated",
                        readonly: true,
                        addDisplay: false,
                        editDisplay: false,
                        editDisabled: true,
                        addDisabled: true,
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                    },
                ]
            },
            dialog: false
        }
    },
    computed: {
        ...mapGetters(["userInfo"])
    },
    created() {
        this.refresh()
    },
    methods: {
        updateHandle(row, index, done) {
            SaveUserBind(row).then(() => {
                done();
                this.refresh();
            })
        },
        deleteHandle(row) {
            this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                DeleteUserBind({id: row.id}).then(() => {
                    this.refresh();
                })
            }).catch(() => {
                console.log('已取消删除操作')
            });
        },
        // 列表查询
        onLoad(param) {
            this.tableLoading = true
            ListUserBind(param).then(res => {
                this.tableLoading = false
                if (res.data.status) {
                    const data = res.data.data;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.pageList = data.records;
                }
            })
        },
        sizeChange(val) {
            this.page.currentPage = 1;
            this.page.pageSize = val;
            this.onLoad(this.pageParam())
        },
        currentChange(val) {
            this.page.currentPage = val;
            this.onLoad(this.pageParam())
        },
        pageParam() {
            return {
                page: this.page.currentPage,
                pageSize: this.page.pageSize
            }
        },
        searchChange(param, done) {
            let pageParam = this.pageParam()
            pageParam.param = param;
            this.onLoad(pageParam)
            done()
        },
        refresh() {
            this.onLoad(this.pageParam());
        },
        openDialog() {
            this.dialog = true;
            // GetUserBindPreBindInfo().then(res => {
            //     let resData = res.data;
            //     let data1 = resData.data;
            //     console.log(resData);
            //     new window.WwLogin({
            //         "id": "wx_reg",
            //         "appid": data1[0],
            //         "agentid": data1[1],
            //         "redirect_uri": data1[3],
            //         "state": data1[2],
            //         "self_redirect": true
            //     });
            // })

			getBindInfo({"oauthType": "WECHAT_ENTERPRISE"}).then(res => {
				let resData = res.data;
				if(resData.code === '00000'){
					let data1 = resData.info;
					new window.WwLogin({
						"id": "wx_reg",
						"appid": data1.appId,
						"agentid": data1.agentId,
						"redirect_uri": data1.redirectUri,
						"state": data1.humancode,
						"self_redirect": true
					});
				} else {
					this.$message.error(resData.info);
					this.dialog = false;
				}
			})
        },
        dialogClosed() {
            // console.log("dialogClosed");
            this.refresh();
        }
    }
}
</script>
