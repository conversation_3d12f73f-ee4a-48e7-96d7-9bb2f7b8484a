<template>
    <div>
        <basic-container class="basic-container">
            <h3 class="pageTitle">认证管理</h3>
            <avue-crud :table-loading="tableLoading"
                       :option="optionDetail"
                       :data="dataList"
                       :page.sync="page"
                       @rowAdd="rowAdd"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       @row-save="editHandle"
                       @row-update="editHandle"
                       @row-del="deleteHandle"
                       @search-change="searchChange"
                       @refresh-change="refresh"></avue-crud>
        </basic-container>
    </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteClient, EditClient, GetClientList} from "@/api/client";

export default {
        data() {
            return {
                tableLoading: false,
                dataList: [],
                page: {
                    //pageSizes: [10, 20, 30, 40],默认
                    currentPage: 1,
                    total: 0,
                    pageSize: 10,
                },
            };
        },
        computed: {
            ...mapGetters(["userInfo"]),
            optionDetail() {
                return {
                    index: true,
                    labelWidth: 130,
                    menuWidth: 150,
                    editBtnText: "修改",
                    delBtnText: "删除",
                    border: true,
                    align: "center",
                    dialogWidth: 580,
                    dialogClickModal: false,
                    searchBtn: true,
                    size: "mini",
                    searchShow: false,
                    refreshBtn: false,
                    columnBtn: false,
                    column: [
                        {
                            prop: "id",
                            hide: true,
                            editDisplay: false,
                            addDisplay: false,
                        },
                        {
                            label: "应用名称",
                            prop: "clientName",
                            span: 24,
                            width: 200,
                            rules: [
                                {required: true, message: "请输入应用名称", trigger: blur},
                            ],
                        },
                        {
                            label: "应用首页",
                            span: 24,
                            prop: "clientIndex",
                            rules: [
                                {required: true, message: "请输入应用首页", trigger: blur},
                            ],
                        },
                        {
                          label: "clientId",
                          prop: "clientId",
                          span: 24,
                          editDisplay: false,
                        },
                        {
                            label: "clientSecret",
                            prop: "clientSecret",
                            editDisplay: false,
                            addDisplay: false,
                        },
                        {
                            label: "回调地址",
                            prop: "webServerRedirectUri",
                            span: 24,
                            rules: [
                                {required: true, message: "请输入回调地址", trigger: blur},
                            ],
                        },
                        {
                            label: "认证类型",
                            prop: "authType",
                            span: 24,
                            type: "radio",
                            dicData: [
                                {
                                    label: "OAuth2",
                                    value: "OAuth2"
                                },
                                {
                                    label: "ExMail",
                                    value: "ExMail"
                                },
                                {
                                    label: "CAS",
                                    value: "CAS"
                                }
                            ],
                            rules: [{required: true, message: "认证类型不能为空"}]
                        },
                        {
                            label: "应用简介",
                            prop: "remark",
                            type: "textarea",
                            span: 24,
                            hide: true,
                            rules: [
                                {required: true, message: "请输入应用简介", trigger: blur},
                            ],
                        },
                    ],
                };
            },
        },
        created() {
            this.onLoad(this.pageParam());
        },
        methods: {
            // 编辑
            editHandle(row, done, loading) {
                EditClient(row).then((res) => {
                    if (res.data.code === "00000") {
                        this.$message({type: "success", message: "操作成功"});
                    }
                    this.onLoad(this.pageParam());
                    done();
                }).catch((res) => {
                    loading();
                    this.$message.error(res.info);
                });
            },
            // 删除
            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    DeleteClient({id: row.id}).then((res) => {
                        if (res.data.code === "00000") {
                            this.$message({type: "success", message: "删除成功"});
                            this.onLoad(this.pageParam());
                        }
                    });
                }).catch(() => {
                    // this.$message({ type: "info", message: "已取消删除" });
                    console.log("已取消删除操作");
                });
            },
            // 列表查询
            onLoad(param) {
                this.tableLoading = true;
                GetClientList(param).then((res) => {
                    this.tableLoading = false;
                    if (res.data.code !== "00000") {
                        this.$message({message: res.data.info, type: "error"});
                    } else {
                        const data = res.data.info;
                        this.page.currentPage = data.current;
                        this.page.total = data.total;
                        this.page.pageSize = data.size;
                        this.dataList = data.records;
                    }
                });
            },
            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.onLoad(this.pageParam());
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.onLoad(this.pageParam());
            },
            rowAdd() {
            },
            generateRandom(min, max) {
                var returnStr = "",
                    range = max ? Math.round(Math.random() * (max - min)) + min : min,
                    arr = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o",
                        "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O",
                        "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",];
                for (var i = 0; i < range; i++) {
                    var index = Math.round(Math.random() * (arr.length - 1));
                    returnStr += arr[index];
                }
                return returnStr;
            },
            pageParam() {
                return {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {},
                };
            },
            searchChange(param, done) {
                var pageParam = this.pageParam();
                pageParam.queryParam = param;
                this.onLoad(pageParam);
                done();
            },
            refresh() {
                this.onLoad(this.pageParam());
            },
        },
    };
</script>
<style scoped>
    .basic-container >>> .el-card .avue-form__menu {
        width: 25%;
        text-align: left;
        padding-top: 0;
    }

    .avue-view {
        height: 100%;
    }
</style>
