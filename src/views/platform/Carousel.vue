<template>
    <basic-container>
        <div class="carousel-wrapper">
            <avue-crud ref="crud"
                       :option="option"
                       :data="data"
                       :page.sync="page"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       :upload-after="uploadAfter"
                       @row-save="saveHandle"
                       @row-update="updateHandle"
                       @row-del="delHandle"
                       @refresh-change="refreshChange"></avue-crud>
        </div>
    </basic-container>
</template>

<script>
    import {
        GetRoleInfo,
        EditCarousel,
        GetCarousel,
        RemoveCarousel
    } from "@/api/settings";

    export default {
        inject: ["reload"],
        data() {
            return {
                uploadObj: {},
                page: {
                    //pageSizes: [10, 20, 30, 40],默认
                    currentPage: 1,
                    total: 0,
                    pageSize: 10
                },
                data: [],
                option: {
                    size: "mini",
                    index: true,
                    align: "center",
                    menuAlign: "center",
                    border: true,
                    editBtnText: "修改",
                    delBtnText: '删除',
                    menuWidth: 150,
                    dialogClickModal: false,
                    column: [
                        {
                            label: "标题",
                            prop: "title",
                            span: 12,
                            rules: [{required: true, message: "标题不能为空"}]
                        },
                        {
                            label: "图片链接",
                            prop: "detail",
                            span: 12,
                            rules: [{required: true, message: "图片链接不能为空"}]
                        },
                        {
                            label: "排序",
                            prop: "sort",
                            type: "number",
                            rules: [{required: true, message: "排序不能为空"}]
                        },
                        {
                            label: "状态",
                            prop: "status",
                            type: "select",
                            dicData: [
                                {
                                    label: "启用",
                                    value: "启用"
                                },
                                {
                                    label: "停用",
                                    value: "停用"
                                }
                            ],
                            rules: [{required: true, message: "状态不能为空"}]
                        },

                    ]
                }
            };
        },
        created() {
            this.getRoleInfo();
            this.getQuery();
        },
        methods: {
            getRoleInfo() {
                GetRoleInfo().then(res => {
                    this.option.column.push({
                        label: "授权角色",
                        prop: "roleId",
                        type: "select",
                        placeholder: "请选择 授权角色-可多选",
                        span: 24,
                        drag: true,
                        multiple: true,
                        dicData: res.data.info,
                        props: {
                            label: "rolename",
                            value: "id"
                        }
                        // rules: [{ required: true, message: "授权角色不能为空" }]
                    }, {
                        label: "轮播图",
                        prop: "img",
                        type: "upload",
                        listType: "picture-img",
                        span: 24,
                        propsHttp: {
                            // home: "http://**************:8181/",
                            // home: "http://*************/",
                            home: window.location.origin,
                            res: "info"
                        },
                        // tip: "只能上传jpg/png用户头像，且不超过500kb",
                        action: "/file/upload",
                        rules: [{required: true, message: "轮播图不能为空"}]
                    });
                });
            },

            getQuery() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                };
                GetCarousel(context).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                    this.data.forEach(item => {
                        item.roleId = JSON.parse(item.roleId);
                        item.uploadObj = JSON.parse(item.img);
                        item.img = JSON.parse(item.img)[0].url;
                    });
                    // console.log(this.data);
                });
            },

            saveHandle(row, done) {
                let arr = [];
                arr.push(this.uploadObj);
                row.img = arr;
                console.log("save", row);
                EditCarousel(row).then(res => {
                    console.log(res);
                    if (res.data.code == "00000") {
                        done();
                        this.$message({
                            type: "success",
                            message: "新增成功"
                        });
                        this.uploadObj = {};
                        this.reload();
                    } else {
                        this.$message.error("新增失败");
                        this.uploadObj = {};
                        this.reload();
                    }
                });
            },

            updateHandle(row, index, done, loading) {
                console.log(row);
                if (JSON.stringify(this.uploadObj) == "{}") {
                    // console.log(this.uploadObj + "图片没修改");
                    row.img = row.uploadObj;
                } else {
                    // console.log(this.uploadObj + "图片已修改");
                    row.img = [];
                    row.img.push(this.uploadObj);
                }

                // console.log(row);

                EditCarousel(row).then(res => {
                    // console.log(res);
                    if (res.data.code == "00000") {
                        done();
                        this.$message.success("修改成功！");
                        this.uploadObj = {};
                        this.reload();
                    } else {
                        loading();
                        this.$message.error("修改失败");
                        this.uploadObj = {};
                        this.reload();
                    }
                });
            },

            delHandle(data) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    RemoveCarousel(data.id).then(res => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!"
                            });
                            this.reload();
                        } else {
                            this.$message.error("删除失败");
                            this.reload();
                        }
                    });
                }).catch(() => {
                });
            },

            uploadAfter(res, done) {
                console.log("uploadObj", res);
                if (res) {
                    this.uploadObj = res;
                    done();
                    this.$message.success("上传成功");
                }
            },

            // uploadPreview(file, column) {
            //   console.log(">>", file);
            //   console.log(">>", column);
            // },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getQuery();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getQuery();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.reload();
            }
        }
    };
</script>

<style scoped>
</style>
