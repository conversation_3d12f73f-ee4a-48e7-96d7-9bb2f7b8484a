<template>
    <basic-container>
        <avue-crud v-model="obj"
                   :data="roleDetail"
                   :option="optionDetail1"
                   :page.sync="page"
                   :upload-after="uploadAfter"
                   :upload-preview="uploadPreview"
                   :upload-delete="uploadDelete"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @row-del="deleteItemHandle"
                   @row-save="addUserHandle"
                   @row-update="updateHandle"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    import {GetRoleInfo} from "@/api/settings";
    import {GetOrg} from "@/api/basetable";
    import {
        EditNews,
        getNewsQuery,
        DelNews,
        GetDataCategory
    } from "@/api/platform";

    import Editor from "@/components/Editor";

    export default {
        inject: ["reload"],
        data() {
            return {
                // query
                obj: {},

                attachObj: [],
                roleList: [],
                roleDetail: [],
                // 组织机构
                // orgData:
                // group: [],
                optionDetail1: {
                    size: "mini",
                    index: true,
                    editBtnText: "修改",
                    delBtnText: '删除',
                    border: true,
                    labelWidth: 120,
                    menuWidth: 150,
                    dialogClickModal: false,
                    column: [
                        {
                            label: "新闻标题",
                            prop: "title",
                            span: 12,
                            rules: [{required: true, message: "标题不能为空"}]
                            // addDisplay: false,
                            // editDisplay: false
                        },
                        {
                            label: "新闻链接",
                            prop: "detailLinks",
                            span: 12
                            // rules: [{ required: true, message: "新闻链接不能为空" }]
                            // addDisplay: false,
                            // editDisplay: false
                        },

                        // {
                        //   label: "新闻内容编辑",
                        //   component: tinymce,
                        //   span: 24,
                        //   params: {
                        //     // html: "<Tinymce></Tinymce>"
                        //   }
                        // },
                        // {
                        //   label: "新闻内容编辑",
                        //   span: 24,
                        //   prop: "content",
                        //   rules: [{ required: true, message: "新闻内容不能为空" }],
                        //   component: "AvueUeditor",
                        //   options: {
                        //     //普通图片上传
                        //     // action: "https://avuejs.com/upload",
                        //     action: "file/upload",
                        //     props: {
                        //       res: "info"
                        //     }
                        //   }
                        // },
                        // {
                        //   type: "ueditor",
                        //   component: "avue-ueditor",
                        //   label: "富文本",
                        //   span: 24,
                        //   display: true,
                        //   options: {
                        //     action: "",
                        //     oss: "",
                        //     props: {},
                        //     ali: {},
                        //     qiniu: {}
                        //   },
                        //   size: "small",
                        //   prop: "1592056150471_11252"
                        // },
                        {
                            label: "新闻内容",
                            span: 24,
                            prop: "content",
                            component: Editor,
                            hide: true,
                            rules: [{required: true, message: "新闻内容不能为空"}]
                        },
                        {
                            label: "上传附件",
                            prop: "attachment",
                            type: "upload",
                            span: 24,
                            // dataType: "array",
                            drag: true,
                            hide: true,
                            propsHttp: {
                                res: "info"
                                // name: "name"
                            },
                            // tip: "只能上传jpg/png文件，且不超过500kb",
                            action: "file/upload",
                            props: {
                                label: "name",
                                value: "url"
                            }
                        }
                    ]
                    // group: [
                    //   // 富文本编辑
                    //   {
                    //     // labelWidth: 0,
                    //     column: []
                    //   },

                    //   // 上传附件
                    //   {
                    //     // label: "上传附件",
                    //     column: [

                    //     ]
                    //   }
                    // ]
                },
                page: {
                    //pageSizes: [10, 20, 30, 40],默认
                    currentPage: 1,
                    total: 0,
                    pageSize: 10
                }
            };
        },
        created() {
            this.getSelectList();
            this.onLoad();
        },
        methods: {
            // query
            getSelectList() {
                GetDataCategory({model: "xinwen"}).then(res => {
                    console.log(res);

                    if (res.data.code == "00000") {
                        this.optionDetail1.column.splice(2, 0, {
                            label: "选项卡分类",
                            rules: [{required: true, message: "请选择新闻分类"}],
                            prop: "categoryId",
                            span: 12,
                            type: "select",
                            dicData: res.data.info,
                            props: {
                                label: "name",
                                value: "id"
                            }
                        });

                        GetRoleInfo().then(res => {
                            if (res.data.code == "00000") {
                                this.optionDetail1.column.splice(3, 0, {
                                    label: "可见角色",
                                    rules: [
                                        // { required: true, message: "请选择可见新闻的角色 - 可多选" }
                                    ],
                                    prop: "roleIds",
                                    type: "select",
                                    drag: true,
                                    span: 12,
                                    multiple: true,
                                    dicData: res.data.info,
                                    props: {
                                        label: "rolename",
                                        value: "id"
                                    }
                                });

                                GetOrg().then(res => {
                                    if (res.data.code == "00000") {
                                        this.optionDetail1.column.splice(4, 0, {
                                            span: 24,
                                            label: "可见组织机构",
                                            // rules: [
                                            //   {
                                            //     required: true,
                                            //     message: "请选择可见新闻的组织机构 - 可多选"
                                            //   }
                                            // ],
                                            prop: "orgIds",
                                            type: "tree",
                                            multiple: true,
                                            dicData: res.data.info,
                                            props: {
                                                value: "id"
                                            }
                                        });
                                    } else {
                                        this.$message.error("服务异常");
                                    }
                                });
                            } else {
                                this.$message.error("服务异常");
                            }
                        });
                    } else {
                        this.$message.error("服务异常");
                    }
                });
            },

            onLoad() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    // pageSize: 1,
                    queryParam: {}
                };
                getNewsQuery(context).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.roleDetail = data.records;
                    // console.log("<<", this.roleDetail);
                    // this.roleDetail.forEach(item => {
                    //   item.attachment = item.attachment;
                    // });
                    // console.log(",,", this.roleDetail);
                });
            },
            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.onLoad();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.onLoad();
                // this.$message.success("页码" + val);
            },

            // 刷新 - 1
            refreshChange() {
                this.reload();
            },

            uploadAfter(res, done) {
                this.attachObj.push(res);
                // console.log("up-", res);
                done();
                this.$message.success("上传成功！");
            },

            uploadPreview(file, column) {
                console.log(file,column);
            },

            uploadDelete(column, file) {
                console.log(file);

                console.log(column);
            },

            // 添加
            addUserHandle(row, done, loading) {
                // console.log(row);
                let orgIds = row.orgIds;
                let roleIds = row.roleIds;
                delete row.orgIds;
                delete row.roleIds;
                delete row.attachment;
                const context = {
                    ...row,
                    orgIds,
                    roleIds,
                    attachment: this.attachObj
                };
                EditNews(context).then(res => {
                    if (res.data.code == "00000") {
                        // console.log(res);
                        this.$message({
                            type: "success",
                            message: "添加成功!"
                        });
                        this.attachObj = [];
                        this.reload();
                    } else {
                        this.$message.error("添加失败");
                        loading();
                        this.attachObj = [];
                        this.reload();
                    }
                });
            },

            // 修改
            updateHandle(row, index, done, loading) {
                row.attachment.forEach((obj, index) => {
                    if (Object.keys(obj).length != 4) {
                        row.attachment.splice(index, 1);
                    }
                });
                row.attachment.push(...this.attachObj);

                EditNews(row).then(res => {
                    // console.log(res);
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!"
                        });
                        this.reload();
                    } else {
                        this.$message.error("修改失败");
                        loading();
                        this.reload();
                    }
                });
            },

            // 删除
            deleteItemHandle(row) {
                // console.log(row);

                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    DelNews(row.id).then(res => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!"
                            });
                            this.reload();
                        } else {
                            this.$message.error("删除失败");
                            this.reload();
                        }
                    });
                }).catch(() => {
                });
            }
        }
    };
</script>

<style scoped>
    .user-wrapper {
        padding: 10px;
        padding-top: 0;
    }
</style>
