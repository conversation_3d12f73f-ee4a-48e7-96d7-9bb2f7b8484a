<template>
    <basic-container>
        <avue-crud
                :data="data"
                :option="option"
                :page.sync="page"
                @row-save="saveHandle"
                @row-update="updateHandle"
                @row-del="deleteHandle"
                @size-change="sizeChange"
                @current-change="currentChange"
                @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    import {Query, Edit} from "@/api/basetable";
    import {GetRoleInfo} from "@/api/settings";
    import {GetOrg} from "@/api/basetable";
    import {DelMessage} from "@/api/home";
    import {GetDataCategory} from "@/api/platform";

    export default {
        inject: ["reload"],
        data() {
            return {
                type: this.$route.path.split("/")[2],
                page: {
                    currentPage: 1,
                    total: 1,
                    pageSize: 10
                },
                obj: {},
                data: [],
                option: {}
            };
        },
        created() {
            this.getType();
            this.getInfo();
        },
        methods: {
            getType() {
                if (this.type == "sytSysMsg") {
                    this.option = {
                        size: "mini",
                        labelWidth: 120,
                        index: true,
                        delBtnText: '删除',
                        border: true,
                        menuWidth: 150,
                        align: "center",
                        menuAlign: "center",
                        dialogClickModal: false,
                        // menuType: "button",
                        editBtnText: "修改",
                        column: [
                            {
                                label: "分类",
                                prop: "categoryName",
                                width: "160",
                                addDisplay: false,
                                editDisplay: false
                            },
                            {
                                label: "选项卡分类",
                                prop: "categoryId",
                                span: 12,
                                hide: true,
                                type: "select",
                                dicData: [],
                                props: {
                                    label: "name",
                                    value: "id"
                                },
                                rules: [{required: true, message: "请选择分类"}]
                            },
                            {
                                label: "消息类型",
                                prop: "type",
                                width: "160",
                                align: "left"
                                // rules: [{ required: true, message: "消息类型不能为空" }]
                                // type: "select",
                                // hide: true,
                                // dicData: [
                                //   { label: "轮播图", value: "lunbotu" },
                                //   { label: "新闻列表", value: "xinwen" },
                                //   { label: "消息任务", value: "xiaoxirenwu" },
                                //   { label: "我的数据", value: "wodeshuju" },
                                //   { label: "集成系统", value: "jichengxitong" },
                                //   { label: "我的服务", value: "wodefuwu" }
                                // ]
                            },
                            {
                                label: "用户名",
                                prop: "username",
                                hide: true,
                                span: 12
                            },
                            {
                                label: "创建时间",
                                prop: "createtimeStr",
                                width: "160",
                                addDisplay: false,
                                editDisplay: false
                            },
                            {
                                label: "状态",
                                prop: "status",
                                width: "120",
                                addDisplay: false,
                                editDisplay: false
                            },
                            {
                                label: "消息/待办内容",
                                prop: "content",
                                type: "textarea",
                                overHidden: true,
                                // hide: true,
                                align: "left",
                                span: 24,
                                rules: [{required: true, message: "消息内容不能为空"}]
                            },
                            // {
                            //   label: "启用状态",
                            //   prop: "status",
                            //   type: "switch",
                            //   dicData: [
                            //     { value: "禁用", label: "禁用" },
                            //     { value: "启用", label: "启用" }
                            //   ],
                            //   rules: [{ required: true, message: "启用状态不能为空" }],
                            //   value: "启用"
                            // },
                            // {
                            //   label: "排序",
                            //   prop: "sort",
                            //   type: "number",
                            //   rules: [{ required: true, message: "排序不能为空" }]
                            // },
                        ]
                    };
                }
                this.getSelectList();
            },

            getSelectList() {
                GetDataCategory({model: "xiaoxirenwu"}).then(res => {
                    console.log(res);

                    if (res.data.code == "00000") {
                        this.option.column[1].dicData = res.data.info;

                        GetRoleInfo().then(res => {
                            if (res.data.code == "00000") {
                                this.option.column.splice(6, 0, {
                                    label: "可见角色",
                                    // rules: [
                                    //   { required: true, message: "请选择可见角色 - 可多选" }
                                    // ],
                                    prop: "roleids",
                                    type: "select",
                                    drag: true,
                                    span: 12,
                                    multiple: true,
                                    dicData: res.data.info,
                                    props: {
                                        label: "rolename",
                                        value: "id"
                                    }
                                });

                                GetOrg().then(res => {
                                    if (res.data.code == "00000") {
                                        this.option.column.splice(7, 0, {
                                            span: 24,
                                            label: "可见组织机构",
                                            // rules: [
                                            //   {
                                            //     required: true,
                                            //     message: "请选择可见组织机构 - 可多选"
                                            //   }
                                            // ],
                                            prop: "orgids",
                                            type: "tree",
                                            multiple: true,
                                            dicData: res.data.info,
                                            props: {
                                                value: "id"
                                            }
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            },

            getInfo() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                };
                Query(context, this.type).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                });
            },

            saveHandle(row, done, loading) {
                // console.log(row);

                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        done();
                        this.$message.success("新增成功！");
                        this.reload();
                    } else {
                        this.$message.error("新增失败");
                        loading();
                    }
                });
            },

            updateHandle(row, index, done, loading) {
                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!"
                        });
                        done();
                        this.reload();
                    } else {
                        this.$message.error("修改失败");
                        loading();
                    }
                });
            },

            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        DelMessage([row.id]).then(res => {
                            if (res.data.code == "00000") {
                                this.$message({
                                    type: "success",
                                    message: "删除成功!"
                                });
                                this.reload();
                            } else {
                                this.$message.error("删除失败");
                            }
                        });
                    })
                    .catch(() => {
                    });
            },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getInfo();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getInfo();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.reload();
            }
        }
    };
</script>

<style scoped>
</style>
