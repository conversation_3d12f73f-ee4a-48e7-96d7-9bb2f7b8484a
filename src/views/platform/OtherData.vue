<template>
    <basic-container>
        <avue-crud v-model="obj"
                   :data="roleDetail"
                   :option="optionDetail1"
                   :page.sync="page"
                   :upload-after="uploadAfter"
                   :upload-preview="uploadPreview"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @row-del="deleteItemHandle"
                   @row-save="addUserHandle"
                   @row-update="updateHandle"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    // import { GetRoleInfo } from "@/api/settings";
    // import { GetOrg } from "@/api/basetable";
    import {
        EditMyData,
        getMyData,
        DelMyData,
        GetDataCategory
    } from "@/api/platform";

    export default {
        inject: ["reload"],
        data() {
            return {
                // query
                obj: {},
                icon: "",
                attachObj: [],
                roleList: [],
                roleDetail: [],
                // 组织机构
                // orgData:
                // group: [],
                optionDetail1: {
                    size: "mini",
                    index: true,
                    dialogWidth: 580,
                    editBtnText: "修改",
                    delBtnText: '删除',
                    border: true,
                    align: "center",
                    labelWidth: 120,
                    menuWidth: 150,
                    column: [
                        {
                            label: "标题",
                            prop: "title",
                            span: 24,
                            rules: [{required: true, message: "标题不能为空"}]
                        },
                        {
                            label: "信息内容",
                            prop: "content",
                            span: 24,
                            rules: [{required: true, message: "信息内容不能为空"}]
                        },
                        {
                            label: "详情链接",
                            prop: "detailLinks",
                            span: 24,
                            rules: [{required: true, message: "详情链接不能为空"}]
                        }
                        // {
                        //   label: "图标",
                        //   prop: "icon",
                        //   type: "upload",
                        //   listType: "picture-img",
                        //   span: 24,
                        //   propsHttp: {
                        //     // home: "http://**************:8181/",
                        //     // home: "http://*************/",
                        //     res: "info"
                        //   },
                        //   // tip: "只能上传jpg/png用户头像，且不超过500kb",
                        //   action: "/file/upload"
                        // }
                    ]
                },
                page: {
                    //pageSizes: [10, 20, 30, 40],默认
                    currentPage: 1,
                    total: 0,
                    pageSize: 10
                }
            };
        },
        created() {
            this.getSelectList();
            this.onLoad();
        },
        methods: {
            // query
            getSelectList() {
                GetDataCategory({model: "wodeshuju"}).then(res => {
                    console.log(res);

                    if (res.data.code == "00000") {
                        this.optionDetail1.column.splice(1, 0, {
                            label: "信息分类",
                            rules: [{required: true, message: "请选择信息分类"}],
                            prop: "categoryId",
                            span: 24,
                            type: "select",
                            dicData: res.data.info,
                            props: {
                                label: "name",
                                value: "id"
                            }
                        });

                        // GetRoleInfo().then(res => {
                        //   if (res.data.code == "00000") {
                        //     this.optionDetail1.column.splice(4, 0, {
                        //       label: "可见角色",
                        //       rules: [
                        //         // { required: true, message: "请选择可见新闻的角色 - 可多选" }
                        //       ],
                        //       prop: "roleIds",
                        //       type: "select",
                        //       drag: true,
                        //       span: 24,
                        //       multiple: true,
                        //       dicData: res.data.info,
                        //       props: {
                        //         label: "rolename",
                        //         value: "id"
                        //       }
                        //     });

                        //     GetOrg().then(res => {
                        //       if (res.data.code == "00000") {
                        //         this.optionDetail1.column.splice(5, 0, {
                        //           span: 24,
                        //           label: "可见组织机构",
                        //           // rules: [
                        //           //   {
                        //           //     required: true,
                        //           //     message: "请选择可见新闻的组织机构 - 可多选"
                        //           //   }
                        //           // ],
                        //           prop: "orgIds",
                        //           type: "tree",
                        //           multiple: true,
                        //           dicData: res.data.info,
                        //           props: {
                        //             value: "id"
                        //           }
                        //         });
                        //       } else {
                        //         this.$message.error("服务异常");
                        //       }
                        //     });
                        //   } else {
                        //     this.$message.error("服务异常");
                        //   }
                        // });
                    } else {
                        this.$message.error("服务异常");
                    }
                });
            },

            onLoad() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    // pageSize: 1,
                    queryParam: {}
                };
                getMyData(context).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.roleDetail = data.records;
                    // console.log("<<", this.roleDetail);
                    // this.roleDetail.forEach(item => {
                    //   item.icon = item.icon[0].url;
                    // });
                    // console.log(",,", this.roleDetail);
                });
            },
            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.onLoad();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.onLoad();
                // this.$message.success("页码" + val);
            },

            // 刷新 - 1
            refreshChange() {
                this.reload();
            },

            uploadAfter(res, done) {
                this.icon = res.url;
                // console.log("up-", res);
                console.log(this.icon);

                done();
                this.$message.success("上传成功！");
            },

            uploadPreview(file, column) {
                console.log(file, column);
            },

            // uploadDelete(column, file) {
            //   console.log(file);

            //   console.log(column);
            // },

            // 添加
            addUserHandle(row, done, loading) {
                // console.log(row);
                // let orgIds = row.orgIds;
                // let roleIds = row.roleIds;
                // delete row.orgIds;
                // delete row.roleIds;
                // // delete row.icon;
                // const context = {
                //   ...row,
                //   orgIds,
                //   roleIds,
                //   icon: this.icon
                // };
                // console.log(context);
                row.icon = this.icon;
                // console.log(row);
                // return;

                EditMyData(row).then(res => {
                    if (res.data.code == "00000") {
                        // console.log(res);
                        this.$message({
                            type: "success",
                            message: "添加成功!"
                        });
                        this.icon = "";
                        this.reload();
                    } else {
                        this.$message.error("添加失败");
                        loading();
                        this.icon = "";
                        this.reload();
                    }
                });

                // console.log(context);
            },

            // 修改
            updateHandle(row, index, done, loading) {
                // row.icon.forEach((obj, index) => {
                //   if (Object.keys(obj).length != 4) {
                //     row.icon.splice(index, 1);
                //   }
                // });
                // row.icon.push(...this.attachObj);
                if (this.icon != "") {
                    row.icon = this.icon;
                }

                EditMyData(row).then(res => {
                    // console.log(res);
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!"
                        });
                        this.icon = "";
                        this.reload();
                    } else {
                        this.$message.error("修改失败");
                        loading();
                        this.reload();
                    }
                });
            },

            // 删除
            deleteItemHandle(row) {
                // console.log(row);

                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    DelMyData(row.id).then(res => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!"
                            });
                            this.reload();
                        } else {
                            this.$message.error("删除失败");
                            this.reload();
                        }
                    });
                }).catch(() => {
                });
            }
        }
    };
</script>

<style scoped>
    .user-wrapper {
        padding: 10px;
        padding-top: 0;
    }
</style>
