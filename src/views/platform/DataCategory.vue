<template>
    <basic-container>
        <avue-crud v-model="obj"
                   :data="data"
                   :option="option"
                   :page.sync="page"
                   @row-save="saveHandle"
                   @row-update="updateHandle"
                   @row-del="deleteHandle"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    import {Query, Edit, Delete} from "@/api/basetable";
    import {GetDesktop,} from "@/api/platform";
    // GetDesktopBlock
    export default {
        inject: ["reload"],
        data() {
            return {
                type: this.$route.path.split("/")[2],
                page: {
                    currentPage: 1,
                    total: 1,
                    pageSize: 10
                },
                obj: {},
                data: [],
                option: {
                    index: true,
                    size: "mini",
                    border: true,
                    align: "center",
                    menuAlign: "center",
                    editBtnText: "修改",
                    delBtnText: '删除',
                    menuWidth: 150,
                    dialogClickModal: false,
                    column: [
                        {
                            label: "名称",
                            prop: "name",
                            rules: [{required: true, message: "名称不能为空"}]
                        },

                        // {
                        //   label: "所属板块",
                        //   prop: "blockName",
                        //   addDisplay: false,
                        //   editDisplay: false
                        // },

                        {
                            label: "所属桌面",
                            prop: "desktopName",
                            addDisplay: false,
                            editDisplay: false
                        },
                        // {
                        //   label: "图标",
                        //   prop: "icon",
                        //   hide: true
                        // },

                        {
                            label: "所属类型",
                            prop: "model",
                            type: "select",
                            dicData: [
                                {label: "新闻公告", value: "xinwen"},
                                {label: "消息任务", value: "xiaoxirenwu"},
                                {label: "我的数据", value: "wodeshuju"},
                                {label: "我的服务", value: "wodefuwu"}
                            ]
                        },
                        {
                            label: "数据类型",
                            prop: "type",
                            type: "select",
                            dicData: [
                                {label: "最新", value: "new"},
                                {label: "收藏", value: "collect"},
                                {label: "推荐", value: "recommend"},
                                {label: "常用", value: "common"}
                            ]
                        },
                        {
                            label: "是否显示",
                            prop: "display",
                            type: "radio",
                            width: 150,
                            dicData: [
                                {label: "是", value: "是"},
                                {label: "否", value: "否"}
                            ],
                            rules: [
                                {required: true, message: "是否显示不能为空", trigger: "blur"}
                            ]
                        },
                        {
                            label: "排序",
                            prop: "sort",
                            type: "number",
                            width: 150
                        },
                        {
                            label: "备注",
                            prop: "remark",
                            type: "textarea",
                            span: 24,
                            maxlength: 150,
                            showWordLimit: true,
                            hide: true
                        }
                    ]
                }
            };
        },
        created() {
            // this.getType();
            this.getSelect();
            this.getInfo();
        },
        methods: {
            getSelect() {
                GetDesktop().then(res => {
                    this.option.column.splice(2, 0, {
                        label: "所属桌面",
                        prop: "desktopId",
                        type: "select",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        dicData: res.data.info,
                        change: () => {
                            this.$set(this.obj, "blockId", "");
                        }
                    });
                });
            },

            getInfo() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                };
                Query(context, this.type).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                });
            },

            saveHandle(row, done, loading) {
                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "新增成功!"
                        });
                        done();
                        this.getInfo();
                        // this.reload();
                    } else {
                        this.$message.error("新增成功");
                        loading();
                    }
                });
            },

            updateHandle(row, index, done, loading) {
                Edit(row, this.type).then(res => {
                    if (res.data.code == "00000") {
                        this.$message({
                            type: "success",
                            message: "修改成功!"
                        });
                        done();
                        // this.reload();
                        this.getInfo();
                    } else {
                        this.$$message.error("修改失败");
                        loading();
                    }
                });
            },

            deleteHandle(row) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        Delete(row.id, this.type).then(res => {
                            if (res.data.code == "00000") {
                                this.$message({
                                    type: "success",
                                    message: "删除成功!"
                                });
                                // this.reload();
                                this.getInfo();
                            } else {
                                this.$message.error("删除失败");
                            }
                        });
                    })
                    .catch(() => {
                    });
            },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getInfo();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getInfo();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.reload();
            }
        }
        // watch: {
        //   "obj.desktopId"(val) {
        //     // console.log(val);
        //     // this.obj.blockId = "";
        //     GetDesktopBlock(val).then(res => {
        //       console.log(res);
        //       this.option.column.splice(7, 1, {
        //         label: "所属板块",
        //         prop: "blockId",
        //         type: "select",
        //         dicData: res.data.info,
        //         props: {
        //           label: "name",
        //           value: "id"
        //         },
        //         hide: true,
        //         click: () => {
        //           if (this.obj.desktopId == "") {
        //             this.$message.warning("请先选择所属桌面");
        //           }
        //         }
        //       });

        //       // this.$set(this.option.column[3], "dicData", res.data.info);
        //     });
        //   }
        // }
    };
</script>

<style scoped>
    .w {
        padding: 10px;
        padding-top: 0;
    }
</style>
