<template>
    <basic-container>
        <avue-crud ref="crud"
                   :option="option"
                   :data="data"
                   :page.sync="page"
                   @size-change="sizeChange"
                   @current-change="currentChange"
                   :upload-after="uploadAfter"
                   @row-save="saveHandle"
                   @row-update="updateHandle"
                   @row-del="delHandle"
                   @refresh-change="refreshChange"></avue-crud>
    </basic-container>
</template>

<script>
    import {
        GetRoleInfo,
        GetAppId,
        EditSC,
        GetSC,
        RemoveSC
    } from "@/api/settings";

    import {GetDataCategory} from "@/api/platform";

    export default {
        inject: ["reload"],
        data() {
            return {
                uploadObj: {},
                page: {
                    //pageSizes: [10, 20, 30, 40],默认
                    currentPage: 1,
                    total: 0,
                    pageSize: 10
                },
                data: [],
                option: {
                    dialogClickModal: false,
                    size: "mini",
                    index: true,
                    align: "center",
                    menuAlign: "center",
                    editBtnText: "修改",
                    delBtnText: '删除',
                    menuWidth: 150,
                    border: true,
                    column: [
                        {
                            label: "服务名称",
                            prop: "name",
                            span: 12,
                            rules: [{required: true, message: "服务名称不能为空"}]
                        },

                        {
                            label: "服务链接",
                            prop: "link",
                            span: 12,
                            rules: [{required: true, message: "服务链接不能为空"}]
                        },
                        {
                            label: "是否推荐",
                            prop: "recommend",
                            type: "radio",
                            dicData: [
                                {label: "是", value: "是"},
                                {label: "否", value: "否"}
                            ],
                            rules: [{required: true, message: "是否推荐不能为空"}],
                            value: "否"
                        },
                        {
                            label: "服务简介",
                            prop: "remark",
                            type: "textarea",
                            span: 24,
                            hide: true,
                            rules: [{required: true, message: "服务简介不能为空"}]
                        },
                        {
                            label: "服务图标",
                            prop: "icon",
                            type: "upload",
                            listType: "picture-img",
                            span: 24,
                            propsHttp: {
                                // home: "http://**************:8181/",
                                // home: "http://*************/",
                                home: window.location.origin,
                                res: "info"
                            },
                            // tip: "只能上传jpg/png用户头像，且不超过500kb",
                            action: "/file/upload",
                            rules: [{required: true, message: "图标不能为空"}]
                        }
                    ]
                }
            };
        },
        created() {
            this.getSelectInfo();
            this.getQuery();
        },
        methods: {
            getSelectInfo() {
                GetDataCategory({model: "wodefuwu"}).then(res => {
                    console.log(res);
                    this.option.column.splice(2, 0, {
                        label: "服务分类",
                        prop: "categoryId",
                        type: "select",
                        placeholder: "请选择 服务分类",
                        span: 12,
                        // drag: true,
                        // multiple: true,
                        dicData: res.data.info,
                        props: {
                            label: "name",
                            value: "id"
                        },
                        rules: [{required: true, message: "服务分类不能为空"}]
                    });

                    GetRoleInfo().then(res => {
                        this.option.column.splice(3, 0, {
                            label: "授权角色",
                            prop: "roleId",
                            type: "select",
                            placeholder: "请选择 授权角色-可多选",
                            drag: true,
                            multiple: true,
                            dicData: res.data.info,
                            props: {
                                label: "rolename",
                                value: "id"
                            }
                            // rules: [{ required: true, message: "授权角色不能为空" }]
                        });

                        GetAppId().then(res => {
                            this.option.column.splice(5, 0, {
                                label: "所属应用",
                                prop: "appId",
                                type: "tree",
                                dicData: res.data.info,
                                props: {
                                    label: "clientName",
                                    value: "id"
                                }
                                // rules: [{ required: true, message: "授权角色不能为空" }]
                            });
                        });
                    });
                });
            },

            getQuery() {
                const context = {
                    page: this.page.currentPage,
                    pageSize: this.page.pageSize,
                    queryParam: {}
                };
                GetSC(context).then(res => {
                    const data = res.data.info;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.data = data.records;
                    this.data.forEach(item => {
                        // item.roleId = JSON.parse(item.roleId);
                        item.icon = item.icon ? "file/view/" + item.icon : '';
                    });
                    // console.log(this.data);
                });
            },

            saveHandle(row, done) {
                row.icon = this.uploadObj.id;
                const context = {serviceCenter: row, roleId: row.roleId};
                // console.log("保存", context);

                EditSC(context).then(res => {
                    console.log(res);
                    if (res.data.code == "00000") {
                        done();
                        this.$message({
                            type: "success",
                            message: "新增成功"
                        });
                        this.uploadObj = {};
                        this.reload();
                    } else {
                        this.$message.error("新增失败");
                        this.uploadObj = {};
                        this.reload();
                    }
                });
            },

            updateHandle(row, index, done, loading) {
                // console.log("修改", row);
                if (JSON.stringify(this.uploadObj) == "{}") {
                    // console.log(this.uploadObj + "图片没修改");
                    row.icon = row.icon.split("/")[row.icon.split("/").length - 1];
                } else {
                    // console.log(this.uploadObj + "图片已修改");
                    row.icon = this.uploadObj.id;
                }

                // console.log(row);
                const context = {serviceCenter: row, roleId: row.roleId};
                EditSC(context).then(res => {
                    console.log(res);
                    if (res.data.code == "00000") {
                        done();
                        this.$message.success("修改成功！");
                        this.uploadObj = {};
                        this.reload();
                    } else {
                        loading();
                        this.$message.error("修改失败");
                        this.uploadObj = {};
                        this.reload();
                    }
                });
            },

            delHandle(data) {
                this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(() => {
                    RemoveSC(data.id).then(res => {
                        if (res.data.code == "00000") {
                            this.$message({
                                type: "success",
                                message: "删除成功!"
                            });
                            this.reload();
                        } else {
                            this.$message.error("删除失败");
                        }
                    });
                }).catch(() => {
                });
            },

            uploadAfter(res, done) {
                // console.log("uploadObj", res);
                if (res) {
                    this.uploadObj = res;
                    done();
                    this.$message.success("上传成功");
                }
            },

            // uploadPreview(file, column) {
            //   console.log(">>", file);
            //   console.log(">>", column);
            // },

            sizeChange(val) {
                this.page.currentPage = 1;
                this.page.pageSize = val;
                this.getQuery();
                // this.$message.success("行数" + val);
            },
            currentChange(val) {
                this.page.currentPage = val;
                this.getQuery();
                // this.$message.success("页码" + val);
            },
            refreshChange() {
                this.getQuery();
            }
            // uploadPreview(file, column, done) {
            //   console.log(file, column);
            //   done(); //默认执行打开方法
            //   this.$message.success("自定义查看方法,查看控制台");
            // }
        }
    };
</script>

<style scoped>
    .carousel-wrapper {
        background-color: #fff;
        width: 98%;
        margin-left: 1%;
        padding: 10px;
    }

    /* .el-dialog >>> .avue-upload__icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        line-height: 100px!important;
        text-align: center;
    } */
</style>
