<template>
    <div>
        <basic-container class="basic-container">
            <h3 class="pageTitle">用户管理</h3>
            <avue-crud :table-loading="tableLoading"
                       :option="optionDetail"
                       :data="dataList"
                       :page.sync="page"
                       @rowAdd="rowAdd"
                       @size-change="sizeChange"
                       @current-change="currentChange"
                       @row-save="editHandle"
                       @row-update="editHandle"
                       @row-del="deleteHandle"
                       @search-change="searchChange"
                       @refresh-change="refresh"></avue-crud>
        </basic-container>
    </div>
</template>
<script>
import {mapGetters} from "vuex";
import {DeleteClient, EditClient, GetClientList} from "@/api/client";
import {deleteuser, edituser, userQueryList} from "@/api/wxgzh";

export default {
    data() {
        return {
            tableLoading: false,
            dataList: [],
            page: {
                //pageSizes: [10, 20, 30, 40],默认
                currentPage: 1,
                total: 0,
                pageSize: 10,
            },
            appId: '',
        };
    },
    computed: {
        ...mapGetters(["userInfo"]),
        optionDetail() {
            return {
                index: true,
                labelWidth: 150,
                menuWidth: 150,
                editBtnText: "修改",
                delBtnText: "删除",
                border: true,
                align: "center",
                dialogWidth: 580,
                searchLabelWidth: 150,
                dialogClickModal: false,
                searchBtn: true,
                size: "mini",
                searchShow: false,
                refreshBtn: false,
                columnBtn: false,
                column: [
                    // {
                    //     label: "应用",
                    //     prop: "app.name",
                    //     span: 24,
                    //     width: 200,
                    //
                    // },
                    {
                        label: "openId",
                        span: 24,
                        prop: "openId",
                        search: true,
                    },
                    {
                        label: "系统用户名",
                        prop: "systemUsername",
                        span: 24,
                        search: true,
                    },
                    {
                        label: "创建时间",
                        prop: "created",
                        type: "date",
                        span: 24,
                        format: "yyyy-MM-dd HH:mm:ss",
                        editDisplay: false,
                        addDisplay: false,
                        // rules: [
                        //     {required: true, message: "请输入应用简介", trigger: blur},
                        // ],
                    },
                    {
                        label: "修改时间",
                        prop: "updated",
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                        span: 24,
                        editDisplay: false,
                        addDisplay: false,
                        // rules: [
                        //     {required: true, message: "请输入应用简介", trigger: blur},
                        // ],
                    },
                ],
            };
        },
    },
    created() {
        this.appId = this.$route.query.id;
        this.onLoad(this.pageParam());
    },
    methods: {
        // 编辑
        editHandle(row, done, loading) {
            row.appId = this.appId;
            edituser(row).then((res) => {
                if (res.data.status) {
                    this.$message({type: "success", message: "操作成功"});
                }else {
                    this.$message.error("操作失败");
                }
                this.onLoad(this.pageParam());
                done();
            }).catch((res) => {
                loading();
                this.$message.error(res.info);
            });
        },
        // 删除
        deleteHandle(row) {
            this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                deleteuser({id: row.id}).then((res) => {
                    if (res.data.status) {
                        this.$message({type: "success", message: "删除成功"});
                        this.onLoad(this.pageParam());
                    }else {
                        this.$message.error("删除失败");
                    }
                });
            }).catch(() => {
                // this.$message({ type: "info", message: "已取消删除" });
                console.log("已取消删除操作");
            });
        },
        // 列表查询
        onLoad(param) {
            this.tableLoading = true;
            userQueryList(param).then((res) => {
                this.tableLoading = false;
                if (res.data.status) {
                    const data = res.data.data;
                    this.page.currentPage = data.current;
                    this.page.total = data.total;
                    this.page.pageSize = data.size;
                    this.dataList = data.records;
                } else {
                    this.$message({message: res.data.info, type: "error"});
                }
            });
        },
        sizeChange(val) {
            this.page.currentPage = 1;
            this.page.pageSize = val;
            this.onLoad(this.pageParam());
        },
        currentChange(val) {
            this.page.currentPage = val;
            this.onLoad(this.pageParam());
        },
        rowAdd() {
        },
        pageParam() {
            return {
                page: this.page.currentPage,
                pageSize: this.page.pageSize,
                param: {
                    appId: this.appId
                },
            };
        },
        searchChange(param, done) {
            var pageParam = this.pageParam();
            let newparam = {
                appId: this.appId,
                openIdLike: param.openId,
                systemUsernameLike: param.systemUsername,
            }
            pageParam.param = newparam;
            this.onLoad(pageParam);
            done();
        },
        refresh() {
            this.onLoad(this.pageParam());
        },
    },
};
</script>
<style scoped>
.basic-container >>> .el-card .avue-form__menu {
    width: 25%;
    text-align: left;
    padding-top: 0;
}

.avue-view {
    height: 100%;
}
</style>
