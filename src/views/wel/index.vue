<template>
  <div>
    <basic-container class="grBasic" v-if="gr">
      <v-user></v-user>
    </basic-container>
    <basic-container class="adminBasic" v-if="admin">
      <div class="wel__body">
        <el-row class="rowFirst">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <h3 class="data-title">数据概览</h3>
            <div class="dataContent">
              <div class="data">
                <p class="dataName">帐号总数</p>
                <p class="dataNumber" v-text="info.ZHZS||0">0</p>
              </div>
              <div class="data">
                <p class="dataName">在校人数</p>
                <p class="dataNumber" v-text="info.ZXRS||0">0</p>
              </div>
              <div class="data">
                <p class="dataName">离校人数</p>
                <p class="dataNumber" v-text="info.LXRS||0">0</p>
              </div>
              <div class="data">
                <p class="dataName">访问人数</p>
                <p class="dataNumber" v-text="info.FWRS||0"></p>
              </div>
              <div class="data">
                <p class="dataName">应用接入</p>
                <p class="dataNumber" v-text="info.YYS||0">0</p>
              </div>
              <div class="data">
                <p class="dataName">组织机构</p>
                <p class="dataNumber" v-text="info.ORGNUM||0">0</p>
              </div>
              <!--                        <div class="data">
                                          <p class="dataName">认证失败</p>
                                          <p class="dataNumber">134</p>
                                      </div>
                                      <div class="data">
                                          <p class="dataName">认证接口数</p>
                                          <p class="dataNumber">2</p>
                                      </div>-->
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <h3 class="data-title">快捷导航</h3>
            <div class="dataApp">
              <div class="app" @click="routerLink('/client/client/index')">
                <i class="el-icon-coordinate"></i>应用认证
              </div>
              <div class="app" @click="routerLink('/tongyi/user-settings/index')">
                <i class="el-icon-tickets"></i>帐号管理
              </div>
              <div class="app" @click="routerLink('/settings/org-settings/index')">
                <i class="el-icon-office-building"></i>组织机构
              </div>
              <div class="app" @click="routerLink('/settings/module-settings/index')">
                <i class="el-icon-c-scale-to-original"></i>模块管理
              </div>
              <div class="app" @click="routerLink('/settings/param-setting/index')">
                <i class="el-icon-cpu"></i>系统参数
              </div>
              <div class="app" @click="routerLink('/settings/menu-settings/index')">
                <i class="el-icon-guide"></i>菜单管理
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <h3 class="data-title">数据分析</h3>
            <!--                        <div class="pickMonth">-->
            <!--                            <el-date-picker-->
            <!--                                v-model="monthvalue"-->
            <!--                                type="month"-->
            <!--                                size="small"-->
            <!--                                placeholder="请选择月份">-->
            <!--                            </el-date-picker>-->
            <!--                        </div>-->
            <div class="chartWrap">
              <el-row>
                <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
                  <div class="longChart" id="numberData">

                  </div>
                </el-col>
                <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                  <div class="shortChart" id="numberPieData">

                  </div>
                </el-col>
              </el-row>
            </div>

          </el-col>
        </el-row>
      </div>
    </basic-container>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import echarts from 'echarts'
import {getRoleList} from "@/api/user";
import {getIndexData} from "@/api/index";
import info from "@/views/user/info"
import Cookies from 'js-cookie'

export default {
  name: "wel",
  components: {
    "v-user": info
  },
  data() {
    return {
      // monthvalue:"",
      info: {},
      days: [],
      barData: [],
      rylb: [],
      pieData: [],
      gr: false,
      admin: false,

    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.init();
    this.redirectPage();
  },
  created() {
  },
  methods: {
    init() {
      getRoleList().then((res) => {
        if (res.data.code === '00000') {
          if (res.data.info.roleKey === "SuperAdmin") {
            this.gr = false;
            this.admin = true;
            this.onLoad();
          } else {
            this.admin = false;
            this.gr = false;
          }
        }
      })
    },
    onLoad() {
      getIndexData().then((res) => {
        if (res.data.code === "00000") {
          this.info = res.data.info.sjgl;
          res.data.info.yfw.forEach(item => {
            this.days.push(item.DAY);
            this.barData.push(item.NUM);
          });
          this.drawBar();
          res.data.info.rylb.forEach(item => {
            this.rylb.push(item.NAME);
            let o = {};
            o.name = item.NAME;
            o.value = item.VALUE;
            this.pieData.push(o);
          });
          this.drawPie();
        }
      })
    },
    routerLink(link) {
      this.$router.push(link);
    },
    drawBar() {
      let resizeBar = echarts.init(document.getElementById("numberData"))
      resizeBar.setOption({
        title: {
          text: '当月访问人数',
          left: "25px",
          textStyle: {
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: ['访问人数'],
          icon: 'pin',
          bottom: "10px"
        },
        grid: {
          left: '3%',
          right: '2%',
          top: '20%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#999'
            }
          },
          data: this.days
        },
        color: ["#6395F9", "#62DAAB"],
        // dataZoom: [{
        //     show: true,
        //     realtime: true,
        //     start: 30,
        //     end: 70,
        //     height: 15,
        // }],
        yAxis: {
          type: 'value',
          splitNumber: 3,
          axisTick: { //y轴刻度线
            show: false
          },
          splitLine: {
            show: true, //网格线不显示
            lineStyle: {
              color: '#eee'
            }
          },
          axisLine: { //y轴
            show: false,
            lineStyle: {
              color: '#999'
            }
          },
        },
        series: [{
          name: '访问人数',
          markPoint: {
            data: [{
              type: 'max',
              name: '最大值'
            },
              {
                type: 'min',
                name: '最小值'
              }
            ]
          },
          data: this.barData,
          type: 'line',
          barWidth: 20,
          symbol: "none",
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: 'rgb(99,149,249)'
            }, {
              offset: 1,
              color: 'rgb(255,255,255)'
            }])
          },
        }]
      })

      window.addEventListener("resize", () => {
        resizeBar.resize();
      })
    },
    drawPie() {
      let resizePie = echarts.init(document.getElementById("numberPieData"));
      resizePie.setOption({
        title: {
          text: '人员类别占比',
          left: "25px",
          textStyle: {
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        color: ['#6395F9', '#62DAAB', '#F6C022', '#E96C5B', '#667898',
          '#2ec7c9', '#b6a2de', '#5ab1ef', '#ffb980', '#d87a80',
          '#8d98b3', '#e5cf0d', '#97b552', '#95706d', '#dc69aa',
          '#07a2a4', '#9a7fd1', '#588dd5', '#f5994e', '#c05050',
          '#59678c', '#c9ab00', '#7eb00a', '#6f5553', '#c14089'],
        legend: {
          bottom: '10px',
          orient: 'horizontal',
          icon: 'pin',
          data: this.rylb
        },
        graphic: [{ //环形图中间添加文字
          type: 'text', //通过不同top值可以设置上下显示
          left: "center",
          top: '40%',
          style: {
            text: '总数' + '\n' + this.info.ZHZS,
            textAlign: 'center',
            fill: '#6395F9', //文字的颜色
            width: 30,
            height: 30,
            fontSize: 16,
            fontFamily: "Microsoft YaHei"
          }
        }],
        series: [{
          name: '帐号数量',
          type: 'pie',
          label: {
            formatter: '{b}' + '\n' + '{c} ({d}%)',
            show: false,
          },
          center: ['50%', '45%'],
          radius: ['30%', '50%'],
          data: this.pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
      window.addEventListener("resize", () => {
        resizePie.resize();
      })
    },
    redirectPage() {
      let redirectPage = Cookies.get('redirectPage')
      console.log(redirectPage)
      if (redirectPage && redirectPage !== null && redirectPage !== '') {
        Cookies.remove('redirectPage');
        this.routerLink(redirectPage);
      }
    }

  },
};
</script>
<style scoped="scoped">
.grBasic >>> .el-card {
  background: #fff;
}

.grBasic >>> .el-card__body {
  padding: 10px 5px;
}

.adminBasic >>> .el-card {
  background: #F0F2F5;
  /*padding: 10px 10px;*/
  box-sizing: border-box;
}

.adminBasic >>> .el-card__body {
  padding: 0;
}

.el-row .el-col {
  background: #fff;
  border-radius: 3px;

}

.rowFirst .el-col {
  height: 240px;
  width: calc(50% - 5px);
  margin-bottom: 10px;
}

.rowFirst .el-col:nth-of-type(1) {
  margin-right: 10px;
}

@media screen and (max-width: 1024px) {
  .rowFirst .el-col {
    height: 240px;
    width: 100%;
    margin-bottom: 10px;
  }

  .rowFirst .el-col:nth-of-type(1) {
    margin-right: 0px;
  }
}

.chartTitle {
  border: none !important;
  position: absolute;
  left: 0;
  top: 3px;
}

.pickMonth {
  position: absolute;
  right: 10px;
  top: 7px;
  z-index: 10;
}

.chartWrap {
  width: 100%;
  padding-top: 20px;
}

.longChart, .shortChart {
  width: 100%;
  height: 350px;
}

.zanwuContent {
  width: 100%;
  height: 100%;
  padding-top: 80px;
  text-align: center;
  font-size: 12px;
  color: #ccc;
}

.zanwuContent img {
  width: 128px;
}
</style>
<style scoped="scoped" lang="scss">
.data-box {
  padding-top: 20px;
}

.avue-view {
  height: 100%;
}

.data-title {
  font-size: 16px;
  border-bottom: 1px solid #E4E7ED;
  height: 45px;
  line-height: 45px;
  margin: 0;
  padding: 0 15px;
}

.dataContent {
  padding: 0 20px;
  padding-top: 20px;
}

.data {
  width: 25%;
  display: inline-block;
  margin: 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  .dataName {
    margin: 0;
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
  }

  .dataNumber {
    font-size: 25px;
    margin: 0;
  }
}

.dataApp {
  padding: 10px;
}

.app {
  display: inline-block;
  background: #F0F2F3;
  height: 65px;
  line-height: 65px;
  width: 30.33%;
  margin: 10px 1.5%;
  text-align: center;
  font-size: 14px;
  color: #333;
  cursor: pointer;

  i {
    color: #1890ff;
    margin-right: 10px;
    font-size: 25px;
    vertical-align: sub;
  }
}

@media screen and (min-width: 320px) and (max-width: 750px) {
  .wel__info {
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 10px;
  }
  .wel__info-title {
    font-size: 18px;
  }
  .wel__info-content {
    margin-left: 20px;
  }
  .wel__info-img {
    width: 60px;
    height: 60px;
    border-radius: 30px;
  }
}
</style>
