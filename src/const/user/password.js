export default {
    emptyBtn: false,
    submitBtn: false,
    column: [{
        label: '原密码',
        span: 12,
        row: true,
        type: 'password',
        prop: 'oldpassword',
        rules: [{required: true, message: "原密码不能为空"}],
    }, {
        label: '新密码',
        span: 12,
        row: true,
        type: 'password',
        prop: 'newpassword',
        // placeholder: '请输入新密码，请使用10到16位数字加大小写字母',
        rules: [{required: true, message: "新密码不能为空"}],
    }, {
        label: '确认密码',
        span: 12,
        row: true,
        type: 'password',
        prop: 'newpasswords',
        rules: [{required: true, message: "确认密码不能为空"}],
    }],
    RE_PASS: new RegExp(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{10,16}$/),
    // RE_PASS: new RegExp(/^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{10,16}$/),
    validatePass:(rule, value, callback)=>{
        const RE_PASS = new RegExp(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{10,16}$/);
        // const RE_PASS = new RegExp(/^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{10,16}$/);
        if (value !== null&&value!==''&&!RE_PASS.test(value)) {
            callback(new Error('密码强度不符,请使用10到16位数字加大小写字母'));
        } else {
            callback();
        }
    }
}
