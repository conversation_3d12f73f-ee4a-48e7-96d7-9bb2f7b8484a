# git-sanyth-sso-view-pc

统一身份认证界面

# 目前采用的是主流的vue-cli脚手架
├──api	//ajax后台api接口
├──assets	//图片资源存
├──components //基础公共组件
├──config //项目全局变量配置文件
   ├──env.js //阿里巴巴图标库地址,项目api接口地址等
├──const	//静态数据
├──directive	//vue全局自定义指令
├──docker	//docker部署文件dockerfile
├──filters	//vue全局过滤器
├──mock	//mock.js模拟数据(项目没有与服务器交互api文件调用这里模拟数据)
├──page	//登录页和主页的一些功能核心组件
├──router	//项目的路由文件和ajax拦截器
   ├──views	//业务路由
   ├──axios.js	//ajax拦截器配置
   ├──page	//底层框架路由
   ├──avue-router	//动态路由核心源码
   ├──router.js	//你的业务路由配置文件
├──store	//vuex全局组件共享变量和方法
  ├──moudles
    ├──tags.js	//导航栏持久化
    ├──common.js	//公用数据持久化
    ├──user.js	//用户相关持久化
    ├──logs.js //错误日志持久化
    ├── admin.js //模拟系统管理的持久化
├──styles	//全局样式文件存放
    ├──animate //全局动画
            ├──vue-transition.scss 过度动画
    ├──theme //主题配置
            ├──black.scss 黑色主题
            ├──gradual.scss 渐变主题
            ├──index.scss 主题的配置
            ├──star.scss 炫彩主题
    ├──common.scss  //基础样式
    ├──media.scss //多终端适配
    ├──element-ui.scss  //覆盖ele的样式
    ├──mixin.scss  //工具类包(滚动条等)
    ├──sidebar.scss	//侧边菜单
    ├──tags.scss  //选项标签
    ├──top.scss	  //顶部
    ├──variables.scss  //全局scss变量
├──util	//全局工具包存放
   ├──admin.js //系统用具类
   ├──auth.js	    //(cookie)授权相关的工具包
   ├──store.js    //本地存储缓存的工具包
   ├──date.js  //日期工具类
   ├──util.js	//基础工具包
   ├──validate.js  //验证工具包
├──vendor	//第三方依赖包（excel生成下载等）
├──views	//业务逻辑页面存放
├──App.vue	//vue主组件入口文件
├──error.js	//全局错误日志文件配置
├──main.js	//主文件入口
├──permission.js	//全局权限配置
