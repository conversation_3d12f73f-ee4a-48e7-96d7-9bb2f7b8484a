{"name": "avue-cli", "version": "2.0.0", "private": true, "scripts": {"serve": "NODE_OPTIONS=\"--openssl-legacy-provider\" vue-cli-service serve", "build": "NODE_OPTIONS=\"--openssl-legacy-provider\" vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"axios": "^0.18.0", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "echarts": "^4.9.0", "element-ui": "^2.4.5", "js-cookie": "^2.2.0", "mockjs": "^1.0.1-beta3", "sass": "^1.32.0", "nprogress": "^0.2.0", "script-loader": "^0.7.2", "sortablejs": "^1.10.0-rc2", "vue": "^2.5.16", "vue-axios": "^2.1.2", "vue-i18n": "^8.7.0", "vue-router": "^3.0.1", "vuex": "^3.0.1", "wangeditor": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "chai": "^4.1.2", "sass-loader": "^7.0.1", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}